/**
 * Phone Validation Tests
 * Tests for TanzanianPhoneValidator to ensure only Airtel and Tigo networks are allowed
 */

// Mock fs to avoid file system dependencies
jest.mock('fs', () => ({
  readFileSync: jest.fn().mockReturnValue('mock-private-key')
}));

// Mock Redis to avoid connection issues
jest.mock('../src/helpers/connectRedis', () => ({
  setItem: jest.fn(),
  getItem: jest.fn()
}));

import { TanzanianPhoneValidator, PhoneValidator } from '../src/helpers/phoneValidation';

describe('TanzanianPhoneValidator', () => {
    describe('Valid Airtel Numbers', () => {
        const validAirtelNumbers = [
            '255681234567',
            '255682345678',
            '255689876543',
            '255691234567',
            '255692345678',
            '255699876543',
            '255781234567',
            '255782345678',
            '255789876543',
            // Test with formatting
            '255 68 123 4567',
            '255-69-123-4567',
            '255(78)123-4567'
        ];

        test.each(validAirtelNumbers)('should validate %s as valid Airtel number', (phoneNumber) => {
            const result = TanzanianPhoneValidator.validate(phoneNumber);
            expect(result.isValid).toBe(true);
            expect(result.operator).toBe('AIRTEL');
            expect(result.country).toBe('TANZANIA');
        });
    });

    describe('Valid Tigo Numbers', () => {
        const validTigoNumbers = [
            '255651234567',
            '255652345678',
            '255659876543',
            '255671234567',
            '255672345678',
            '255679876543',
            '255711234567',
            '255712345678',
            '255719876543',
            // Test with formatting
            '255 65 123 4567',
            '255-67-123-4567',
            '255(71)123-4567'
        ];

        test.each(validTigoNumbers)('should validate %s as valid Tigo number', (phoneNumber) => {
            const result = TanzanianPhoneValidator.validate(phoneNumber);
            expect(result.isValid).toBe(true);
            expect(result.operator).toBe('TIGO');
            expect(result.country).toBe('TANZANIA');
        });
    });

    describe('Invalid Networks - Should be Blocked', () => {
        describe('Vodacom Tanzania Numbers', () => {
            const vodacomNumbers = [
                '255741234567', // 255 74
                '255752345678', // 255 75
                '255769876543', // 255 76
                '255 74 123 4567',
                '255-75-123-4567',
                '255(76)123-4567'
            ];

            test.each(vodacomNumbers)('should reject Vodacom number %s', (phoneNumber) => {
                const result = TanzanianPhoneValidator.validate(phoneNumber);
                expect(result.isValid).toBe(false);
                expect(result.operator).toBeUndefined();
                expect(result.country).toBeUndefined();
            });
        });

        describe('Halotel Numbers', () => {
            const halotelNumbers = [
                '255611234567', // 255 61
                '255622345678', // 255 62
                '255 61 123 4567',
                '255-62-123-4567'
            ];

            test.each(halotelNumbers)('should reject Halotel number %s', (phoneNumber) => {
                const result = TanzanianPhoneValidator.validate(phoneNumber);
                expect(result.isValid).toBe(false);
                expect(result.operator).toBeUndefined();
            });
        });

        describe('Zantel Numbers', () => {
            const zantelNumbers = [
                '255771234567', // 255 77
                '255772345678',
                '255 77 123 4567',
                '255-77-123-4567'
            ];

            test.each(zantelNumbers)('should reject Zantel number %s', (phoneNumber) => {
                const result = TanzanianPhoneValidator.validate(phoneNumber);
                expect(result.isValid).toBe(false);
                expect(result.operator).toBeUndefined();
            });
        });

        describe('Smile Numbers', () => {
            const smileNumbers = [
                '255661234567', // 255 66
                '255662345678',
                '255 66 123 4567',
                '255-66-123-4567'
            ];

            test.each(smileNumbers)('should reject Smile number %s', (phoneNumber) => {
                const result = TanzanianPhoneValidator.validate(phoneNumber);
                expect(result.isValid).toBe(false);
                expect(result.operator).toBeUndefined();
            });
        });

        describe('TTCL Numbers', () => {
            const ttclNumbers = [
                '255731234567', // 255 73
                '255732345678',
                '255 73 123 4567',
                '255-73-123-4567'
            ];

            test.each(ttclNumbers)('should reject TTCL number %s', (phoneNumber) => {
                const result = TanzanianPhoneValidator.validate(phoneNumber);
                expect(result.isValid).toBe(false);
                expect(result.operator).toBeUndefined();
            });
        });
    });

    describe('Invalid Format Numbers', () => {
        const invalidNumbers = [
            '254681234567',    // Wrong country code (Kenya)
            '255681234',       // Too short
            '25568123456789',  // Too long (13 digits)
            '255801234567',    // Non-existent prefix (255 80)
            '255991234567',    // Non-existent prefix (255 99)
            '255501234567',    // Non-existent prefix (255 50)
            'invalid',         // Not a number
            '',                // Empty string
            '25568',           // Too short
            '255-68-',         // Incomplete with formatting
            '255abc234567',    // Contains letters
        ];

        test.each(invalidNumbers)('should reject invalid number %s', (phoneNumber) => {
            const result = TanzanianPhoneValidator.validate(phoneNumber);
            expect(result.isValid).toBe(false);
            expect(result.operator).toBeUndefined();
        });
    });

    describe('Utility Methods', () => {
        test('getSupportedPrefixes should return correct format', () => {
            const prefixes = TanzanianPhoneValidator.getSupportedPrefixes();
            expect(prefixes).toEqual({
                airtel: ["255 68x xxx xxx", "255 69x xxx xxx", "255 78x xxx xxx"],
                tigo: ["255 65x xxx xxx", "255 67x xxx xxx", "255 71x xxx xxx"]
            });
        });

        test('getErrorMessage should return standard error message', () => {
            const message = TanzanianPhoneValidator.getErrorMessage();
            expect(message).toBe("Invalid phone number. Only Airtel and Tigo networks are supported for TZS Collections.");
        });
    });
});

describe('PhoneValidator Generic Class', () => {
    test('validateTanzanian should delegate to TanzanianPhoneValidator', () => {
        const validAirtelNumber = '255681234567';
        const invalidVodacomNumber = '255741234567';

        const validResult = PhoneValidator.validateTanzanian(validAirtelNumber);
        expect(validResult.isValid).toBe(true);
        expect(validResult.operator).toBe('AIRTEL');

        const invalidResult = PhoneValidator.validateTanzanian(invalidVodacomNumber);
        expect(invalidResult.isValid).toBe(false);
    });
});

describe('Integration Test - Request Blocking Verification', () => {
    test('should demonstrate that invalid numbers would block the request flow', () => {
        // Simulate the validation logic from the transactions.ts file
        const simulateTransactionValidation = (phone: string) => {
            const phoneValidation = TanzanianPhoneValidator.validate(phone);
            
            if (!phoneValidation.isValid) {
                return {
                    status: 400,
                    message: TanzanianPhoneValidator.getErrorMessage(),
                    data: {
                        error_code: "INVALID_PHONE_NUMBER",
                        supported_prefixes: TanzanianPhoneValidator.getSupportedPrefixes()
                    },
                    requestBlocked: true
                };
            }
            
            // If validation passes, request would proceed
            return {
                status: 200,
                message: "Validation passed - request would proceed to thirdPartyHandler.handleCollection",
                requestBlocked: false
            };
        };

        // Test that valid numbers pass through
        const validAirtelResult = simulateTransactionValidation('255681234567');
        expect(validAirtelResult.requestBlocked).toBe(false);
        expect(validAirtelResult.status).toBe(200);

        const validTigoResult = simulateTransactionValidation('255651234567');
        expect(validTigoResult.requestBlocked).toBe(false);
        expect(validTigoResult.status).toBe(200);

        // Test that invalid networks are blocked
        const vodacomResult = simulateTransactionValidation('255741234567');
        expect(vodacomResult.requestBlocked).toBe(true);
        expect(vodacomResult.status).toBe(400);
        expect(vodacomResult.data?.error_code).toBe("INVALID_PHONE_NUMBER");

        const halotelResult = simulateTransactionValidation('255611234567');
        expect(halotelResult.requestBlocked).toBe(true);
        expect(halotelResult.status).toBe(400);

        const zantelResult = simulateTransactionValidation('255771234567');
        expect(zantelResult.requestBlocked).toBe(true);
        expect(zantelResult.status).toBe(400);

        const smileResult = simulateTransactionValidation('255661234567');
        expect(smileResult.requestBlocked).toBe(true);
        expect(smileResult.status).toBe(400);

        const ttclResult = simulateTransactionValidation('255731234567');
        expect(ttclResult.requestBlocked).toBe(true);
        expect(ttclResult.status).toBe(400);
    });
});
