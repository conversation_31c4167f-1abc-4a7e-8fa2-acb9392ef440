// import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
// import MagmaPay, { 
//   InitiateCollectionData, 
//   PaymentProcessData, 
//   InitiatePayoutData, 
//   ValidateBankAccountOrPhoneNumberData,
//   CountryCode 
// } from '../src/intergrations/MagmaPay';

// /**
//  * Comprehensive MagmaPay Collection and Payout Integration Tests
//  * 
//  * This test file covers:
//  * - Collection operations (initiateCollection, paymentProcess, getCollectionStatus)
//  * - Payout operations (initiatePayout, getPayoutStatus, getWalletBalances)
//  * - Validation methods (validateBankAccountOrPhoneNumber, validatePayeePhoneNumber)
//  * - Utility methods (generateUniqueReference, validatePhonePrefix)
//  * - Error handling and edge cases
//  */

// describe('MagmaPay Collection and Payout Integration Tests', () => {
//   let magmaPay: MagmaPay;
//   let testCollectionData: InitiateCollectionData;
//   let testPayoutData: InitiatePayoutData;
//   let testValidationData: ValidateBankAccountOrPhoneNumberData;

//   beforeAll(() => {
//     // Initialize MagmaPay instance
//     magmaPay = new MagmaPay();
    
//     // Set up test data for collection
//     testCollectionData = {
//       merchant_transaction_id: `TEST_COLLECTION_${Date.now()}`,
//       amount: 1000,
//       currency: 'XOF',
//       description: 'Test collection payment',
//       payee: '+225**********',
//       payee_first_name: 'John',
//       payee_last_name: 'Doe',
//       channel: 'mobile_money',
//       webhook_url: 'https://example.com/webhook',
//       success_url: 'https://example.com/success',
//       error_url: 'https://example.com/error',
//       custom_field: 'test_field'
//     };

//     // Set up test data for payout
//     testPayoutData = {
//       merchant_transaction_id: `TEST_PAYOUT_${Date.now()}`,
//       amount: 500,
//       currency: 'XOF',
//       channel: 'mobile_money',
//       payment_method: 'MTN_CI',
//       country_code: 'CI',
//       receiver_account: '+225**********',
//       payee: '+225**********',
//       receiver_first_name: 'Jane',
//       receiver_last_name: 'Smith',
//       description: 'Test payout payment',
//       webhook_url: 'https://example.com/webhook',
//       custom_field: 'test_field'
//     };

//     // Set up test data for validation
//     testValidationData = {
//       country_code: 'CI',
//       channel: 'mobile_money',
//       operator_code: 'MTN_CI',
//       phone_number: '+225**********'
//     };
//   });

//   beforeEach(() => {
//     // Reset any state before each test
//     jest.clearAllMocks();
//   });

//   afterAll(() => {
//     // Cleanup after all test s
//   //  });

//   // =============================================================================
//   // COLLECTION TESTS
//   // =============================================================================

//   describe('Collection Operations', () => {
// //     test('should get collection payment methods', async () => {
// //       try {
// //         const result = await magmaPay.getCollectionPaymentMethods();
// //         expect(result).toBeDefined();
// //         expect(Array.isArray(result)).toBe(true);
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Collection payment methods test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should initiate collection with valid data', async () => {
// //       try {
// //         const result = await magmaPay.initiateCollection(testCollectionData);
// //         expect(result).toBeDefined();
// //         expect(result.merchant_transaction_id).toBe(testCollectionData.merchant_transaction_id);
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Initiate collection test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should handle collection initiation with missing required fields', async () => {
// //       const invalidData = {
// //         ...testCollectionData,
// //         merchant_transaction_id: '', // Empty transaction ID
// //         amount: 0 // Invalid amount
// //       };

// //       try {
// //         await magmaPay.initiateCollection(invalidData);
// //         // If no error is thrown, the API should still handle it gracefully
// //       } catch (error) {
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should process payment with valid token', async () => {
// //       const paymentProcessData: PaymentProcessData = {
// //         payment_token: 'test_token_123',
// //         otp_code: '123456'
// //       };

// //       try {
// //         const result = await magmaPay.paymentProcess(paymentProcessData);
// //         expect(result).toBeDefined();
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Payment process test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should get collection status', async () => {
// //       const transactionId = 'test_transaction_123';
      
// //       try {
// //         const result = await magmaPay.getCollectionStatus(transactionId);
// //         expect(result).toBeDefined();
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Collection status test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });
// //   });

// //   // =============================================================================
// //   // PAYOUT TESTS
// //   // =============================================================================

// //   describe('Payout Operations', () => {
// //     test('should get payout payment methods', async () => {
// //       try {
// //         const result = await magmaPay.getPayoutPaymentMethods();
// //         expect(result).toBeDefined();
// //         expect(Array.isArray(result)).toBe(true);
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Payout payment methods test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should get wallet balances', async () => {
// //       try {
// //         const result = await magmaPay.getWalletBalances();
// //         expect(result).toBeDefined();
// //         expect(result.data).toBeDefined();
// //         expect(Array.isArray(result.data)).toBe(true);
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Wallet balances test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should get balance by country prefix', async () => {
// //       try {
// //         const result = await magmaPay.getBalanceByCountryPrefix('CI', 'XOF');
// //         expect(typeof result).toBe('number');
// //         expect(result).toBeGreaterThanOrEqual(0);
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Balance by country prefix test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should initiate payout with valid data', async () => {
// //       try {
// //         const result = await magmaPay.initiatePayout(testPayoutData);
// //         expect(result).toBeDefined();
// //         expect(result.data).toBeDefined();
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Initiate payout test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should handle payout initiation with invalid data', async () => {
// //       const invalidPayoutData = {
// //         ...testPayoutData,
// //         amount: -100, // Negative amount
// //         currency: 'INVALID' // Invalid currency
// //       };

// //       try {
// //         await magmaPay.initiatePayout(invalidPayoutData);
// //       } catch (error) {
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should get payout status by transfer token', async () => {
// //       const transferToken = 'test_transfer_token_123';
      
// //       try {
// //         const result = await magmaPay.getPayoutStatus(transferToken);
// //         expect(result).toBeDefined();
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Payout status test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should get payout status by reference', async () => {
// //       const payoutId = 'test_payout_reference_123';
      
// //       try {
// //         const result = await magmaPay.getPayoutStatusbyReference(payoutId);
// //         expect(result).toBeDefined();
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Payout status by reference test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });
// //   });

// //   // =============================================================================
// //   // VALIDATION TESTS
// //   // =============================================================================

// //   describe('Validation Methods', () => {
// //     test('should validate bank account or phone number for mobile money', async () => {
// //       try {
// //         const result = await magmaPay.validateBankAccountOrPhoneNumber(testValidationData);
// //         expect(result).toBeDefined();
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Bank account/phone validation test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should validate bank account for bank account channel', async () => {
// //       const bankValidationData: ValidateBankAccountOrPhoneNumberData = {
// //         country_code: 'CI',
// //         channel: 'bank_account',
// //         operator_code: 'BANK_CI',
// //         account_number: '**********'
// //       };

// //       try {
// //         const result = await magmaPay.validateBankAccountOrPhoneNumber(bankValidationData);
// //         expect(result).toBeDefined();
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Bank account validation test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should throw error for missing account number in bank account validation', async () => {
// //       const invalidData = {
// //         country_code: 'CI',
// //         channel: 'bank_account' as const,
// //         operator_code: 'BANK_CI'
// //         // Missing account_number
// //       };

// //       await expect(magmaPay.validateBankAccountOrPhoneNumber(invalidData as ValidateBankAccountOrPhoneNumberData))
// //         .rejects.toThrow('account_number is required for bank_account channel');
// //     });

// //     test('should throw error for missing phone number in mobile money validation', async () => {
// //       const invalidData = {
// //         country_code: 'CI',
// //         channel: 'mobile_money' as const,
// //         operator_code: 'MTN_CI'
// //         // Missing phone_number
// //       };

// //       await expect(magmaPay.validateBankAccountOrPhoneNumber(invalidData as ValidateBankAccountOrPhoneNumberData))
// //         .rejects.toThrow('phone_number is required for mobile_money channel');
// //     });

// //     test('should validate payee phone number with valid Côte d\'Ivoire number', () => {
// //       const result = magmaPay.validatePayeePhoneNumber('+225**********', 'CI');
      
// //       expect(result.isValid).toBe(true);
// //       expect(result.message).toBe('Phone number is valid');
// //       expect(result.prefix).toBe('+225');
// //     });

// //     test('should reject invalid phone number format', () => {
// //       const result = magmaPay.validatePayeePhoneNumber('invalid_phone', 'CI');
      
// //       expect(result.isValid).toBe(false);
// //       expect(result.message).toContain('Phone number must be in E.164 format');
// //     });

// //     test('should reject phone number without country code', () => {
// //       const result = magmaPay.validatePayeePhoneNumber('**********', 'CI');
      
// //       expect(result.isValid).toBe(false);
// //       expect(result.message).toContain('Phone number must be in E.164 format');
// //     });

// //     test('should reject phone number with wrong country code', () => {
// //       const result = magmaPay.validatePayeePhoneNumber('+**********', 'CI');
      
// //       expect(result.isValid).toBe(false);
// //       expect(result.message).toContain('Phone number is invalid for the selected payment provider');
// //     });

// //     test('should validate accepted currency', async () => {
// //       const validCurrencies = ['UGX', 'KES', 'NGN', 'GHS', 'XOF'];
      
// //       for (const currency of validCurrencies) {
// //         const result = await magmaPay.validateAcceptedCurrency(currency);
// //         expect(result).toBe(true);
// //       }
// //     });

// //     test('should reject invalid currency', async () => {
// //       const invalidCurrencies = ['USD', 'EUR', 'INVALID'];
      
// //       for (const currency of invalidCurrencies) {
// //         const result = await magmaPay.validateAcceptedCurrency(currency);
// //         expect(result).toBe(false);
// //       }
// //     });

// //     test('should validate channel used', async () => {
// //       const validChannels = [
// //         { channel: 'mobile_money' as const },
// //         { channel: 'credit_card' as const },
// //         { channel: 'wave' as const }
// //       ];
      
// //       for (const channel of validChannels) {
// //         const result = await magmaPay.validateChannelUsed(channel);
// //         expect(result).toBe(true);
// //       }
// //     });

// //     test('should reject invalid channel', async () => {
// //       const invalidChannel = { channel: 'invalid_channel' as any };
      
// //       const result = await magmaPay.validateChannelUsed(invalidChannel);
// //       expect(result).toBe(false);
// //     });
// //   });

// //   // =============================================================================
// //   // UTILITY METHODS TESTS
// //   // =============================================================================

// //   describe('Utility Methods', () => {
// //     test('should generate unique reference', () => {
// //       const reference1 = magmaPay.generateUniqueReference();
// //       const reference2 = magmaPay.generateUniqueReference();
      
// //       expect(reference1).toBeDefined();
// //       expect(reference2).toBeDefined();
// //       expect(reference1).not.toBe(reference2);
// //       expect(reference1).toMatch(/^MAGMAPAY_\d+_[a-z0-9]+$/);
// //       expect(reference2).toMatch(/^MAGMAPAY_\d+_[a-z0-9]+$/);
// //     });

// //     test('should validate phone prefix for supported countries', async () => {
// //       const testCases = [
// //         { phone: '+225**********', country: 'CI' as CountryCode, expected: 'MTN_CI' },
// //         { phone: '+2250502030405', country: 'CI' as CountryCode, expected: 'MTN_CI' },
// //         { phone: '+2250702030405', country: 'CI' as CountryCode, expected: 'ORANGE_CI' },
// //         { phone: '+229**********', country: 'BJ' as CountryCode, expected: 'MTN_BJ' },
// //         { phone: '+2217702030405', country: 'SN' as CountryCode, expected: 'ORANGE_SN' }
// //       ];

// //       for (const testCase of testCases) {
// //         const result = await magmaPay.validatePhonePrefix(testCase.phone, testCase.country);
// //         expect(result).toBe(testCase.expected);
// //       }
// //     });

// //     test('should return false for unsupported phone prefix', async () => {
// //       const result = await magmaPay.validatePhonePrefix('+**********', 'CI' as CountryCode);
// //       expect(result).toBe(false);
// //     });

// //     test('should get transaction history', async () => {
// //       try {
// //         const result = await magmaPay.getTransactionHistory();
// //         expect(result).toBeDefined();
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Transaction history test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should get transaction history with filters', async () => {
// //       const queryData = {
// //         start_date: '2024-01-01',
// //         end_date: '2024-12-31',
// //         channel: 'mobile_money',
// //         currency: 'XOF',
// //         status: 'success'
// //       };

// //       try {
// //         const result = await magmaPay.getTransactionHistory(queryData);
// //         expect(result).toBeDefined();
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Transaction history with filters test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should get transaction history by account', async () => {
// //       const receiverAccount = '+225**********';
      
// //       try {
// //         const result = await magmaPay.getTransactionHistoryByAccount(receiverAccount);
// //         expect(result).toBeDefined();
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Transaction history by account test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });
// //   });

// //   // =============================================================================
// //   // ERROR HANDLING TESTS
// //   // =============================================================================

// //   describe('Error Handling', () => {
// //     test('should handle network errors gracefully', async () => {
// //       // Mock a network error scenario
// //       const originalMakeRequest = magmaPay['makeRequest'];
      
// //       // Temporarily replace makeRequest to simulate network error
// //       magmaPay['makeRequest'] = jest.fn().mockRejectedValue(new Error('Network error'));
      
// //       try {
// //         await magmaPay.getCollectionPaymentMethods();
// //       } catch (error) {
// //         expect((error as Error).message).toBe('Network error');
// //       }
      
// //       // Restore original method
// //       magmaPay['makeRequest'] = originalMakeRequest;
// //     });

// //     test('should handle API errors gracefully', async () => {
// //       // Mock an API error scenario
// //       const originalMakeRequest = magmaPay['makeRequest'];
      
// //       // Temporarily replace makeRequest to simulate API error
// //       magmaPay['makeRequest'] = jest.fn().mockResolvedValue({
// //         code: 400,
// //         message: 'Bad Request',
// //         data: null
// //       });
      
// //       try {
// //         const result = await magmaPay.getCollectionPaymentMethods();
// //         expect(result.code).toBe(400);
// //       } catch (error) {
// //         expect(error).toBeDefined();
// //       }
      
// //       // Restore original method
// //       magmaPay['makeRequest'] = originalMakeRequest;
// //     });
// //   });

// //   // =============================================================================
// //   // INTEGRATION TESTS
// //   // =============================================================================

// //   describe('Integration Tests', () => {
// //     test('should complete full collection flow', async () => {
// //       try {
// //         // Step 1: Get payment methods
// //         const paymentMethods = await magmaPay.getCollectionPaymentMethods();
// //         expect(paymentMethods).toBeDefined();

// //         // Step 2: Initiate collection
// //         const collectionResult = await magmaPay.initiateCollection(testCollectionData);
// //         expect(collectionResult).toBeDefined();

// //         // Step 3: Check collection status
// //         if (collectionResult.merchant_transaction_id) {
// //           const statusResult = await magmaPay.getCollectionStatus(collectionResult.merchant_transaction_id);
// //           expect(statusResult).toBeDefined();
// //         }
// //       } catch (error) {
// //         // In test environment, API might not be available
// //         console.log('Full collection flow test skipped - API not available');
// //         expect(error).toBeDefined();
// //       }
// //     });

// //     test('should complete full payout flow', async () => {
// //       try {
// //         // Step 1: Get wallet balances
// //         const balances = await magmaPay.getWalletBalances();
// //         expect(balances).toBeDefined();

// //         // Step 2: Get payout methods
// //         const payoutMethods = await magmaPay.getPayoutPaymentMethods();
// //         expect(payoutMethods).toBeDefined();

// //         // Step 3: Initiate payout
// //         const payoutResult = await magmaPay.initiatePayout(testPayoutData);
// //         expect(payoutResult).toBeDefined();

// //         // Step 4: Check payout status
// //         if (payoutResult.data?.transfer_token) {
// //           const statusResult = await magmaPay.getPayoutStatus(payoutResult.data.transfer_token);
// //           expect(statusResult).toBeDefined();
// //         }
// //       } catch (error) {
// //         // In test environment, API might not be available
//         console.log('Full payout flow test skipped - API not available');
//         expect(error).toBeDefined();
//       }
//     });
//   });
// });
