/**
 * Unit tests for HoneyCoin.getBalance method
 * 
 * This test file loads environment variables from the .env file in the project root.
 * If environment variables are not set in the .env file, test fallback values will be used.
 * 
 * Required environment variables (if using .env file):
 * - HONEYCOIN_API_URL
 * - HONEYCOIN_CRYPTO_API_URL
 * - HONEYCOIN_PUBLIC_KEY
 * - HONEYCOIN_API_KEY
 */

import dotenv from 'dotenv';
import path from 'path';
import HoneyCoin from '../src/intergrations/HoneyCoin';
import RequestHelper from '../src/helpers/request.helper';
import axios from 'axios';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Mock dependencies
jest.mock('../src/helpers/request.helper');
jest.mock('axios');

describe('HoneyCoin.getBalance', () => {
  let honeyCoin: HoneyCoin;
  const mockRequestHelper = RequestHelper as jest.Mocked<typeof RequestHelper>;
  const mockedAxios = axios as jest.Mocked<typeof axios>;

  // Store original env vars to restore after tests
  const originalEnv: Record<string, string | undefined> = {};

  beforeAll(() => {
    // Store original environment variables
    originalEnv.HONEYCOIN_API_URL = process.env.HONEYCOIN_API_URL;
    originalEnv.HONEYCOIN_CRYPTO_API_URL = process.env.HONEYCOIN_CRYPTO_API_URL;
    originalEnv.HONEYCOIN_PUBLIC_KEY = process.env.HONEYCOIN_PUBLIC_KEY;
    originalEnv.HONEYCOIN_API_KEY = process.env.HONEYCOIN_API_KEY;
  });

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    
    // Ensure environment variables are set (use values from .env file if loaded, otherwise use test fallbacks)
    if (!process.env.HONEYCOIN_API_URL) {
      process.env.HONEYCOIN_API_URL = 'https://api.honeycoin.test';
    }
    if (!process.env.HONEYCOIN_CRYPTO_API_URL) {
      process.env.HONEYCOIN_CRYPTO_API_URL = 'https://crypto-api.honeycoin.test';
    }
    if (!process.env.HONEYCOIN_PUBLIC_KEY) {
      process.env.HONEYCOIN_PUBLIC_KEY = 'test-public-key';
    }
    if (!process.env.HONEYCOIN_API_KEY) {
      process.env.HONEYCOIN_API_KEY = 'test-api-key';
    }

    // Create new instance for each test
    honeyCoin = new HoneyCoin();

    // Mock axios defaults
    mockedAxios.defaults = {
      headers: {
        common: {}
      }
    } as any;
  });

  afterAll(() => {
    // Restore original environment variables
    Object.keys(originalEnv).forEach(key => {
      if (originalEnv[key] !== undefined) {
        process.env[key] = originalEnv[key];
      } else {
        delete process.env[key];
      }
    });
  });

  describe('Successful balance retrieval', () => {
    test('should call getUsers and log the response', async () => {
      // Mock token generation
      const mockTokenResponse = {
        data: {
          success: true,
          token: 'mock-bearer-token'
        }
      };
      mockedAxios.post = jest.fn().mockResolvedValue(mockTokenResponse);

      // Mock RequestHelper methods
      mockRequestHelper.setEndpoint = jest.fn();
      mockRequestHelper.setHeaders = jest.fn();
      mockRequestHelper.setData = jest.fn();
      mockRequestHelper.getRequest = jest.fn().mockResolvedValue({
        data: {
          success: true,
          data: {
            users: [
              { id: 1, email: '<EMAIL>', balance: { KES: 1000 } },
              { id: 2, email: '<EMAIL>', balance: { KES: 2000 } }
            ]
          }
        },
        status: true
      });
      mockRequestHelper.getErrors = jest.fn().mockReturnValue({ status: false });
      mockRequestHelper.getResults = jest.fn().mockReturnValue({
        data: {
          success: true,
          data: {
            users: [
              { id: 1, email: '<EMAIL>', balance: { KES: 1000 } }
            ]
          }
        },
        status: true
      });

      // Spy on console.log
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();

      // Call getBalance
      const result = await honeyCoin.getBalance('KES');

      // Verify token generation was called with actual env variables or test values
      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.stringContaining('/auth/generate-bearer-token'),
        {
          publicKey: expect.any(String),
          'api-key': expect.any(String)
        }
      );

      // Verify RequestHelper methods were called
      expect(mockRequestHelper.setEndpoint).toHaveBeenCalled();
      expect(mockRequestHelper.setHeaders).toHaveBeenCalled();
      expect(mockRequestHelper.getRequest).toHaveBeenCalled();

      // Verify console.log was called with response
      expect(consoleLogSpy).toHaveBeenCalledWith(
        'response get users',
        expect.any(Object)
      );

      consoleLogSpy.mockRestore();
    });
  });

  describe('Error handling', () => {
    test('should return null when getUsers throws an error', async () => {
      // Mock token generation
      const mockTokenResponse = {
        data: {
          success: true,
          token: 'mock-bearer-token'
        }
      };
      mockedAxios.post = jest.fn().mockResolvedValue(mockTokenResponse);

      // Mock RequestHelper to throw an error
      mockRequestHelper.setEndpoint = jest.fn();
      mockRequestHelper.setHeaders = jest.fn();
      mockRequestHelper.getRequest = jest.fn().mockRejectedValue(
        new Error('Network error')
      );
      mockRequestHelper.getErrors = jest.fn().mockReturnValue({ status: false });

      // Call getBalance
      const result = await honeyCoin.getBalance('KES');

      // Should return null on error
      expect(result).toBeNull();
    });

    test('should return null when token generation fails', async () => {
      // Mock token generation to fail
      mockedAxios.post = jest.fn().mockRejectedValue(
        new Error('Token generation failed')
      );

      // Call getBalance
      const result = await honeyCoin.getBalance('KES');

      // Should return null on error
      expect(result).toBeNull();
    });

    test('should return null when token generation returns error status', async () => {
      // Mock token generation to return error
      const mockTokenResponse = {
        data: {
          success: false,
          message: 'Invalid credentials'
        }
      };
      mockedAxios.post = jest.fn().mockResolvedValue(mockTokenResponse);

      // Call getBalance
      const result = await honeyCoin.getBalance('KES');

      // Should return null on error
      expect(result).toBeNull();
    });
  });

  describe('Different currencies', () => {
    test('should handle different currency codes', async () => {
      // Mock token generation
      const mockTokenResponse = {
        data: {
          success: true,
          token: 'mock-bearer-token'
        }
      };
      mockedAxios.post = jest.fn().mockResolvedValue(mockTokenResponse);

      // Mock RequestHelper methods
      mockRequestHelper.setEndpoint = jest.fn();
      mockRequestHelper.setHeaders = jest.fn();
      mockRequestHelper.setData = jest.fn();
      mockRequestHelper.getRequest = jest.fn().mockResolvedValue({
        data: {
          success: true,
          data: { users: [] }
        },
        status: true
      });
      mockRequestHelper.getErrors = jest.fn().mockReturnValue({ status: false });
      mockRequestHelper.getResults = jest.fn().mockReturnValue({
        data: {
          success: true,
          data: { users: [] }
        },
        status: true
      });

      // Spy on console.log
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();

      // Test with different currencies
      const currencies = ['KES', 'UGX', 'TZS', 'USD'];
      
      for (const currency of currencies) {
        await honeyCoin.getBalance(currency);
        expect(mockRequestHelper.getRequest).toHaveBeenCalled();
      }

      consoleLogSpy.mockRestore();
    });
  });

  describe('RequestHelper error status', () => {
    test('should handle RequestHelper returning error status', async () => {
      // Mock token generation
      const mockTokenResponse = {
        data: {
          success: true,
          token: 'mock-bearer-token'
        }
      };
      mockedAxios.post = jest.fn().mockResolvedValue(mockTokenResponse);

      // Mock RequestHelper with error status
      mockRequestHelper.setEndpoint = jest.fn();
      mockRequestHelper.setHeaders = jest.fn();
      mockRequestHelper.getRequest = jest.fn().mockResolvedValue({
        data: {
          success: false,
          message: 'API error'
        },
        status: false
      });
      mockRequestHelper.getErrors = jest.fn().mockReturnValue({ status: true });
      mockRequestHelper.getResults = jest.fn().mockReturnValue({
        data: {
          success: false,
          message: 'API error'
        },
        status: false
      });

      // Spy on console.log
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();

      // Call getBalance
      const result = await honeyCoin.getBalance('KES');

      // Should still call getUsers and log
      expect(consoleLogSpy).toHaveBeenCalled();

      consoleLogSpy.mockRestore();
    });
  });

  describe('Environment variables', () => {
    test('should use environment variables from .env file or fallback to test values', () => {
      // Verify that environment variables are available (either from .env or test defaults)
      expect(process.env.HONEYCOIN_API_URL).toBeDefined();
      expect(process.env.HONEYCOIN_CRYPTO_API_URL).toBeDefined();
      expect(process.env.HONEYCOIN_PUBLIC_KEY).toBeDefined();
      expect(process.env.HONEYCOIN_API_KEY).toBeDefined();

      // Verify HoneyCoin instance uses the configured values
      const config = (honeyCoin as any).config;
      expect(config.apiUrl).toBe(process.env.HONEYCOIN_API_URL);
      expect(config.cryptoApiUrl).toBe(process.env.HONEYCOIN_CRYPTO_API_URL);
      expect(config.publicKey).toBe(process.env.HONEYCOIN_PUBLIC_KEY);
      expect(config.apiKey).toBe(process.env.HONEYCOIN_API_KEY);
    });

    test('should load environment variables from .env file when available', () => {
      // This test verifies that dotenv.config() has been called
      // If .env file exists and has values, they should be loaded
      // If not, test fallback values should be used
      const honeyCoinInstance = new HoneyCoin();
      const config = (honeyCoinInstance as any).config;
      
      // Config should have values (either from .env or fallback)
      expect(config.apiUrl).toBeTruthy();
      expect(config.cryptoApiUrl).toBeTruthy();
      expect(config.publicKey).toBeTruthy();
      expect(config.apiKey).toBeTruthy();
    });
  });
});

