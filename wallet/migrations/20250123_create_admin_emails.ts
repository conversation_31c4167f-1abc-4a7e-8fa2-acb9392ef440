import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
    // Create admin_emails table
    await knex.schema.createTable('admin_emails', (table) => {
        table.increments('id').primary();
        table.string('email', 255).notNullable().unique();
        table.string('name', 100).nullable();
        table.string('role', 50).nullable(); // e.g., 'admin', 'finance', 'technical'
        table.boolean('is_active').defaultTo(true);
        table.boolean('receive_balance_alerts').defaultTo(true);
        table.boolean('receive_error_alerts').defaultTo(true);
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
        
        // Indexes
        table.index('email', 'idx_admin_email');
        table.index('is_active', 'idx_admin_active');
    });

    // Insert default admin emails
    await knex('admin_emails').insert([
        { 
            email: '<EMAIL>', 
            name: '<PERSON><PERSON><PERSON>', 
            role: 'admin',
            receive_balance_alerts: true,
            receive_error_alerts: true
        },
        { 
            email: '<EMAIL>', 
            name: 'Emma', 
            role: 'admin',
            receive_balance_alerts: true,
            receive_error_alerts: true
        }
    ]);
}

export async function down(knex: Knex): Promise<void> {
    // WARNING: This will delete all admin emails!
    // Only use this in development or if you're absolutely sure.
    
    // Uncomment the line below only if you're sure you want to drop the table
    // await knex.schema.dropTableIfExists('admin_emails');
    
    console.warn('⚠️  DOWN migration for admin_emails is disabled for safety.');
    console.warn('⚠️  To drop this table, manually run: DROP TABLE admin_emails;');
}

