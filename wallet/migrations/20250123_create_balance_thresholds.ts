import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
    // Create balance_thresholds table
    await knex.schema.createTable('balance_thresholds', (table) => {
        table.increments('id').primary();
        table.string('currency', 10).notNullable().unique();
        table.decimal('threshold_amount', 20, 2).notNullable().defaultTo(0);
        table.string('description', 255).nullable();
        table.boolean('is_active').defaultTo(true);
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
        
        // Indexes
        table.index('currency', 'idx_currency');
        table.index('is_active', 'idx_active');
    });

    // Insert default threshold values
    await knex('balance_thresholds').insert([
        { currency: 'USD', threshold_amount: 1000.00, description: 'US Dollar minimum threshold' },
        { currency: 'USDC', threshold_amount: 1000.00, description: 'USD Coin minimum threshold' },
        { currency: 'USDT', threshold_amount: 1000.00, description: 'Tether minimum threshold' },
        { currency: 'KES', threshold_amount: 100000.00, description: 'Kenyan Shilling minimum threshold' },
        { currency: 'UGX', threshold_amount: 3000000.00, description: 'Ugandan Shilling minimum threshold' },
        { currency: 'TZS', threshold_amount: 2000000.00, description: 'Tanzanian Shilling minimum threshold' },
        { currency: 'XLM', threshold_amount: 1000.00, description: 'Stellar Lumens minimum threshold' },
        { currency: 'EUR', threshold_amount: 800.00, description: 'Euro minimum threshold' },
        { currency: 'GBP', threshold_amount: 700.00, description: 'British Pound minimum threshold' }
    ]);
}

export async function down(knex: Knex): Promise<void> {
    // WARNING: This will delete all balance thresholds data!
    // Only use this in development or if you're absolutely sure.
    // In production, consider keeping the table or backing up data first.
    
    // Uncomment the line below only if you're sure you want to drop the table
    // await knex.schema.dropTableIfExists('balance_thresholds');
    
    console.warn('⚠️  DOWN migration for balance_thresholds is disabled for safety.');
    console.warn('⚠️  To drop this table, manually run: DROP TABLE balance_thresholds;');
}

