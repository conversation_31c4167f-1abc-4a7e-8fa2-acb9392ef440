import BaseModel from "./base.model";
import { get, post } from "./httpRequest";
import { v4 as uuidv4 } from 'uuid';
import EmailSender from './email';
import TwoF<PERSON>AuthHelper from "./2fa.helper";
import CryptoJS from "crypto-js";
import StellarSdk from "stellar-sdk";
export type WebhookData = {
    type: string;
    event: string;
    statusCode: number;
    message: string;
    client_id: string;
    trans_type: string;
    timestamp: string;
    reference_id: string;
    status: string;
    amount: string;
    fee: string;
    currency: string;
    sender_account: string;
    receiver_account: string;
    transaction_id: string;
    meta: string;
    chainInfo?: ChainInfo;
}
type ChainInfo = {
    from_address: string;
    to_address: string;
    amount: string;
    asset_code: string;
    contract_address?: string;
    hash?: string;
    state?: string;
    direction?: string;
}
export enum Steps {
    START = "START",
    VALIDATION = "VALIDATION",
    SAVE_TO_DB = "SAVE_TO_DB",
    SEND_TO_THIRD_PARTY = "SEND_TO_THIRD_PARTY",
    THIRDPARTY_REQUEST = "THIRDPARTY_REQUEST",
    THIRDPARTY_RESPONSE = "THIRDPARTY_RESPONSE",
    UPDATE_BALANCE = "UPDATE_BALANCE",
    UPDATE_BALANCE_FAILED = "UPDATE_BALANCE_FAILED",
    ERROR_REPONSE = "ERROR_REPONSE",
    WEBHOOK_SENT = "WEBHOOK_SENT",
    WEBHOOK_RECEIVED = "WEBHOOK_RECEIVED",
    STELLAR_REQUEST = "STELLAR_REQUEST",
    STELLAR_RESPONSE = "STELLAR_RESPONSE",
    TRANS_STATUS_CHECK = "TRANS_STATUS_CHECK",
    OUTGOING_TRANSACTION = "OUTGOING_TRANSACTION",
    END = "END",
    TRANS_STATUS_RESPONSE = "TRANS_STATUS_RESPONSE",
    TRANSACTION_TIMEOUT = "TRANSACTION_TIMEOUT",
    BALANCE_CHECK = "BALANCE_CHECK"
}


import jwt from "jsonwebtoken";
const SECRET_KEY = process.env.SECRET_KEY || ""

import { sendNotification } from "./FCM";
import { StableCoinTransaction, StatusCodes, TransactionResponseInterface } from "../intergrations/interfaces";
import LrService from "../intergrations/LR";
import { walletEventPublisher } from "../utils/eventPublisher";
const lr = new LrService();
const mailer = new EmailSender();
export default class Model extends BaseModel {
    async GetIssuerAccount(asset_code: string, arg1: string) {
        return process.env.STELLAR_PAYOUT_ISSUER_SECRET
    }

    async getNetworkCodes(code: string) {
        console.log(`getNetworkCodes`, code)
        const networkCodes: any = await this.selectDataQuerySafe("network_codes", { code });
        console.log(`networkCodes`, networkCodes)
        if (networkCodes.length > 0) {
            return networkCodes[0].network
        }
        return "MTN"
    }

    GetTradingIssuer(sourceAsset: string, clientId: string) {
        console.log(`GetTradingIssuer`, sourceAsset, clientId)
        const assetIssuer = process.env.STELLAR_PAYOUT_ISSUER_PUBLIC || "";
        console.log(`GetTradingIssuer`, assetIssuer)
        return assetIssuer;
    }

    checkTransactionStatus(statusCode: any) {
        const SUCCESS_CODES = ["0"];
        const FAILED_CODES = [
            "1", "2", "3", "4", "5", "6", "7", "8", "10", "11", "12", "13", "14", "15", "20", "22",
            "25", "26", "27", "28", "30", "31", "32", "33", "34", "35", "36", "37", "2000", "202", "100"
        ];
        const PENDING_CODES = ["122", "9", "17", "21"];
        const ONHOLD_CODES = ["11"];
        //const RETRY_CODES = ["16"]
        const RETRY_CODES = ["567890"]

        if (SUCCESS_CODES.includes(statusCode)) {
            return "SUCCESS"
        } else if (FAILED_CODES.includes(statusCode)) {
            return "FAILED"
        } else if (PENDING_CODES.includes(statusCode)) {
            return "PENDING"
        } else if (ONHOLD_CODES.includes(statusCode)) {
            return "ONHOLD"
        } else if (RETRY_CODES.includes(statusCode)) {
            return "RETRY"
        } else {
            return "ONHOLD"
        }
    }

    async saveTransactionLog(trans_id: string, status: string, step: Steps, response_code: number, description: string, data: any) {
        try {
            const logData = {
                trans_id: trans_id,
                status: status,
                step: step,
                response_code: response_code,
                description: description,
                data: typeof data === 'string' ? data : JSON.stringify(data)
            }
            return await this.insertData("transactions_log", logData);
        } catch (error: any) {
            console.error("Error saving transaction log:", error);
            return false;
        }

    }

    async composeThirdPartyTransaction(transaction: any) {
        const callbackData: TransactionResponseInterface = {
            trans_id: transaction.trans_id,
            client_id: transaction.client_id,
            service_name: transaction.service_name,
            product_id: transaction.product_id,
            trans_type: transaction.trans_type,
            reference_id: transaction.reference_id,
            amount: transaction.amount,
            currency: transaction.currency,
            sender_account: transaction.sender_account,
            receiver_account: transaction.receiver_account,
            status: transaction.status,
            fee: transaction.fee,
            created_at: transaction.created_at,
            narration: transaction.memo
        }
        return callbackData;
    }

    async systemRules() {
        const rules = await this.selectDataQuerySafe("system_rules");
        return rules;
    }

    async validateAPIkeyPermission(clientId: any, apiKey: any, arg2: string, ipAddress: any) {
        try {
            let hasPermission = false;
            let validIP = false;

            const systemRules = await this.systemRules();
            console.log(`systemRules`, systemRules)
            if (systemRules.length === 0) {
                return this.makeResponse(401, "System rules not found");
            }
            const { api_key_permission, ip_address_permission } = systemRules[0];
            console.log(`api_key_permission`, api_key_permission)
            console.log(`ip_address_permission`, ip_address_permission)
            const apiKeyRecord = await this.selectDataQuerySafe("api_keys", { client_id: clientId, api_key: apiKey });
            console.log(`apiKeyRecord`, apiKeyRecord)
            if (apiKeyRecord.length === 0) {
                return this.makeResponse(401, "Invalid API key");
            }
            const permissions = apiKeyRecord[0].permissions.split(",");
            if (permissions.includes(arg2.toUpperCase())) {
                hasPermission = true;
            }
            const ipAddresses = apiKeyRecord[0].ip_addresses.split(",");
            if (ipAddresses.includes(ipAddress)) {
                validIP = true;
            }
            console.log(`hasPermission`, hasPermission, validIP)



            if (!hasPermission) {
                return this.makeResponse(401, "API key has no permission to access this endpoint");
            }
            if (!validIP) {
                return this.makeResponse(401, "IP not allowed to access this endpoint");
            }
            console.log(`validIP`, validIP)


            if (hasPermission && validIP) {
                return true;
            }
        } catch (error: any) {
            console.error("Error validating API key permission:", error);
            return this.makeResponse(400, "Error validating API key permission");
        }
    }

    async userFaAuthAccountStatus(data: any) {
        try {

            const userID = (data?.user_type === 'admin' || data?.user_type === "") ? data.clientId : data.userId;
            const client: any = await this.callQuerySafe(`
                        SELECT * FROM user_2fa
                        WHERE user_id = ? AND user_type = ? AND deleted_at IS NULL
                        ORDER BY created_at DESC
                        LIMIT 1
                    `, [userID, data.user_type]);
            return client;

        } catch (error: any) {
            return;
        }
    }

    async getFees(data: any) {
        console.log(`getFees`, data)
        try {
            const { productId, clientId, amount } = data;

            if (!productId || !clientId || !amount) {
                return this.makeResponse(400, "Product ID, Client ID and Amount are required");
            }

            const parsedAmount = parseFloat(String(amount));
            const productInfo = await this.getProductInfo(productId, clientId);
            const { currency: productCurrency, has_c_account, min_amount, max_amount, transaction_type, fee_type, provider_fee, fee_amount, product_code: service_name } = productInfo;

            const { mudaFee, providerFee } = await this.calculateFee(fee_type, fee_amount, provider_fee, parsedAmount, productInfo);

            return this.makeResponse(200, "Fees fetched successfully", {
                fee_type: fee_type,
                fee: mudaFee,
                provider_fee: providerFee
            })
        } catch (error) {
            console.error("getFees error", error);
            return this.makeResponse(500, "Failed to get fees");
        }
    }

    async calculateFee(fee_type: any, fee_amount: any, provider_fee: any, parsedAmount: number, productInfo: any): Promise<{ mudaFee: any; providerFee: any; }> {

        const { originalFeeType, originalFeeAmount } = productInfo
        let fallBackFeeAmount = originalFeeAmount;

        if (originalFeeType == "PERCENTAGE") {
            fallBackFeeAmount = (originalFeeAmount / 100) * parsedAmount;
        }

        let mudaFee = fee_amount;
        let providerFee = provider_fee;
        try {

            if (fee_type == "PERCENTAGE") {
                mudaFee = (fee_amount / 100) * parsedAmount;
                providerFee = (provider_fee / 100) * parsedAmount;
            } else if (fee_type == "TIER") {
                const { custome_fee_id } = productInfo;
                const customeFee: any = await this.callQuerySafe(
                    "SELECT * FROM tier_fees WHERE custome_fee_id = ? AND ? BETWEEN min_amount AND max_amount",
                    [custome_fee_id, parsedAmount]
                );
                if (customeFee.length > 0) {
                    const customeFeeType = customeFee[0].fee_type;
                    if (customeFeeType == "PERCENTAGE") {
                        mudaFee = (customeFee[0].fee_value / 100) * parsedAmount;
                    } else if (customeFeeType == "FLAT") {
                        mudaFee = customeFee[0].fee_value;
                    }
                } else {
                    mudaFee = fallBackFeeAmount;
                }
            }
            return { mudaFee, providerFee };
        } catch (error) {
            console.error("calculateFee error", error);
            return { mudaFee: mudaFee, providerFee: providerFee };
        }
    }
    async getProductInfo(product_id: any, client_id: any) {
        let productInfo = await this.selectDataQuerySafe("products", { product_id });
        if (productInfo.length === 0) {
            return null
        }
        productInfo[0].product_id = product_id
        productInfo[0].originalFeeAmount = productInfo[0].fee_amount
        productInfo[0].originalFeeType = productInfo[0].fee_type
        productInfo[0].custome_fee_id = null


        const customeFees = await this.selectDataQuerySafe("custome_fees", {
            product_id,
            client_id,
            active_status: 1
        });
        if (customeFees.length > 0) {
            const { fee_type, amount, id } = customeFees[0]
            productInfo[0].fee_type = fee_type
            productInfo[0].fee_amount = amount
            productInfo[0].custome_fee_id = id
        }
        console.log(`productInfo`, productInfo)
        return productInfo[0]
    }

    async createWallet(clientId: string) {
        try {
            const keys: any = await this.selectDataQuerySafe("client_wallets", { client_id: clientId });
            if (keys.length > 0) {
                return keys[0]
            }
            const keypair = StellarSdk.Keypair.random();
            const publicKey = keypair.publicKey();
            const secretKey = keypair.secret();
            const encryptedSecretKey = CryptoJS.AES.encrypt(secretKey, SECRET_KEY).toString();
            const decryptedSecretKey = CryptoJS.AES.decrypt(encryptedSecretKey, SECRET_KEY).toString(CryptoJS.enc.Utf8);
            // console.log(`decSSecretKey`, decryptedSecretKey) // SECURITY: Removed private key logging
            const apiKey = uuidv4();
            const apiKeyData = {
                client_id: clientId,
                public_key: publicKey,
                secret_key: encryptedSecretKey,
            };

            await this.insertData("client_wallets", apiKeyData);
            return this.makeResponse(201, "API keys generated successfully", apiKeyData);
        } catch (error: any) {
            console.error("Error generating API keys:", error);
            return this.makeResponse(500, "Server error", error.message);
        }
    }


    makeResponse(status: number, message: string, data: any = null) {
        const response: any = { status, message };
        if (data !== null) {
            response.data = data;
        }
        return response;
    }

    getRandomString() {
        return uuidv4().replace(/-/g, '');
    }

    getTransId() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 15; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }




    async mapDescription(description: string) {
        try {
            if (typeof description === "string" && description.includes("TARGET_AUTHORIZATION_ERROR")) {
                return StatusCodes.INSUFFICIENT_BALANCE.message
            }
            if (typeof description === "string" && description.includes("FAILED")) {
                return StatusCodes.NOT_AUTHORIZED.message
            }
            return description
        } catch (error: any) {
            return description
        }
    }

    async getPaymentMethodById(payment_method_id: any) {
        try {
            return await lr.getPaymentMethodById(payment_method_id);
        } catch (error: any) {
            console.error("Error in getPaymentMethodById:", error);
            return []
        }

    }

    async updateTransaction(trans_id: string, status: "SUCCESS" |"INITIATED"| "PENDING_APPROVAL" | "PROCESSING" | "PENDING_REVERSAL" | "FAILED" | "ONHOLD" | "PENDING" | "RECEIVED" | "MINT_INITIATED" | "MINT_FAILED", message: string, data: any = null) {
        try {
            this.saveTransactionLog(trans_id, status, Steps.UPDATE_BALANCE, 200, "UPDATING TRANSACTION", data);
            const transaction = await this.selectDataQuerySafe("transactions", { trans_id });
            if (transaction.length === 0) {
                this.saveTransactionLog(trans_id, status, Steps.UPDATE_BALANCE, 404, "Transaction not found", data);
                return this.makeResponse(404, "Transaction not found");
            }

            const { currency, client_id, asset_code, status: oldStatus } = transaction[0];
            if (oldStatus == "SUCCESS" || oldStatus == "FAILED") {
                return this.makeResponse(200, "Balance updated successfully", data);
            }

            const updated = {
                status: status,
            }
            await this.updateData(
                "transactions",
                `trans_id='${trans_id}'`,
                updated
            );

            try {
                const updatedMessage = {
                    message: `${message}`,
                }
                await this.updateData(
                    "transactions",
                    `trans_id='${trans_id}'`,
                    updatedMessage
                );
            } catch (error: any) {
                console.error("Error updating transaction message:", error);
            }




            // Import StellarService here to avoid circular dependency


            if (status == 'SUCCESS') {

                const { default: StellarService } = await import("./StellarService");
                const stellar = new StellarService();
                const balances = await stellar.getSingleBalance(client_id, asset_code);
                if (balances == null) {
                    return this.makeResponse(404, "Transaction not found");
                }
                const { balance } = balances;
                await this.updateData("transactions", `trans_id='${trans_id}'`, { "running_balance": balance, status: status, balance_updated_at: new Date().toISOString() }
                );
                this.updateBalance(trans_id, client_id, asset_code, status, balance, "END");
            }

            //   this.saveTransactionLog(trans_id, status, Steps.UPDATE_BALANCE, 200, "Balance updated successfully", data);
            return this.makeResponse(200, "Balance updated successfully");
        } catch (error: any) {
            console.error("Error updating current balance:", error);
            this.saveTransactionLog(trans_id, status, Steps.UPDATE_BALANCE_FAILED, 500, "Failed to update balance", error);
            return this.makeResponse(500, "Failed to update balance", error.message);
        }
    }

    async updateBalance(trans_id: string, client_id: string, asset_code: string, status: string, balance: any, type: string = "START") {
        try {
            if (type == "START") {
                this.insertData("balance_updates", { trans_id, client_id, asset_code, status, balance, type });
            } else {
                this.updateData("balance_updates", `trans_id='${trans_id}'`, { status, balance, balance_updated_at: new Date().toISOString() });
            }
        } catch (error: any) {
            console.error("Error updating balance:", error);
        }
        return true;
    }

    async sendAppNotification(userId: string, operation: string, name = '', otp = '') {
        console.log(`SEND_1`, { userId, operation });



        console.log(`SEND_2`, { userId, operation });

        const messageBody: any = await this.callQuerySafe(
            "SELECT * FROM notification_templates WHERE operation = ? AND channel != 'EMAIL'",
            [operation]
        );
        if (messageBody.length === 0) {
            console.log(`SEND_3`, messageBody);
            return this.makeResponse(404, "Operation not found");
        }

        const token = ""
        const message = messageBody[0]['body'];
        const subject = messageBody[0]['title'];

        const newMessage = this.constructSmsMessage(message, name, otp, "", "");
        const data = { title: subject, body: newMessage };

        console.log(`SEND_4`, data);

        const response = await sendNotification(token, data);
        console.log(`SEND_5`, response);

        return false;
    }

    sendDirectEmail(email: string, subject: string, message: string) {
        mailer.sendMail(email, subject, subject, message);
    }
    sendBalanceAlert(trans_id: string, service_name: any, chain: any, amount: string | number, currency: string, clientId: string | number) {
        const emails = ["<EMAIL>", "<EMAIL>"]
        for (const email of emails) {
            const message = `Hello, there is a balance alert for the following transaction:
            <p>Transaction ID: ${trans_id}</p>
            <p>Service Name: ${service_name}</p>
            <p>Chain: ${chain}</p>
            <p>Amount: ${amount}</p>
            <p>Currency: ${currency}</p>
            <p>Client ID: ${clientId}</p>
            <p>Please check the balance and approve the transaction</p>
            `
            this.sendDirectEmail(email, "BALANCE_ALERT", message)
        }

        return true
    }

    async notifiyAdmins(trans_id: any, operation: any) {
        const transaction = await this.selectDataQuerySafe("transactions", { trans_id })
        if (transaction.length === 0) {
            return this.makeResponse(404, "Transaction not found")
        }
        const { service_name, chain, amount, currency, clientId } = transaction[0]
        const emails = ["<EMAIL>", "<EMAIL>"]
        for (const email of emails) {
            this.sendEmail(operation, email, clientId, amount, [], "")
        }
        return true
    }


    async sendEmail(operation: string, email: string, name = "", otp = "", tableData: any = [], code: string = '') {
        try {
            const messageBody = await this.selectDataQuerySafe("notification_templates", { operation });
            if (messageBody.length === 0) {
                return this.makeResponse(404, "Operation not found");
            }

            let listHtml = "<ul>";
            tableData.forEach((item: any) => {
                listHtml += `<li>${item}</li>`;
            });
            listHtml += "</ul>";

            const message = messageBody[0]['body'];
            const subject = messageBody[0]['title'];

            const newMessage = this.constructSmsMessage(message, name, otp, listHtml, code);
            mailer.sendMail(email, subject, subject, newMessage);

            return true;
        } catch (error) {
            return this.makeResponse(203, "Error fetching company");
        }
    }

    constructSmsMessage(template: string, name: string, otp: string, listHtml: any, code: string): string {
        const data: any = { name, otp, code, listHtml };
        for (const key in data) {
            if (Object.prototype.hasOwnProperty.call(data, key)) {
                template = template.replace(new RegExp(`{${key}}`, 'g'), data[key]);
            }
        }
        return template;
    }

    generateRandom4DigitNumber() {
        return "10" + Math.floor(100000 + Math.random() * 900000);
    }

    generateRandomDigitNumber(length: number) {
        return Math.floor(10066000 + Math.random() * 90066000).toString().slice(0, length);
    }
    async getapikeys(clientId: string) {
        return await this.selectDataQuerySafe("api_keys", { client_id: clientId });
    }
    async getClientWallet(clientId: string) {
        const wallet = await this.selectDataQuerySafe("client_wallets", { client_id: clientId });
        return wallet

    }
    async getDecryptedWalletKey(clientId: string) {
        try {
            const apiKeyRecord = await this.selectDataQuerySafe("client_wallets", { client_id: clientId });
            if (apiKeyRecord.length === 0) {
                return this.createWallet(clientId);
            }


            const decryptedSecretKey = CryptoJS.AES.decrypt(apiKeyRecord[0].secret_key, SECRET_KEY)
                .toString(CryptoJS.enc.Utf8);
            // console.log(`encryptedSecretKey`, decryptedSecretKey, apiKeyRecord[0].secret_key, SECRET_KEY) // SECURITY: Removed private key logging

            return {
                client_id: clientId,
                public_key: apiKeyRecord[0].public_key,
                secret_key: decryptedSecretKey // This is now the hashed value
            }

        } catch (error: any) {
            console.error("Error retrieving API keys:", error);
            return null
        }
    }

    async decryptTradingAccount(clientId: string) {
        try {
            const tradingAccount = await this.selectDataQuerySafe("trading_accounts", { client_id: clientId });
            if (tradingAccount.length === 0) {
                return null;
            }

            const decryptedSecretKey = CryptoJS.AES.decrypt(tradingAccount[0].secret_key, SECRET_KEY)
                .toString(CryptoJS.enc.Utf8);

            return {
                client_id: clientId,
                public_key: tradingAccount[0].public_key,
                secret_key: decryptedSecretKey
            }
        } catch (error: any) {
            console.error("Error retrieving trading account keys:", error);
            return null
        }
    }

    async getBusinessByEmail(email: string) {
        return await this.selectDataQuerySafe("clients", { contact_email: email });
    }

    validateDomain(domain: string) {
        const domainRegex = /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        const cleanDomain = domain.replace(/^(?:https?:\/\/)?(?:www\.)?/, '').split('/')[0];
        return domainRegex.test(cleanDomain);
    }

    validateAndCleanDomain(domain: string) {
        return domain.replace(/^(?:https?:\/\/)?(?:www\.)?/, '').split('/')[0];
    }

    doesEmailDomainMatch(email: string, domain: string) {
        return email.split('@')[1] === domain;
    }

    async getClientInfo(clientId: string) {
        return await this.selectDataQuerySafe("clients", { client_id: clientId });
    }

    async getUserClientByUserId(userId: any) {
        return await this.selectDataQuerySafe(`client_logins`, { id: userId })
    }

    async getUserClientLogin(email: any) {
        return await this.selectDataQuerySafe(`client_logins`, { email })
    }

    async sendUserAuthToken(userId: string) {
        const user = await this.getUserClientByUserId(userId)
        console.log(`client3`, user)

        if (user.length === 0) {
            return false
        }
        const token = await this.getOtp(user[0].email, userId, 'code')
        this.sendEmail("AUTH_TOKEN", user[0].email, token, token);
        return this.makeResponse(200, `Token sent successfully to ${user[0].email}`);

    }

    async sendClientAuthToken(clientId: string) {
        const client = await this.getClientInfo(clientId)
        if (client.length === 0) {
            return false
        }
        const token = await this.getOtp(client[0].email, clientId, 'code')
        this.sendEmail("AUTH_TOKEN", client[0].email, token, token);
        return this.makeResponse(200, `Token sent successfully to ${client[0].email}`);

    }

    async getOtp(email: string, user_id: string, otpType: string = 'otp') {
        console.log(`client4`, email, user_id, otpType)

        const user: any = await this.selectDataQuerySafe("user_otp", { email, user_id });
        let otp = this.generateRandom4DigitNumber().toString();

        if (otpType === 'code') {
            otp = this.generateRandomDigitNumber(8);
        }

        console.log(`client5`, { email, otp });

        if (user.length === 0) {
            await this.insertData('user_otp', { user_id, email, otp });
        } else {
            await this.updateData('user_otp', `email = '${email}' and user_id = '${user_id}'`, { otp });
        }

        return otp;
    }

    async saveNotification(title: string, companyId: string, message: any) {
        const newNotification = { title, companyId, message };
        return await this.insertData('notifications', newNotification);
    }

    async getDocVerifiers(docId: string) {
        return await this.callQuerySafe(`SELECT * FROM verifiers WHERE doc_id = ?`, [docId]);
    }





    async confirmUser2Fa(data: any) {
        try {
            const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'admin' })
            const responseData: any = await TwoFactorAuthHelper.verifySecret(client[0]?.secret, data.token)
            if (!responseData?.status) {
                return { status: false, message: 'Invalid 2fa token' }
            }
            return { status: true, message: '' };
        } catch (error: any) {
            return { status: true, message: 'Invalid 2fa token' };
        }
    }

    /**
     * Secure error response - prevents verbose error information from being exposed
     * @param statusCode - HTTP status code
     * @param message - User-friendly error message
     * @param data - Optional data to include (never include error objects)
     * @returns Formatted response object
     */
    makeSecureResponse(statusCode: number, message: string, data?: any) {
        // Log the full error details internally for debugging
        if (data && typeof data === 'object' && data.error) {
            console.error('Internal error details:', data.error);
            // Remove sensitive error information from response
            delete data.error;
        }

        return this.makeResponse(statusCode, message, data);
    }

    /**
     * Handle errors securely without exposing internal details
     * @param error - The error object
     * @param context - Context where the error occurred
     * @param userMessage - User-friendly error message
     * @returns Secure error response
     */
    handleErrorSecurely(error: any, context: string, userMessage: string = "An error occurred") {
        // Log the full error internally for debugging
        console.error(`Error in ${context}:`, error);

        // Return a secure response without exposing error details
        return this.makeResponse(500, userMessage);
    }

    async sendEvent(eventType: string, transId: any, serviceName: string, assetCode: string) {
        try {
            const allowedAssets = ["USDT", "USDC"];
            if (!allowedAssets.includes(assetCode)) {
                return this.makeResponse(404, "Invalid asset code");
            }


            const transaction = await this.selectDataQuerySafe("transactions", { trans_id: transId });
            if (transaction.length === 0) {
                return this.makeResponse(404, "Transaction not found");
            }
            const { client_id, amount, trans_type, product_id, asset_code, service_name, source, destination, created_at, sender_account, receiver_account, reference_id, narration, currency, status, fee } = transaction[0];

            let isCrypto = false;
            if (!allowedAssets.includes(asset_code)) {
                isCrypto = true;
            }

            if (isCrypto) {
                const sc_transactions: any = await this.callQuerySafe("select  direction, state, hash from sc_transactions where refId = ?", [transId]);
                if (sc_transactions.length == 0) {
                    return this.makeResponse(404, "SC transaction not found");
                }
                const { direction, state, hash } = sc_transactions[0];


                const stableCoinTransaction: StableCoinTransaction = {
                    clientId: client_id,
                    refId: transId,
                    hash: hash,
                    direction,
                    state,
                    network: asset_code,
                    asset: asset_code,
                    asset_id: asset_code,
                    amount: amount,
                    source: source,
                    destination: destination,
                    createTime: created_at,
                    confirmTime: ""
                }
                walletEventPublisher.publishEvent(eventType, stableCoinTransaction);

            } else {
                const fiatTransaction: TransactionResponseInterface = {
                    trans_id: transId,
                    client_id: client_id,
                    service_name: service_name,
                    product_id: product_id,
                    trans_type: trans_type,
                    reference_id: reference_id,
                    amount: amount,
                    currency: currency,
                    sender_account: sender_account,
                    receiver_account: receiver_account,
                    status: status,
                    fee: fee,
                    created_at: created_at,
                    narration: narration
                }
                // Publish DEPOSIT_RECEIVED event
                walletEventPublisher.publishEvent(eventType, fiatTransaction);

            }

        } catch (eventError) {
            console.error('Failed to publish DEPOSIT_RECEIVED event:', eventError);
        }
    }
}
