/**
 * Phone Number Validation Utilities
 * Provides validation for different mobile network operators
 */

export interface PhoneValidationResult {
    isValid: boolean;
    operator?: string;
    country?: string;
}

export interface NetworkPrefix {
    prefix: string;
    operator: string;
    country: string;
}

/**
 * Tanzania phone number validation for TZS Collections
 * Supports only Airtel and Tigo networks
 */
export class TanzanianPhoneValidator {
    private static readonly AIRTEL_PREFIXES = ['25568', '25569', '25578'];
    private static readonly TIGO_PREFIXES = ['25565', '25567', '25571'];

    /**
     * Validates if a phone number is a supported Tanzanian network (Airtel or Tigo)
     * @param phoneNumber - The phone number to validate
     * @returns PhoneValidationResult with validation status and operator info
     */
    static validate(phoneNumber: string): PhoneValidationResult {
        // Remove any spaces, dashes, parentheses, or other formatting
        const cleanPhone = phoneNumber.replace(/[\s\-\(\)]/g, '');

        // Basic validation: must be numeric and proper length (12 digits for Tanzania: 255 + 9 digits)
        if (!/^\d+$/.test(cleanPhone)) {
            return { isValid: false };
        }

        // Must be exactly 12 digits for Tanzanian numbers (255 + 9 digits)
        if (cleanPhone.length !== 12) {
            return { isValid: false };
        }

        // Must start with 255 (Tanzania country code)
        if (!cleanPhone.startsWith('255')) {
            return { isValid: false };
        }

        // Check if phone starts with any Airtel prefix
        for (const prefix of this.AIRTEL_PREFIXES) {
            if (cleanPhone.startsWith(prefix)) {
                return {
                    isValid: true,
                    operator: 'AIRTEL',
                    country: 'TANZANIA'
                };
            }
        }

        // Check if phone starts with any Tigo prefix
        for (const prefix of this.TIGO_PREFIXES) {
            if (cleanPhone.startsWith(prefix)) {
                return {
                    isValid: true,
                    operator: 'TIGO',
                    country: 'TANZANIA'
                };
            }
        }

        return { isValid: false };
    }

    /**
     * Get supported prefixes for display purposes
     * @returns Object containing arrays of supported prefixes for each operator
     */
    static getSupportedPrefixes() {
        return {
            airtel: ["255 68x xxx xxx", "255 69x xxx xxx", "255 78x xxx xxx"],
            tigo: ["255 65x xxx xxx", "255 67x xxx xxx", "255 71x xxx xxx"]
        };
    }

    /**
     * Get error message for invalid phone numbers
     * @returns Standard error message for TZS Collections
     */
    static getErrorMessage(): string {
        return "Invalid phone number. Only Airtel and Tigo networks are supported for TZS Collections.";
    }
}

/**
 * Generic phone validator that can be extended for other countries/services
 */
export class PhoneValidator {
    /**
     * Validates Tanzanian phone numbers for TZS Collections
     * @param phoneNumber - The phone number to validate
     * @returns PhoneValidationResult
     */
    static validateTanzanian(phoneNumber: string): PhoneValidationResult {
        return TanzanianPhoneValidator.validate(phoneNumber);
    }

    // Future: Add other country validators here
    // static validateKenyan(phoneNumber: string): PhoneValidationResult { ... }
    // static validateUgandan(phoneNumber: string): PhoneValidationResult { ... }
}

export default PhoneValidator;
