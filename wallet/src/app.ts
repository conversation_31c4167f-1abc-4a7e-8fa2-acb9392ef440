import express, { Request, Response } from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';
import dotenv from 'dotenv';
import expressFileUpload from 'express-fileupload';
import stellarListner from './helpers/stellar.listner';
import CronService from './helpers/cron';
import transactions from './controllers/transactions';
// import walletRoutes from './routes/wallet';  // TypeScript import
import settings from './controllers/internal';  // TypeScript import
 import Test from './tests/index'
import walletEventPublisher from './utils/eventPublisher';
import { SweepTest } from './tests/sweep.test'
new Test()

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3005; // Default to 3000 if PORT is not in environment

app.use(cors());
app.use(expressFileUpload({}) as unknown as express.RequestHandler); // Initialize express-fileupload with proper type casting
app.use(bodyParser.json());
app.use(express.json({ limit: '50mb' })); // Increase JSON payload limit to 50 MB
app.use(express.urlencoded({ limit: '50mb', extended: true })); // Increase URL-encoded payload limit
app.set('trust proxy', true);

const cronService = new CronService()
new stellarListner()

app.use('/payment', transactions);
app.use('/wallet', settings);

// Manual balance check endpoint
app.post('/admin/check-balances', async (req: Request, res: Response) => {
  try {
    await cronService.manualBalanceCheck();
    res.status(200).json({ 
      success: true, 
      message: 'Balance check triggered successfully' 
    });
  } catch (error: any) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
});


app.use('/health', (req: Request, res: Response) => {
  res.status(200).json({ status: 'ok', service: 'wallet' });
});

app.listen(PORT, async () => {
  console.log(`Server is running on port ${PORT}`);
  
  // Initialize event publisher
  try {
    await walletEventPublisher.connect();
    console.log('✅ Wallet event publisher connected');
  } catch (error) {
    console.error('❌ Failed to connect wallet event publisher:', error);
  }
});
