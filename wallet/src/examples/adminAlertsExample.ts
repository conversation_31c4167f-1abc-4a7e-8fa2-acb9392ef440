/**
 * Admin Alerts Service - Usage Examples
 * 
 * This file demonstrates how to use the Admin Alerts Service from anywhere in your application.
 */

import { adminAlertsService } from '../services/adminAlerts.service';

// ========================================
// Example 1: Send Daily Balance Report
// ========================================
export async function sendDailyReport() {
    const balances = [
        { account: 'Main Wallet', currency: 'USDC', balance: 5000 },
        { account: 'Trading Account', currency: 'KES', balance: 250000 },
        { account: 'Reserve Wallet', currency: 'USDT', balance: 3000 },
        { account: 'Euro Account', currency: 'EUR', balance: 1200 }
    ];

    await adminAlertsService.sendAdminAlert('balance', balances);
    console.log('Daily balance report sent to admins');
}

// ========================================
// Example 2: Check Balance Thresholds
// ========================================
export async function checkLowBalances() {
    const balances = [
        { account: 'Main Wallet', currency: 'USDC', balance: 500 },  // Below threshold (1000)
        { account: 'KES Account', currency: 'KES', balance: 50000 }, // Below threshold (100000)
        { account: 'Safe Account', currency: 'USDT', balance: 5000 }  // Above threshold
    ];

    const lowBalances = await adminAlertsService.checkBalanceThresholds(balances);
    console.log(`Found ${lowBalances.length} accounts with low balances`);
    // This will automatically send an alert email if any balances are low
}

// ========================================
// Example 3: Send Error Alert
// ========================================
export async function handleError() {
    try {
        // Some operation that might fail
        throw new Error('Database connection timeout');
    } catch (error) {
        await adminAlertsService.sendErrorAlert(error, 'wallet-service');
        console.log('Error alert sent to admins');
    }
}

// ========================================
// Example 4: Use in Transaction Processing
// ========================================
export async function processTransaction(transaction: any) {
    try {
        // Process the transaction
        console.log('Processing transaction...');
        
        // After transaction, check if balance is low
        const currentBalance = 800; // Get from your database
        
        await adminAlertsService.checkBalanceThresholds([{
            account: transaction.account,
            currency: transaction.currency,
            balance: currentBalance
        }]);
        
        return { success: true };
    } catch (error) {
        // Send error alert
        await adminAlertsService.sendErrorAlert(error, 'transaction-processing');
        throw error;
    }
}

// ========================================
// Example 5: Use in API Endpoint
// ========================================
export async function withdrawalEndpoint(req: any, res: any) {
    try {
        const { amount, currency, account } = req.body;
        
        // Process withdrawal
        const newBalance = await processWithdrawal(amount, currency);
        
        // Check if balance is now below threshold
        await adminAlertsService.checkBalanceThresholds([{
            account: account,
            currency: currency,
            balance: newBalance
        }]);
        
        res.json({ success: true, newBalance });
    } catch (error: any) {
        await adminAlertsService.sendErrorAlert(error, 'withdrawal-api');
        res.status(500).json({ success: false, message: error.message });
    }
}

// ========================================
// Example 6: Manual Alert for Specific Scenario
// ========================================
export async function sendCustomAlert() {
    // Send a custom balance alert
    const criticalAccounts = [
        { account: 'Hot Wallet', currency: 'USDC', balance: 100 },
        { account: 'Payment Gateway', currency: 'USD', balance: 50 }
    ];

    await adminAlertsService.sendAdminAlert('threshold', criticalAccounts);
    console.log('Custom alert sent');
}

// ========================================
// Helper function placeholder
// ========================================
async function processWithdrawal(amount: number, currency: string): Promise<number> {
    // Your withdrawal logic here
    return 1000 - amount; // Example: return new balance
}

// ========================================
// How to run these examples:
// ========================================
// import { sendDailyReport, checkLowBalances, handleError } from './examples/adminAlertsExample';
// 
// sendDailyReport();
// checkLowBalances();
// handleError();

