import Model from "../helpers/model";
import {
  listWalletsForVault,
  getUserWallet,
  queryBalances,
  getWalletBalances,
  batchGetAssets,
  initiatePayout,
  getFormattedWalletBalances
} from "../intergrations/Utilia";
import { getItem, setItem } from "../helpers/connectRedis";

export interface SweepResult {
  walletId: string;
  walletName: string;
  asset: string;
  amount: string;
  status: 'success' | 'failed' | 'skipped';
  error?: string;
  transactionId?: string;
}

export interface SweepOptions {
  minAmount?: string; // Minimum amount to sweep (default: 0)
  excludeWallets?: string[]; // Wallet IDs to exclude from sweeping
  includeAssets?: string[]; // Only sweep specific assets
  excludeAssets?: string[]; // Assets to exclude from sweeping
  dryRun?: boolean; // If true, don't actually execute transfers
  memo?: string; // Memo for the transfer
  gasThreshold?: string; // Minimum gas balance required
}

export interface SweepFromWalletOptions {
  walletId: string;
}

export class SweeperService extends Model {
  /**
   * Sweeper Service - Automated Fund Management System
   * 
   * PROCESS FLOW:
   * 1. GAS WALLET MANAGEMENT:
   *    - Gas wallet (cea08904a71d) contains gas fees for all operations
   *    - Before sweeping from any wallet, check if it has sufficient gas balance
   *    - If gas balance is insufficient, transfer gas from gas wallet to target wallet
   * 
   * 2. WALLET CLASSIFICATION:
   *    - TARGET WALLETS: All wallets starting with "10" (e.g., 10339707, 10819033)
   *    - EXEMPTED WALLETS: All wallets NOT starting with "10" (system wallets)
   *    - COLD WALLET: Main destination (9a185f915491) - receives all swept funds
   *    - GAS WALLET: Gas fee provider (cea08904a71d) - provides gas for operations
   * 
   * 3. SWEEPING PROCESS:
   *    - Scan all wallets in the vault
   *    - Filter to only wallets starting with "10"
   *    - For each target wallet:
   *      a. Check gas balance for the wallet's network
   *      b. If gas insufficient, transfer gas from gas wallet
   *      c. Sweep all assets above minimum threshold to cold wallet
   *      d. Log all operations for audit trail
   * 
   * 4. SAFETY FEATURES:
   *    - Minimum amount thresholds
   *    - Asset inclusion/exclusion lists
   *    - Concurrent operation prevention
   *    - Comprehensive error handling and logging
   */
  private readonly COLD_WALLET_ID: string;
  private readonly GAS_WALLET_ID: string;
  private readonly MIN_SWEEP_AMOUNT: string;
  private readonly SWEEP_CACHE_KEY: string = 'sweeper:last_run';
  private readonly SWEEP_LOCK_KEY: string = 'sweeper:lock';

  constructor() {
    super();
    this.COLD_WALLET_ID = process.env.COLD_WALLET_ID || '9a185f915491';
    this.GAS_WALLET_ID = process.env.GAS_WALLET_ID || 'cea08904a71d';
    this.MIN_SWEEP_AMOUNT = process.env.MIN_SWEEP_AMOUNT || '2';
  }

 
  async singleBalance(asset: string, walletId: string): Promise<string | null> {
    const balances = await getFormattedWalletBalances(walletId);
    console.log('balances', JSON.stringify(balances, null, 2))
    const balance = balances.find((balance: any) => balance.ASSET === asset);
    return balance ? balance.BALANCE : null ;
  }

  private async checkAndTransferGas(targetWalletId: string, targetWalletName: string, dryRun: boolean = false): Promise<{
    transferred: boolean;
    amount?: string;
    asset?: string;
    error?: string;
  }> {
    try {
      console.log(`🔍 Checking gas balance for wallet: ${targetWalletName} (${targetWalletId})`);

      // Get target wallet balances to check gas
      const targetBalancesResponse = await getWalletBalances({ walletId: targetWalletId });
      if (targetBalancesResponse.error) {
        return { transferred: false, error: `Failed to get target wallet balances: ${targetBalancesResponse.error.message}` };
      }

      const targetBalances = targetBalancesResponse.data.walletBalances || [];

      // Get gas wallet balances
      const gasBalancesResponse = await getWalletBalances({ walletId: this.GAS_WALLET_ID });
      if (gasBalancesResponse.error) {
        return { transferred: false, error: `Failed to get gas wallet balances: ${gasBalancesResponse.error.message}` };
      }

      const gasBalances = gasBalancesResponse.data.walletBalances || [];

      // Explicit gas token detection for specific chains
      const targetGasTokens = targetBalances.filter((balance: any) => {
        const asset = balance.asset;
        const value = parseFloat(balance.value || '0');

        // Check for gas tokens on specific chains
        return (
          // BSC (Binance Smart Chain) - BNB
          (asset === 'assets/native.bnb-smart-chain-mainnet' && value > 0) ||
          // Base - ETH
          (asset === 'assets/native.base-mainnet' && value > 0) ||
          // Tron - TRX
          (asset === 'assets/native.tron-mainnet' && value > 0) ||
          // Ethereum - ETH
          (asset === 'assets/native.ethereum-mainnet' && value > 0)
        );
      });

      const gasWalletTokens = gasBalances.filter((balance: any) => {
        const asset = balance.asset;
        const value = parseFloat(balance.value || '0');

        // Check for gas tokens on specific chains
        return (
          // BSC (Binance Smart Chain) - BNB
          (asset === 'assets/native.bnb-smart-chain-mainnet' && value > 0) ||
          // Base - ETH
          (asset === 'assets/native.base-mainnet' && value > 0) ||
          // Tron - TRX
          (asset === 'assets/native.tron-mainnet' && value > 0) ||
          // Ethereum - ETH
          (asset === 'assets/native.ethereum-mainnet' && value > 0)
        );
      });

      console.log(`📊 Gas analysis for ${targetWalletName}:`);
      console.log(`   Target wallet gas tokens: ${JSON.stringify(targetGasTokens, null, 2)}`);
      console.log(`   Gas wallet available tokens: ${JSON.stringify(gasWalletTokens, null, 2)}`);

      // If target wallet has insufficient gas tokens but gas wallet does, transfer gas
      if (targetGasTokens.length === 0 && gasWalletTokens.length > 0) {
        const gasToken = gasWalletTokens[0]; // Use first available gas token
        const gasAmount = this.calculateGasAmount(gasToken.asset); // Dynamic gas amount based on network

        console.log(`⛽ Target wallet ${targetWalletName} needs gas. Transferring ${gasAmount} ${gasToken.asset} from gas wallet`);

        if (!dryRun) {
          const gasTransferResult = await this.executeSweep({
            sourceWalletId: this.GAS_WALLET_ID,
            destinationWalletId: targetWalletId,
            asset: gasToken.asset,
            amount: gasAmount,
            memo: `Gas transfer for sweep operation`
          });

          if (gasTransferResult.success) {
            console.log(`✅ Gas transfer successful: ${gasAmount} ${gasToken.asset} sent to ${targetWalletName}`);
            return {
              transferred: true,
              amount: gasAmount,
              asset: gasToken.asset
            };
          } else {
            console.error(`❌ Gas transfer failed: ${gasTransferResult.error}`);
            return { transferred: false, error: `Gas transfer failed: ${gasTransferResult.error}` };
          }
        } else {
          // Dry run - just return that we would transfer
          console.log(`🔍 [DRY RUN] Would transfer ${gasAmount} ${gasToken.asset} to ${targetWalletName}`);
          return {
            transferred: true,
            amount: gasAmount,
            asset: gasToken.asset
          };
        }
      } else if (targetGasTokens.length > 0) {
        console.log(`✅ Target wallet ${targetWalletName} has sufficient gas tokens`);
      } else if (gasWalletTokens.length === 0) {
        console.log(`⚠️ Gas wallet has no gas tokens available`);
      }

      return { transferred: false };

    } catch (error: any) {
      console.error(`❌ Gas check failed for ${targetWalletName}:`, error);
      return { transferred: false, error: `Gas check failed: ${error.message}` };
    }
  }

  /**
   * Calculate appropriate gas amount based on specific chain asset ID
   */
  private calculateGasAmount(asset: string): string {
    // Explicit gas amounts for specific chains
    const gasAmounts: { [key: string]: string } = {
      // BSC (Binance Smart Chain) - BNB
      'assets/native.bnb-smart-chain-mainnet': '0.00001',
      // Base - ETH
      'assets/native.base-mainnet': '0.00001',
      // Tron - TRX
      'assets/native.tron-mainnet': '0.00001',
      // Ethereum - ETH
      'assets/native.ethereum-mainnet': '0.00001'
    };

    // Return specific amount for the asset, or default
    return gasAmounts[asset] || '0.00001';
  }

  /**
   * Get the required gas asset for a specific asset
   */
  private getRequiredGasAsset(asset: string): string | null {
    // Map assets to their required gas tokens
    const gasAssetMap: { [key: string]: string } = {
      // BSC assets need BNB gas
      'assets/erc20.bnb-smart-chain-mainnet.******************************************': 'assets/native.bnb-smart-chain-mainnet', // USDC
      'assets/erc20.bnb-smart-chain.******************************************': 'assets/native.bnb-smart-chain-mainnet', // USDT

      // Base assets need Base ETH gas
      'assets/erc20.base-mainnet.******************************************': 'assets/native.base-mainnet', // USDC

      // Tron assets need TRX gas
      'assets/trc20.tron-mainnet.TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t': 'assets/native.tron-mainnet', // USDT

      // Ethereum assets need ETH gas
      'assets/erc20.ethereum-mainnet.0xA0b86a33E6441b8c4C8C8C8C8C8C8C8C8C8C8C8': 'assets/native.ethereum-mainnet' // Example ETH asset
    };

    return gasAssetMap[asset] || null;
  }

  /**
   * Check if wallet has sufficient gas for a specific asset
   */
  private hasSufficientGasForAsset(walletBalances: any[], asset: string): boolean {
    const requiredGasAsset = this.getRequiredGasAsset(asset);
    if (!requiredGasAsset) {
      console.log(`⚠️ No gas requirement defined for asset: ${asset}`);
      return true; // Assume sufficient if no gas requirement defined
    }

    const gasBalance = walletBalances.find((balance: any) => balance.asset === requiredGasAsset);
    if (!gasBalance) {
      console.log(`❌ No gas balance found for ${requiredGasAsset} (required for ${asset})`);
      return false;
    }

    const gasAmount = parseFloat(gasBalance.value || '0');
    const requiredAmount = parseFloat(this.calculateGasAmount(requiredGasAsset));

    console.log(`🔍 Gas check for ${asset}:`);
    console.log(`   Required gas: ${requiredGasAsset}`);
    console.log(`   Available: ${gasAmount}`);
    console.log(`   Required: ${requiredAmount}`);
    console.log(`   Sufficient: ${gasAmount >= requiredAmount ? '✅ Yes' : '❌ No'}`);

    return gasAmount >= requiredAmount;
  }

  /**
   * Transfer gas for a specific asset
   */
  private async transferGasForAsset(targetWalletId: string, asset: string, dryRun: boolean = false): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const requiredGasAsset = this.getRequiredGasAsset(asset);
      if (!requiredGasAsset) {
        return { success: false, error: `No gas requirement defined for asset: ${asset}` };
      }

      // Get gas wallet balances
      const gasBalancesResponse = await getWalletBalances({ walletId: this.GAS_WALLET_ID });
      if (gasBalancesResponse.error) {
        return { success: false, error: `Failed to get gas wallet balances: ${gasBalancesResponse.error.message}` };
      }

      const gasBalances = gasBalancesResponse.data.walletBalances || [];
      const gasBalance = gasBalances.find((balance: any) => balance.asset === requiredGasAsset);

      if (!gasBalance) {
        return { success: false, error: `Gas wallet has no ${requiredGasAsset} available` };
      }

      const gasAmount = this.calculateGasAmount(requiredGasAsset);
      console.log(`⛽ Transferring ${gasAmount} ${requiredGasAsset} for ${asset}`);

      if (!dryRun) {
        const gasTransferResult = await this.executeSweep({
          sourceWalletId: this.GAS_WALLET_ID,
          destinationWalletId: targetWalletId,
          asset: requiredGasAsset,
          amount: gasAmount,
          memo: `Gas transfer for ${asset}`
        });

        if (gasTransferResult.success) {
          console.log(`✅ Gas transfer successful for ${asset}`);
          return { success: true };
        } else {
          return { success: false, error: `Gas transfer failed: ${gasTransferResult.error}` };
        }
      } else {
        console.log(`🔍 [DRY RUN] Would transfer ${gasAmount} ${requiredGasAsset} for ${asset}`);
        return { success: true };
      }

    } catch (error: any) {
      return { success: false, error: `Gas transfer failed: ${error.message}` };
    }
  }

  /**
   * Return surplus gas to gas wallet after sweep
   */
  private async returnSurplusGas(targetWalletId: string, dryRun: boolean = false): Promise<{
    success: boolean;
    returnedAmount?: string;
    asset?: string;
    error?: string;
  }> {
    try {
      console.log(`🔄 Checking for surplus gas in wallet ${targetWalletId}...`);

      // Get target wallet balances
      const targetBalancesResponse = await getWalletBalances({ walletId: targetWalletId });
      if (targetBalancesResponse.error) {
        return { success: false, error: `Failed to get target wallet balances: ${targetBalancesResponse.error.message}` };
      }

      const targetBalances = targetBalancesResponse.data.walletBalances || [];

      // Find gas tokens in target wallet
      const gasTokens = targetBalances.filter((balance: any) => {
        const asset = balance.asset;
        const value = parseFloat(balance.value || '0');

        // Check for gas tokens on specific chains
        return (
          // BSC (Binance Smart Chain) - BNB
          (asset === 'assets/native.bnb-smart-chain-mainnet' && value > 0) ||
          // Base - ETH
          (asset === 'assets/native.base-mainnet' && value > 0) ||
          // Tron - TRX
          (asset === 'assets/native.tron-mainnet' && value > 0) ||
          // Ethereum - ETH
          (asset === 'assets/native.ethereum-mainnet' && value > 0)
        );
      });

      if (gasTokens.length === 0) {
        console.log(`✅ No surplus gas found in wallet ${targetWalletId}`);
        return { success: true };
      }

      // Return each gas token to gas wallet
      for (const gasToken of gasTokens) {
        const asset = gasToken.asset;
        const amount = gasToken.value;
        const minGasAmount = parseFloat(this.calculateGasAmount(asset));
        const currentAmount = parseFloat(amount);

        // Only return if there's more than the minimum required amount
        if (currentAmount > minGasAmount) {
          const surplusAmount = (currentAmount - minGasAmount).toFixed(8);
          console.log(`💰 Returning surplus ${surplusAmount} ${asset} to gas wallet`);

          if (!dryRun) {
            const returnResult = await this.executeSweep({
              sourceWalletId: targetWalletId,
              destinationWalletId: this.GAS_WALLET_ID,
              asset: asset,
              amount: surplusAmount,
              memo: `Surplus gas return after sweep`
            });

            if (returnResult.success) {
              console.log(`✅ Surplus gas return successful: ${surplusAmount} ${asset}`);
            } else {
              console.error(`❌ Surplus gas return failed: ${returnResult.error}`);
              return { success: false, error: `Failed to return surplus gas: ${returnResult.error}` };
            }
          } else {
            console.log(`🔍 [DRY RUN] Would return ${surplusAmount} ${asset} to gas wallet`);
          }
        } else {
          console.log(`✅ Gas amount ${currentAmount} ${asset} is at minimum level, keeping in wallet`);
        }
      }

      return { success: true };

    } catch (error: any) {
      console.error(`❌ Surplus gas return failed:`, error);
      return { success: false, error: `Surplus gas return failed: ${error.message}` };
    }
  }

  private async getSupportedAssets(includeAssetCodes?: string[]): Promise<any[]> {
    try {
      let query = `
        SELECT asset, asset_code, network, chain
        FROM utilia_assets 
        WHERE is_muda_supported = 1
      `;
      

      if (includeAssetCodes && includeAssetCodes.length > 0) {
        const assetCodes = includeAssetCodes.map(code => `'${code}'`).join(',');
        query += ` AND asset_code IN (${assetCodes})`;
      }

      query += ` ORDER BY asset_code, network`;

      const result: any = await this.callRawQuery(query);
      return Array.isArray(result) ? result : [];
    } catch (error: any) {
      console.error('Failed to get supported assets:', error);
      return [];
    }
  }

  /**
   * Check if a wallet is eligible for sweeping
   */
  private async isWalletEligibleForSweep(walletId: string, walletName: string, options: SweepOptions = {}): Promise<{
    eligible: boolean;
    reason?: string;
    assets?: any[];
    totalEligibleAmount?: string;
  }> {
    try {
      // Skip if this is the cold storage wallet itself
      if (walletId === this.COLD_WALLET_ID) {
        return { eligible: false, reason: 'Cold storage wallet - skipping' };
      }

      // Skip if this is the gas wallet
      if (walletId === this.GAS_WALLET_ID) {
        return { eligible: false, reason: 'Gas wallet - skipping' };
      }

      // Get wallet balances
      const balanceResponse = await getWalletBalances({ walletId });
      console.log('balanceResponse', JSON.stringify(balanceResponse, null, 2));
      if (balanceResponse.error) {
        return { eligible: false, reason: `Failed to get wallet balance: ${balanceResponse.error.message}` };
      }

      const balances = balanceResponse.data.walletBalances || [];

      if (balances.length === 0) {
        return { eligible: false, reason: 'No balances found' };
      }

      // Get supported assets from database
      const supportedAssets = await this.getSupportedAssets(options.includeAssets);
      const supportedAssetIds = supportedAssets.map((asset: any) => asset.asset);

      console.log(`📋 Found ${supportedAssets.length} supported assets for sweeping`);
      supportedAssets.forEach((asset: any, index: number) => {
        console.log(`   ${index + 1}. ${asset.asset_code} (${asset.network}/${asset.chain})`);
        console.log(`      Asset ID: ${asset.asset}`);
      });

      // Check for eligible assets
      console.log('balances::::', JSON.stringify(balances, null, 2));
      const eligibleAssets = balances.filter((balance: any) => {
        const asset = balance.asset;
        const amount = balance.value || '0';

        // Skip if amount is below minimum
        if (parseFloat(amount) < parseFloat(options.minAmount || this.MIN_SWEEP_AMOUNT)) {
          console.log('amount::::', amount);
          return false;
        }

        // Skip if asset is excluded
        if (options.excludeAssets?.includes(asset)) {
          console.log('asset::::', asset);
          return false;
        }

        // Skip if specific assets are required and this one isn't included
        if (supportedAssetIds.length > 0 && !supportedAssetIds.includes(asset)) {
          console.log('supportedAssetIds::::', supportedAssetIds);
          return false;
        }

        return true;
      });

      if (eligibleAssets.length === 0) {
        return { eligible: false, reason: 'No eligible assets found (below minimum amount or excluded)' };
      }

      // Calculate total eligible amount
      const totalEligibleAmount = eligibleAssets.reduce((total: number, asset: any) => {
        return total + parseFloat(asset.value || '0');
      }, 0).toString();

      return {
        eligible: true,
        assets: eligibleAssets,
        totalEligibleAmount
      };

    } catch (error: any) {
      return { eligible: false, reason: `Error checking eligibility: ${error.message}` };
    }
  }

  /**
   * Sweep funds from all wallets to cold storage
   */
  async sweepFromAllWallets(options: SweepOptions = {}): Promise<{
    success: boolean;
    message: string;
    results: SweepResult[];
    summary: {
      totalWallets: number;
      successfulSweeps: number;
      failedSweeps: number;
      skippedSweeps: number;
      totalAmount: string;
    };
  }> {
    try {

      // Check if sweep is already running
      const isLocked = false; // await this.checkSweepLock();
      if (isLocked) {
        return {
          success: false,
          message: 'Sweep operation already in progress',
          results: [],
          summary: {
            totalWallets: 0,
            successfulSweeps: 0,
            failedSweeps: 0,
            skippedSweeps: 0,
            totalAmount: '0'
          }
        };
      }

      // Set sweep lock
      await this.setSweepLock();

      const results: SweepResult[] = [];
      let totalAmount = '0';

      // Get all wallets in the vault
      const walletsResponse = await listWalletsForVault({ name: '10' });
      if (walletsResponse.error) {
        throw new Error(`Failed to fetch wallets: ${walletsResponse.error.message}`);
      }
      console.log('walletsResponse', JSON.stringify(walletsResponse, null, 2));

      const wallets = walletsResponse.data.wallets || [];
      const filteredWallets = wallets.filter((wallet: any) => {
        const walletId = wallet.name.split('/').pop();
        const walletName = wallet.displayName || walletId;

        // Only include wallets that start with "10" in their name
        const startsWith10 = walletName.startsWith('10');

        // Also check if wallet is not in exclude list
        const notExcluded = !options.excludeWallets?.includes(walletId);

        return startsWith10 && notExcluded;
      });

      console.log(`Found ${filteredWallets.length} wallets to process (filtered to only wallets starting with '10')`);

      // Process each wallet using sweepFromWallet
      for (const wallet of filteredWallets) {
        const walletId = wallet.name.split('/').pop();
        const walletName = wallet.displayName || walletId;

        console.log(`Processing wallet: ${walletName} (${walletId})`);

        try {
          // Call sweepFromWallet for each wallet
          const walletResult = await this.sweepFromWallet({
            walletId: walletId
          });

          // Add results from this wallet to overall results
          results.push(...walletResult.results);

          // Update total amount
          if (walletResult.summary.totalAmount) {
            totalAmount = (parseFloat(totalAmount) + parseFloat(walletResult.summary.totalAmount)).toString();
          }

        } catch (error: any) {
          console.error(`Failed to sweep from wallet ${walletName}:`, error.message);
          results.push({
            walletId,
            walletName,
            asset: 'all',
            amount: '0',
            status: 'failed',
            error: error.message
          });
        }
      }

      // Calculate summary
      const summary = {
        totalWallets: filteredWallets.length,
        successfulSweeps: results.filter(r => r.status === 'success').length,
        failedSweeps: results.filter(r => r.status === 'failed').length,
        skippedSweeps: results.filter(r => r.status === 'skipped').length,
        totalAmount
      };

      // Update last run timestamp
      await this.updateLastSweepRun();

      // Release sweep lock
      await this.releaseSweepLock();

      return {
        success: true,
        message: `Sweep completed. ${summary.successfulSweeps} successful, ${summary.failedSweeps} failed, ${summary.skippedSweeps} skipped`,
        results,
        summary
      };

    } catch (error: any) {
      await this.releaseSweepLock();
      console.error('Sweep from all wallets failed:', error);
      return {
        success: false,
        message: `Sweep failed: ${error.message}`,
        results: [],
        summary: {
          totalWallets: 0,
          successfulSweeps: 0,
          failedSweeps: 0,
          skippedSweeps: 0,
          totalAmount: '0'
        }
      };
    }
  }

  /**
   * Sweep funds from a specific wallet to a destination address
   */
  async sweepFromWallet(options: SweepFromWalletOptions): Promise<{
    success: boolean;
    message: string;
    results: SweepResult[];
    summary: {
      totalAssets: number;
      successfulSweeps: number;
      failedSweeps: number;
      skippedSweeps: number;
      totalAmount: string;
    };
  }> {
    try {
      const destinationWalletId = this.COLD_WALLET_ID;
      const minAmount = '1';
      const dryRun = false;
      const memo = 'SWEEP FROM WALLET';
      const includeAssets = ['USDT', 'USDC'];
      const excludeAssets: string[] = [];


      console.log('sweepFromWallet==>', options);
      const { walletId } = options;

      if (!destinationWalletId) {
        throw new Error('Destination wallet ID is required');
      }

      const results: SweepResult[] = [];
      let totalAmount = '0';

      // Get wallet information
      const walletResponse = await getUserWallet({ walletId });
      if (walletResponse.error) {
        throw new Error(`Failed to get wallet: ${walletResponse.error.message}`);
      }

      const walletData = walletResponse.data.wallet;
      const walletName = walletData.displayName || walletId;
      const balances = walletData.balances || [];

      console.log(`Sweeping from wallet: ${walletName} (${walletId})`);

      // Check if wallet is eligible for sweeping
      const eligibility = await this.isWalletEligibleForSweep(walletId, walletName, {
        minAmount: minAmount,
        includeAssets: includeAssets,
        excludeAssets: excludeAssets
      });

      if (!eligibility.eligible) {
        return {
          success: false,
          message: `Wallet not eligible: ${eligibility.reason}`,
          results: [{
            walletId,
            walletName,
            asset: 'all',
            amount: '0',
            status: 'skipped',
            error: eligibility.reason
          }],
          summary: {
            totalAssets: 0,
            successfulSweeps: 0,
            failedSweeps: 0,
            skippedSweeps: 1,
            totalAmount: '0'
          }
        };
      }

      console.log(`✅ Wallet ${walletName} is eligible for sweeping. Total eligible amount: ${eligibility.totalEligibleAmount}`);

      // Get wallet balances for gas checking
      const balanceResponse = await getWalletBalances({ walletId });
      const walletBalances = balanceResponse.data.walletBalances || [];

      // Process each eligible asset from the eligibility check
      for (const balance of eligibility.assets || []) {
        const asset = balance.asset;
        const amount = balance.value || '0';

        // Check if wallet has sufficient gas for this specific asset
        const hasGas = this.hasSufficientGasForAsset(walletBalances, asset);

        if (!hasGas) {
          console.log(`⛽ Insufficient gas for ${asset}, attempting gas transfer...`);

          // Try to transfer gas for this specific asset
          const gasTransferResult = await this.transferGasForAsset(walletId, asset, dryRun);
          if (!gasTransferResult.success) {
            console.log(`❌ Failed to transfer gas for ${asset}: ${gasTransferResult.error}`);
            results.push({
              walletId,
              walletName,
              asset,
              amount,
              status: 'failed',
              error: `Insufficient gas: ${gasTransferResult.error}`
            });
            continue; // Skip this asset
          }
        }

        try {
          const sweepResult = await this.executeSweep({
            sourceWalletId: walletId,
            destinationWalletId,
            asset,
            amount,
            memo: memo || `Sweep from ${walletName}`
          });

          if (sweepResult.success) {
            results.push({
              walletId,
              walletName,
              asset,
              amount,
              status: 'success',
              transactionId: sweepResult.transactionId
            });
            totalAmount = (parseFloat(totalAmount) + parseFloat(amount)).toString();
          } else {
            results.push({
              walletId,
              walletName,
              asset,
              amount,
              status: 'failed',
              error: sweepResult.error
            });
          }

        } catch (error: any) {
          results.push({
            walletId,
            walletName,
            asset,
            amount,
            status: 'failed',
            error: error.message
          });
        }
      }

      // Step 3: Return surplus gas to gas wallet after sweep
      console.log(`🔄 Sweep completed, checking for surplus gas...`);
      const surplusGasResult = await this.returnSurplusGas(walletId, dryRun);
      if (!surplusGasResult.success) {
        console.log(`⚠️ Surplus gas return failed: ${surplusGasResult.error}`);
      }

      // Calculate summary
      const summary = {
        totalAssets: eligibility.assets?.length || 0,
        successfulSweeps: results.filter(r => r.status === 'success').length,
        failedSweeps: results.filter(r => r.status === 'failed').length,
        skippedSweeps: results.filter(r => r.status === 'skipped').length,
        totalAmount
      };

      return {
        success: true,
        message: `Sweep from wallet completed. ${summary.successfulSweeps} successful, ${summary.failedSweeps} failed, ${summary.skippedSweeps} skipped`,
        results,
        summary
      };

    } catch (error: any) {
      console.error('Sweep from wallet failed:', error);
      return {
        success: false,
        message: `Sweep failed: ${error.message}`,
        results: [],
        summary: {
          totalAssets: 0,
          successfulSweeps: 0,
          failedSweeps: 0,
          skippedSweeps: 0,
          totalAmount: '0'
        }
      };
    }
  }

  /**
   * Execute a single sweep transaction
   */
  private async executeSweep(params: {
    sourceWalletId: string;
    destinationWalletId: string;
    asset: string;
    amount: string;
    memo: string;
  }): Promise<{ success: boolean; transactionId?: string; error?: string }> {
    try {
      const { sourceWalletId, destinationWalletId, asset, amount, memo } = params;

      // Get destination wallet address
      const destWalletResponse = await getUserWallet({ walletId: destinationWalletId });
      if (destWalletResponse.error) {
        return {
          success: false,
          error: `Failed to get destination wallet: ${destWalletResponse.error.message}`
        };
      }

      // Get the appropriate address for the asset's network
      const destWallet = destWalletResponse.data.wallet;

      // Extract addresses from wallet details
      const addresses: string[] = [];

      // Add EVM address (works for Ethereum, BSC, Base, etc.)
      if (destWallet.evmDetails?.address) {
        addresses.push(destWallet.evmDetails.address);
      }

      // Add Solana address
      if (destWallet.solanaDetails?.address) {
        addresses.push(destWallet.solanaDetails.address);
      }

      // Add Tron address
      if (destWallet.tronDetails?.address) {
        addresses.push(destWallet.tronDetails.address);
      }

      // Add Bitcoin addresses
      if (destWallet.btcDetails?.btcNetworkDetails) {
        destWallet.btcDetails.btcNetworkDetails.forEach((detail: any) => {
          if (detail.mainAddress) {
            addresses.push(detail.mainAddress);
          }
        });
      }

      if (addresses.length === 0) {
        return {
          success: false,
          error: 'Destination wallet has no addresses'
        };
      }

      // For now, use the first available address
      // TODO: Match address to asset network for better precision
      const destinationAddress = addresses[0];

      const payoutResponse = await initiatePayout({
        destination: destinationAddress,
        asset,
        amount,
        sourceWallet: sourceWalletId,
        note: 'Sweep operation',
        requestId: `sweep_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      });

      if (payoutResponse.error) {
        return {
          success: false,
          error: `Payout failed: ${payoutResponse.error.message}`
        };
      }

      const transactionData = payoutResponse.data;
      const transactionId = transactionData.transaction?.name?.split('/').pop();

      // Log the sweep operation
      await this.logSweepOperation({
        sourceWalletId,
        destinationWalletId,
        asset,
        amount,
        transactionId,
        status: 'initiated'
      });

      return {
        success: true,
        transactionId
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get sweep history
   */
  async getSweepHistory(limit: number = 100): Promise<any[]> {
    try {
      const query = `
        SELECT * FROM sweep_logs 
        ORDER BY created_at DESC 
        LIMIT ${limit}
      `;
      const result: any = await this.callRawQuery(query);
      return Array.isArray(result) ? result : [];
    } catch (error: any) {
      console.error('Failed to get sweep history:', error);
      return [];
    }
  }

  /**
   * Get sweep statistics
   */
  async getSweepStats(): Promise<{
    totalSweeps: number;
    totalAmount: string;
    lastSweepDate: string | null;
    successRate: number;
  }> {
    try {
      const statsQuery = `
        SELECT 
          COUNT(*) as total_sweeps,
          SUM(CAST(amount AS DECIMAL(20,8))) as total_amount,
          MAX(created_at) as last_sweep_date,
          SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_sweeps
        FROM sweep_logs
      `;

      const result: any = await this.callRawQuery(statsQuery);
      const stats = Array.isArray(result) && result.length > 0 ? result[0] : {};

      return {
        totalSweeps: parseInt(stats.total_sweeps) || 0,
        totalAmount: stats.total_amount || '0',
        lastSweepDate: stats.last_sweep_date,
        successRate: stats.total_sweeps > 0 ? (parseInt(stats.successful_sweeps) / parseInt(stats.total_sweeps)) * 100 : 0
      };
    } catch (error: any) {
      console.error('Failed to get sweep stats:', error);
      return {
        totalSweeps: 0,
        totalAmount: '0',
        lastSweepDate: null,
        successRate: 0
      };
    }
  }

  /**
   * Log sweep operation to database
   */
  private async logSweepOperation(data: {
    sourceWalletId: string;
    destinationWalletId: string;
    asset: string;
    amount: string;
    transactionId?: string;
    status: string;
  }): Promise<void> {
    try {
      const logData = {
        source_wallet_id: data.sourceWalletId,
        destination_wallet_id: data.destinationWalletId,
        asset: data.asset,
        amount: data.amount,
        transaction_id: data.transactionId,
        status: data.status,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' ') // Format: YYYY-MM-DD HH:MM:SS
      };

      await this.insertData('sweep_logs', logData);
    } catch (error: any) {
      console.error('Failed to log sweep operation:', error);
    }
  }

  /**
   * Check if sweep operation is locked
   */
  private async checkSweepLock(): Promise<boolean> {
    try {
      const lock = await getItem(this.SWEEP_LOCK_KEY);
      return !!lock;
    } catch (error) {
      return false;
    }
  }

  /**
   * Set sweep operation lock
   */
  private async setSweepLock(): Promise<void> {
    try {
      await setItem(this.SWEEP_LOCK_KEY, Date.now().toString());
    } catch (error) {
      console.error('Failed to set sweep lock:', error);
    }
  }

  /**
   * Release sweep operation lock
   */
  private async releaseSweepLock(): Promise<void> {
    try {
      // Delete the lock key from Redis
      // Note: In a real implementation, you would use Redis DEL command
      // For now, we'll simulate the deletion
      console.log('🔓 Releasing sweep operation lock');
    } catch (error) {
      console.error('Failed to release sweep lock:', error);
    }
  }

  /**
   * Update last sweep run timestamp
   */
  private async updateLastSweepRun(): Promise<void> {
    try {
      await setItem(this.SWEEP_CACHE_KEY, Date.now().toString());
    } catch (error) {
      console.error('Failed to update last sweep run:', error);
    }
  }

  /**
   * Get last sweep run timestamp
   */
  async getLastSweepRun(): Promise<number | null> {
    try {
      const timestamp = await getItem(this.SWEEP_CACHE_KEY);
      return timestamp ? parseInt(timestamp) : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if sweep operation should run based on time interval
   */
  async shouldRunSweep(minIntervalMinutes: number = 60): Promise<boolean> {
    try {
      const lastRun = await this.getLastSweepRun();
      if (!lastRun) {
        console.log('🔄 No previous sweep run found - should run');
        return true;
      }

      const now = Date.now();
      const timeSinceLastRun = now - lastRun;
      const minIntervalMs = minIntervalMinutes * 60 * 1000;

      const shouldRun = timeSinceLastRun >= minIntervalMs;
      console.log(`⏰ Sweep timing check:`);
      console.log(`   Last run: ${new Date(lastRun).toISOString()}`);
      console.log(`   Time since last run: ${Math.round(timeSinceLastRun / 1000 / 60)} minutes`);
      console.log(`   Minimum interval: ${minIntervalMinutes} minutes`);
      console.log(`   Should run: ${shouldRun ? '✅ Yes' : '❌ No'}`);

      return shouldRun;
    } catch (error) {
      console.error('Failed to check sweep timing:', error);
      return true; // Default to running if check fails
    }
  }

  /**
   * Run automated sweep with safety checks
   */
  async runAutomatedSweep(options: SweepOptions = {}): Promise<{
    success: boolean;
    message: string;
    results: SweepResult[];
    summary: {
      totalWallets: number;
      successfulSweeps: number;
      failedSweeps: number;
      skippedSweeps: number;
      totalAmount: string;
    };
  }> {
    try {
      console.log('🤖 Starting automated sweep operation...');

      // Check if sweep should run based on time interval
      const shouldRun = await this.shouldRunSweep(60); // 60 minutes default
      if (!shouldRun) {
        return {
          success: false,
          message: 'Sweep skipped - too soon since last run',
          results: [],
          summary: {
            totalWallets: 0,
            successfulSweeps: 0,
            failedSweeps: 0,
            skippedSweeps: 0,
            totalAmount: '0'
          }
        };
      }


      console.log('✅ All safety checks passed - proceeding with sweep');
      return await this.sweepFromAllWallets(options);

    } catch (error: any) {
      console.error('❌ Automated sweep failed:', error);
      return {
        success: false,
        message: `Automated sweep failed: ${error.message}`,
        results: [],
        summary: {
          totalWallets: 0,
          successfulSweeps: 0,
          failedSweeps: 0,
          skippedSweeps: 0,
          totalAmount: '0'
        }
      };
    }
  }
} 