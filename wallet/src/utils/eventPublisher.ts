/**
 * Simple Wallet Event Publisher
 */

import { WalletMessageBus, StableDepositEvent } from './messageBus';
import { StableCoinTransaction } from '../intergrations/interfaces';

class WalletEventPublisher {
    private messageBus = new WalletMessageBus();

    async connect() {
        await this.messageBus.connect();
    }

    async publishEvent(eventType: string, publishDepositReceived: any) {
        publishDepositReceived.eventType = eventType;
        return await this.messageBus.publishEvent(publishDepositReceived);
    }





    async close() {
        await this.messageBus.close();
    }
}

export const walletEventPublisher = new WalletEventPublisher();
export default walletEventPublisher;