import { CloudWatchLogsClient, PutLogEventsCommand, CreateLogGroupCommand, CreateLogStreamCommand, DescribeLogGroupsCommand } from '@aws-sdk/client-cloudwatch-logs';

interface LogEntry {
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  timestamp: string;
  service: string;
  userId?: string;
  requestId?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  error?: any;
  meta?: any;
}

class Logger {
  private client: CloudWatchLogsClient;
  private logGroupName: string;
  private logStreamName: string;
  private sequenceToken?: string;
  private logBuffer: LogEntry[] = [];
  private bufferSize = 10;
  private flushInterval = 5000;
  private flushTimer?: NodeJS.Timeout;
  private serviceName: string;

  constructor() {
    this.serviceName = 'wallet-service';
    
    // Initialize CloudWatch client
    this.client = new CloudWatchLogsClient({
      region: process.env.AWS_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      },
    });

    const env = process.env.TABLE_IDENTIFIER || 'dev';
    this.logGroupName = `/aws/muda/${env}`;
    this.logStreamName = `${this.serviceName}-${new Date().toISOString().split('T')[0]}-${Math.random().toString(36).substring(7)}`;
    
    this.initializeLogGroup();
    this.startFlushTimer();
  }

  private async initializeLogGroup() {
    try {
      const describeGroupsCommand = new DescribeLogGroupsCommand({
        logGroupNamePrefix: this.logGroupName,
      });
      
      const groups = await this.client.send(describeGroupsCommand);
      const groupExists = groups.logGroups?.some((group: any) => group.logGroupName === this.logGroupName);

      if (!groupExists) {
        const createGroupCommand = new CreateLogGroupCommand({
          logGroupName: this.logGroupName,
        });
        await this.client.send(createGroupCommand);
        console.log(`Created CloudWatch log group: ${this.logGroupName}`);
      }

      const createStreamCommand = new CreateLogStreamCommand({
        logGroupName: this.logGroupName,
        logStreamName: this.logStreamName,
      });
      await this.client.send(createStreamCommand);
      console.log(`Created CloudWatch log stream: ${this.logStreamName}`);

    } catch (error) {
      console.error('Failed to initialize CloudWatch:', error);
    }
  }

  private startFlushTimer() {
    this.flushTimer = setInterval(() => {
      this.flushLogs();
    }, this.flushInterval);
  }

  private async flushLogs() {
    if (this.logBuffer.length === 0) return;

    try {
      const allLogEvents = this.logBuffer.map(entry => ({
        message: JSON.stringify({
          level: entry.level,
          message: entry.message,
          service: entry.service,
          timestamp: entry.timestamp,
          userId: entry.userId,
          requestId: entry.requestId,
          method: entry.method,
          url: entry.url,
          statusCode: entry.statusCode,
          error: entry.error,
          meta: entry.meta,
        }),
        timestamp: new Date(entry.timestamp).getTime(),
      }));

      const command = new PutLogEventsCommand({
        logGroupName: this.logGroupName,
        logStreamName: this.logStreamName,
        logEvents: allLogEvents,
        sequenceToken: this.sequenceToken,
      });

      const response = await this.client.send(command);
      this.sequenceToken = response.nextSequenceToken;
      this.logBuffer = [];
    } catch (error) {
      console.error('Failed to flush logs to CloudWatch:', error);
    }
  }

  private log(level: 'error' | 'warn' | 'info' | 'debug', message: string, error?: any, meta?: any) {
    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      service: this.serviceName,
      error,
      meta,
    };

    // Always log to console
    const logMessage = `[${this.serviceName}] ${level.toUpperCase()}: ${message}`;
    switch (level) {
      case 'error':
        console.error(logMessage, error || '', meta || '');
        break;
      case 'warn':
        console.warn(logMessage, meta || '');
        break;
      case 'info':
        console.info(logMessage, meta || '');
        break;
      case 'debug':
        console.debug(logMessage, meta || '');
        break;
    }

    // Also send to CloudWatch
    this.logBuffer.push(entry);
    if (this.logBuffer.length >= this.bufferSize || level === 'error') {
      this.flushLogs();
    }
  }

  public error(message: string, error?: any, meta?: any) {
    this.log('error', message, error, meta);
  }

  public warn(message: string, meta?: any) {
    this.log('warn', message, undefined, meta);
  }

  public info(message: string, meta?: any) {
    this.log('info', message, undefined, meta);
  }

  public debug(message: string, meta?: any) {
    this.log('debug', message, undefined, meta);
  }

  public async shutdown() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    await this.flushLogs();
  }
}

// Create singleton instance
const logger = new Logger();

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  await logger.shutdown();
});

process.on('SIGINT', async () => {
  await logger.shutdown();
});

export default logger;