/**
 * Simple Wallet Event Consumer
 */

import { WalletMessageBus, TradingEvent } from './messageBus';

class WalletEventConsumer {
  private messageBus = new WalletMessageBus();

  async connect() {
    await this.messageBus.connect();
  }

  async startConsuming(onEvent: (event: TradingEvent) => void) {
    await this.messageBus.consumeEvents(onEvent);
  }

  async close() {
    await this.messageBus.close();
  }
}

export const walletEventConsumer = new WalletEventConsumer();
export default walletEventConsumer;