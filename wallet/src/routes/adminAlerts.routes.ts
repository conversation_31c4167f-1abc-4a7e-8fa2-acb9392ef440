import { Router } from 'express';
import { adminAlertsService } from '../services/adminAlerts.service';
import Model from '../helpers/model';

const router = Router();
const model = new Model();

// Send daily balance report
router.post('/daily-balance-report', async (req, res) => {
    try {
        // Get all account balances from your database
        const balances = await model.selectDataQuerySafe("account_balances", {});
        
        // Send the daily report
        await adminAlertsService.sendAdminAlert('balance', balances);
        
        // Check for low balances
        const lowBalances = await adminAlertsService.checkBalanceThresholds(balances);
        
        res.json({
            success: true,
            message: 'Daily balance report sent',
            totalAccounts: balances.length,
            lowBalanceAccounts: lowBalances.length
        });
    } catch (error: any) {
        await adminAlertsService.sendErrorAlert(error, 'wallet-admin-alerts');
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// Check balance thresholds and send alerts if needed
router.post('/check-thresholds', async (req, res) => {
    try {
        const balances = req.body.balances || await model.selectDataQuerySafe("account_balances", {});
        const lowBalances = await adminAlertsService.checkBalanceThresholds(balances);
        
        res.json({
            success: true,
            lowBalances,
            count: lowBalances.length
        });
    } catch (error: any) {
        await adminAlertsService.sendErrorAlert(error, 'wallet-admin-alerts');
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// Send error alert
router.post('/error-alert', async (req, res) => {
    try {
        const { error, service } = req.body;
        await adminAlertsService.sendErrorAlert(error, service);
        
        res.json({
            success: true,
            message: 'Error alert sent'
        });
    } catch (err: any) {
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});

// Manual balance alert (can be used from anywhere)
router.post('/send-balance-alert', async (req, res) => {
    try {
        const { balances } = req.body;
        await adminAlertsService.sendAdminAlert('balance', balances);
        
        res.json({
            success: true,
            message: 'Balance alert sent'
        });
    } catch (error: any) {
        await adminAlertsService.sendErrorAlert(error, 'wallet-admin-alerts');
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

export default router;

