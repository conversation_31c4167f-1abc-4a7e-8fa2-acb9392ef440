// codes message: "SUCCE<PERSON>" | "PENDING_REVERSAL" | "FAILED" | "ONHOLD" | "PENDING" | "RECEIVED" | "MINT_INITIATED" | "MINT_FAILED"
// codes status: "SUCCESS" | "PENDING_REVERSAL" | "FAILED" | "ONHOLD" | "PENDING" | "RECEIVED" | "MINT_INITIATED" | "MINT_FAILED"
// code number: 200, 400, 202, 203, 204, 205

/**
 * Payment Status Interface
 * Defines the possible states of a payment transaction
 */
export interface PaymentStatus {
  /** The payment is newly created */
  new: "new";
  /** The payment is successful */
  success: "success";
  /** The payment is pending */
  pending: "pending";
  /** The payment has failed */
  failed: "failed";
  /** The payment has been refunded */
  refunded: "refunded";
  /** The payment has been cancelled */
  cancelled: "cancelled";
}

/**
 * Payment Status Type
 * Union type for payment status values
 */
export type PaymentStatusType = PaymentStatus[keyof PaymentStatus];
export const StatusCodes = {
  SUCCESS: {
    code: 200,
    message: "TRANSACTION SUCCESSFUL"
  },
  FAILED: {
    code: 400,
    message: "TRANSACTION FAILED"
  },
  PENDING: {
    code: 202,
    message: "TRANSACTION PENDING"
  },
  ONHOLD: {
    code: 203,
    message: "TRANSACTION ON HOLD"
  },
  PENDING_REVERSAL: {
    code: 204,
    message: "TRANSACTION PENDING REVERSAL"
  },
  RECEIVED: {
    code: 205,
    message: "TRANSACTION INITIATED"
  },
  MINT_INITIATED: {
    code: 206,
    message: "TRANSACTION MINT INITIATED"
  },
  MINT_FAILED: {
    code: 207,
    message: "TRANSACTION MINT FAILED"
  },
  ERROR: {
    code: 500,
    message: "TRANSACTION ERROR"
  },
  NOT_AUTHORIZED: {
    code: 401,
    message: "CUSTOMER DID NOT AUTHORIZE TRANSACTION"
  },
  INSUFFICIENT_BALANCE: {
    code: 405,
    message: "CUSTOMER BALANCE IS INSUFFICIENT"
  },
  INVALID_TRANSACTION: { 
    code: 406,
    message: "INVALID TRANSACTION"
  },
  INVALID_EVENT_TYPE: {
    code: 407,
    message: "INVALID EVENT TYPE"
  },
  EXPIRED: {
    code: 408,
    message: "TRANSACTION EXPIRED"
  },
  TRANSACTION_TIMEOUT: {
    code: 409,
    message: "CUSTOMER DID NOT AUTHORIZE TRANSACTION IN TIME "
  }
}

export interface StableCoinTransaction {
  clientId: string;
  refId: string;
  hash: string;
  direction: "INCOMING" | "OUTGOING";
  state: string;
  network: string;
  asset: string;
  asset_id: string;
  amount: string; // or number if you convert it before saving
  source: string;
  destination: string;
  createTime: string;   // ISO datetime string
  confirmTime?: string; // Optional, in case it's not yet confirmed
  chain?: string;
}

export interface TransactionStatementInterface {
  id: string;
  client_id: string;
  product_id: string;
  trans_type: string;
  trans_id: string;
  reference_id: string;
  amount: string;
  asset_code: string;
  currency: string;
  sender_account: string;
  receiver_account: string;
  memo: string;
  status: string;
  fee: string;
  service_name: string;
  running_balance: string;
  created_at: string;
  payment_method_id: string
  cryptoTransaction?: CryptoTransactionInterface
}
export interface CryptoTransactionInterface {
  refId: string;
  hash: string;
  direction: string;
  network: string;
  asset_id: string;
  amount: string;
  source: string;
  destination: string;
  createTime: string;
  asset_code: string;
  asset: string;
  is_muda_supported: string;
  explorer_url: string;
}

export interface TransactionResponseInterface {
  trans_id: string;
  client_id: string;
  service_name: string;
  product_id: string;
  trans_type: "PULL" | "PUSH";
  reference_id: string;
  amount: string;
  currency: string;
  sender_account: string;
  receiver_account: string;
  status: string;
  fee: string;
  created_at: string;
  narration: string;
}
export interface UtiliaInterface {
  name: string;
  type: string;
  subType: "TOKEN_TRANSFER";
  state: "CONFIRMED";
  note: string;
  network: string;
  hash: string;
  signingSession: string;
  transfers: Array<{
    amount: string;
    asset: string;
    miningPrice: {
      amount: string;
      currencyCode: string;
    };
    sourceAddress: {
      value: string;
    };
    destinationAddress: {
      value: string;
    };
  }>;
  tokenAllowances: any[];
  designatedSigners: any[];
  createTime: string;
  mineTime: string;
  confirmTime: string;
  evmTransaction?: any;
  direction: "INCOMING" | "OUTGOING";
}
