/**
 * Manual Balance Check Test
 * 
 * This script triggers a manual balance check to test the alert system.
 * Run with: ts-node test-balance-check.ts
 */

import dotenv from 'dotenv';
import InternalModel from './src/models/internal';
import { adminAlertsService } from './src/services/adminAlerts.service';

// Load environment variables
dotenv.config();

async function testBalanceCheck() {
    console.log('='.repeat(60));
    console.log('🔍 Manual Balance Check Test');
    console.log('='.repeat(60));
    console.log('');

    try {
        const internalModel = new InternalModel();

        // Step 1: Fetch all balances
        console.log('📊 Step 1: Fetching all wallet balances...');
        const response = await internalModel.balanceManagement();
        
        if (response.statusCode !== 200) {
            throw new Error('Failed to fetch balances: ' + response.message);
        }

        const balances = response.data;
        console.log('✅ Balances fetched successfully');
        console.log('');

        // Step 2: Format balances
        console.log('🔄 Step 2: Formatting balances...');
        const formattedBalances = formatBalancesForAlert(balances);
        console.log(`✅ Found ${formattedBalances.length} accounts`);
        console.log('');

        // Step 3: Display all balances
        console.log('💰 Step 3: Current Balances:');
        console.log('-'.repeat(60));
        formattedBalances.forEach((bal: any) => {
            console.log(`   ${bal.account.padEnd(40)} ${bal.currency.padEnd(8)} ${Number(bal.balance).toLocaleString()}`);
        });
        console.log('');

        // Step 4: Check thresholds
        console.log('⚖️  Step 4: Checking against thresholds...');
        const lowBalances = await adminAlertsService.checkBalanceThresholds(formattedBalances);
        
        if (lowBalances.length > 0) {
            console.log(`⚠️  WARNING: Found ${lowBalances.length} accounts with LOW BALANCES:`);
            console.log('-'.repeat(60));
            lowBalances.forEach((bal: any) => {
                console.log(`   ⚠️  ${bal.account.padEnd(40)}`);
                console.log(`       Currency: ${bal.currency}`);
                console.log(`       Current:  ${Number(bal.balance).toLocaleString()}`);
                console.log(`       Threshold: ${Number(bal.threshold).toLocaleString()}`);
                console.log('');
            });
            console.log('📧 Email alerts have been sent to admins!');
        } else {
            console.log('✅ All balances are ABOVE thresholds - No alerts needed');
        }
        console.log('');

        // Step 5: Summary
        console.log('='.repeat(60));
        console.log('📋 Summary:');
        console.log(`   Total Accounts: ${formattedBalances.length}`);
        console.log(`   Low Balances: ${lowBalances.length}`);
        console.log(`   Status: ${lowBalances.length > 0 ? '⚠️  ACTION REQUIRED' : '✅ ALL GOOD'}`);
        console.log('='.repeat(60));
        console.log('');
        console.log('✅ Balance check completed successfully!');

    } catch (error: any) {
        console.error('');
        console.error('❌ ERROR:', error.message);
        console.error('');
        
        // Send error alert
        await adminAlertsService.sendErrorAlert(error, 'balance-check-test');
        console.log('📧 Error alert sent to admins');
    }

    // Exit process
    process.exit(0);
}

function formatBalancesForAlert(balances: any): any[] {
    const formatted: any[] = [];

    try {
        // Process Utila balances
        if (balances.utilaBalances) {
            // Gas wallet balances
            if (balances.utilaBalances.gasBalances && Array.isArray(balances.utilaBalances.gasBalances)) {
                balances.utilaBalances.gasBalances.forEach((bal: any) => {
                    formatted.push({
                        account: `Utila Gas Wallet (${balances.walletIds?.gas || 'N/A'})`,
                        currency: bal.asset_code || bal.currency,
                        balance: parseFloat(bal.balance || '0')
                    });
                });
            }

            // Cold wallet balances
            if (balances.utilaBalances.coldBalances && Array.isArray(balances.utilaBalances.coldBalances)) {
                balances.utilaBalances.coldBalances.forEach((bal: any) => {
                    formatted.push({
                        account: `Utila Cold Wallet (${balances.walletIds?.cold || 'N/A'})`,
                        currency: bal.asset_code || bal.currency,
                        balance: parseFloat(bal.balance || '0')
                    });
                });
            }

            // Payout wallet balances
            if (balances.utilaBalances.payoutBalances && Array.isArray(balances.utilaBalances.payoutBalances)) {
                balances.utilaBalances.payoutBalances.forEach((bal: any) => {
                    formatted.push({
                        account: `Utila Payout Wallet (${balances.walletIds?.payout || 'N/A'})`,
                        currency: bal.asset_code || bal.currency,
                        balance: parseFloat(bal.balance || '0')
                    });
                });
            }

            // Main Utila balances
            if (balances.utilaBalances.utilaBalances && Array.isArray(balances.utilaBalances.utilaBalances)) {
                balances.utilaBalances.utilaBalances.forEach((bal: any) => {
                    formatted.push({
                        account: 'Utila Main Wallet',
                        currency: bal.asset_code || bal.currency,
                        balance: parseFloat(bal.balance || '0')
                    });
                });
            }
        }

        // Process PegPay balances
        if (balances.pegPay) {
            if (balances.pegPay.pull) {
                formatted.push({
                    account: 'PegPay Pull',
                    currency: 'KES',
                    balance: parseFloat(balances.pegPay.pull || '0')
                });
            }

            if (balances.pegPay.push) {
                formatted.push({
                    account: 'PegPay Push',
                    currency: 'KES',
                    balance: parseFloat(balances.pegPay.push || '0')
                });
            }
        }

    } catch (error: any) {
        console.error('Error formatting balances:', error);
    }

    return formatted;
}

// Run the test
testBalanceCheck();

