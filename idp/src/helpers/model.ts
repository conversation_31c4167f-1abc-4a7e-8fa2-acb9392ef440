import BaseModel from "./base.model";
import { get } from "./httpRequest";
import { v4 as uuidv4 } from 'uuid';
import EmailSender from './email';
import CryptoJS from "crypto-js";
import StellarSdk from "stellar-sdk";

import jwt from "jsonwebtoken";
const SECRET_KEY = process.env.SECRET_KEY || "DQSJTOOZWCZY2F32762NZRSOD64Y6Q7W"

import { sendNotification } from "./FCM";

const mailer = new EmailSender();

export default class Model extends BaseModel {
    async GetIssuerAccount(asset_code: string, arg1: string) {
        return process.env.STELLAR_PAYOUT_ISSUER_SECRET
    }

    async createWallet(clientId: string) {
        try {
            const keys: any = await this.callRawQuery(`SELECT * FROM client_wallets WHERE client_wallets='${clientId}'`)
            if (keys.length > 0) {
                return keys[0]
            }
            const keypair = StellarSdk.Keypair.random();
            const publicKey = keypair.publicKey();
            const secretKey = keypair.secret();
            const encryptedSecretKey = CryptoJS.AES.encrypt(secretKey, SECRET_KEY).toString();
            const decryptedSecretKey = CryptoJS.AES.decrypt(encryptedSecretKey, SECRET_KEY).toString(CryptoJS.enc.Utf8);
            console.log(`decSSecretKey`, decryptedSecretKey)
            const apiKey = uuidv4();
            const apiKeyData = {
                client_id: clientId,
                public_key: publicKey,
                secret_key: encryptedSecretKey,
            };

            await this.insertData("client_wallets", apiKeyData);
            return this.makeResponse(201, "API keys generated successfully", apiKeyData);
        } catch (error: any) {
            console.error("Error generating API keys:", error);
            return this.makeResponse(500, "Server error", error.message);
        }
    }

    makeResponse(status: number, message: string, data: any = null) {
        const response: any = { status, message };
        if (data !== null) {
            response.data = data;
        }
        return response;
    }

    getRandomString() {
        return uuidv4().replace(/-/g, '');
    }

    async sendAppNotification(userId: string, operation: string, name = '', otp = '') {
        console.log(`SEND_1`, { userId, operation });



        console.log(`SEND_2`, { userId, operation });

        const messageBody = await this.callQuerySafe("SELECT * FROM notification_templates WHERE operation = ? AND channel != 'EMAIL'", [operation]);
        if (messageBody.length === 0) {
            console.log(`SEND_3`, messageBody);
            return this.makeResponse(404, "Operation not found");
        }

        const token = ""
        const message = messageBody[0]['body'];
        const subject = messageBody[0]['title'];

        const newMessage = this.constructSmsMessage(message, name, otp, "", "");
        const data = { title: subject, body: newMessage };

        console.log(`SEND_4`, data);

        const response = await sendNotification(token, data);
        console.log(`SEND_5`, response);

        return false;
    }

    async sendEmail(operation: string, email: string, name = "", otp = "", tableData: any = [], code: string = '') {
        try {
            const messageBody = await this.selectDataQuerySafe("notification_templates", { operation });
            if (messageBody.length === 0) {
                return this.makeResponse(404, "Operation not found");
            }

            let listHtml = "<ul>";
            tableData.forEach((item: any) => {
                listHtml += `<li>${item}</li>`;
            });
            listHtml += "</ul>";

            const message = messageBody[0]['body'];
            const subject = messageBody[0]['title'];

            const newMessage = this.constructSmsMessage(message, name, otp, listHtml, code);
            mailer.sendMail(email, subject, subject, newMessage);

            return true;
        } catch (error) {
            return this.makeResponse(203, "Error fetching company");
        }
    }

    constructSmsMessage(template: string, name: string, otp: string, listHtml: any, code: string): string {
        const data: any = { name, otp, code, listHtml };
        for (const key in data) {
            if (Object.prototype.hasOwnProperty.call(data, key)) {
                template = template.replace(new RegExp(`{${key}}`, 'g'), data[key]);
            }
        }
        return template;
    }

    generateRandom4DigitNumber() {
        return "10" + Math.floor(100000 + Math.random() * 900000);
    }

    generateRandomDigitNumber() {
        return "BV" + Math.floor(10066000 + Math.random() * 90066000);
    }
    async getapikeys(clientId: string) {
        return await this.selectDataQuerySafe("api_keys", { client_id: clientId });
    }
    async getDecryptedApiKey(clientId: string) {
        try {
            const apiKeyRecord = await this.selectDataQuerySafe("client_wallets", { client_id: clientId });
            if (apiKeyRecord.length === 0) {
                return null
            }

            // Since we're now using SHA-256 hashing instead of AES encryption,
            // we can't decrypt the secret key. Instead, we'll return the hashed value
            // which will be used for comparison purposes only.
            const decryptedSecretKey = CryptoJS.AES.decrypt(apiKeyRecord[0].secret_key, SECRET_KEY)
                .toString(CryptoJS.enc.Utf8);
            console.log(`encryptedSecretKey`, decryptedSecretKey, apiKeyRecord[0].secret_key, SECRET_KEY)

            return {
                client_id: clientId,
                public_key: apiKeyRecord[0].public_key,
                secret_key: decryptedSecretKey // This is now the hashed value
            }

        } catch (error: any) {
            console.error("Error retrieving API keys:", error);
            return null
        }
    }

    async getBusinessByEmail(email: string) {
        return await this.selectDataQuerySafe("clients", { contact_email: email });
    }

    validateDomain(domain: string) {
        const domainRegex = /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        const cleanDomain = domain.replace(/^(?:https?:\/\/)?(?:www\.)?/, '').split('/')[0];
        return domainRegex.test(cleanDomain);
    }

    validateAndCleanDomain(domain: string) {
        return domain.replace(/^(?:https?:\/\/)?(?:www\.)?/, '').split('/')[0];
    }

    doesEmailDomainMatch(email: string, domain: string) {
        return email.split('@')[1] === domain;
    }

    async getClientInfo(clientId: string) {
        return await this.selectDataQuerySafe("clients", { client_id: clientId });
    }
    async getOtp(email: string, user_id:string, otpType: string = 'otp') {
        const user: any = await this.selectDataQuerySafe("user_otp", { email });
        let otp = this.generateRandom4DigitNumber().toString();

        if (otpType === 'code') {
            otp = this.getRandomString();
        }

        console.log(`userInfo`, { email, otp });

        if (user.length === 0) {
            await this.insertData('user_otp', {user_id, email, otp });
        } else {
            await this.updateData('user_otp', `email = '${email}'`, { email, otp });
        }

        return otp;
    }

    async saveNotification(title: string, companyId: string, message: any) {
        const newNotification = { title, companyId, message };
        return await this.insertData('notifications', newNotification);
    }

    async getDocVerifiers(docId: string) {
        return await this.callRawQuery(`SELECT * FROM verifiers WHERE doc_id='${docId}'`);
    }

   
}
