#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running type checks before push..."

# Function to run typecheck and only fail on critical errors
run_typecheck() {
  local service=$1
  echo "  Checking $service..."
  
  cd "$service" || return 0
  
  # Run typecheck and capture output
  OUTPUT=$(npx tsc --noEmit --skipLibCheck 2>&1)
  EXIT_CODE=$?
  
  # If typecheck passed, we're good
  if [ $EXIT_CODE -eq 0 ]; then
    echo "  ✅ $service passed"
    cd ..
    return 0
  fi
  
  # Filter out non-critical errors:
  # - test files, entities, examples
  # - missing controllers/routes/integrations (optional modules)
  # - TS18046 (type 'unknown' - acceptable in many cases)
  CRITICAL_ERRORS=$(echo "$OUTPUT" | \
    grep -v "src/tests/" | \
    grep -v "src/entities/" | \
    grep -v "src/examples/" | \
    grep -v "controllers/" | \
    grep -v "intergrations/" | \
    grep -v "routes/" | \
    grep -v "error TS2307" | \
    grep -v "error TS18046" | \
    grep -v "error TS1192" | \
    grep "error TS")
  
  if [ -z "$CRITICAL_ERRORS" ]; then
    echo "  ⚠️  $service has non-critical errors - allowing push"
    cd ..
    return 0
  fi
  
  # We have critical errors in actual source code
  echo "  ❌ $service has critical errors:"
  echo "$CRITICAL_ERRORS" | head -20
  cd ..
  return 1
}

# Track if any service has critical errors
HAS_CRITICAL_ERRORS=0

# Check each service
for service in wallet admin gateway liquidityRailAdmin stellarService trading idp; do
  if [ -d "$service" ]; then
    if ! run_typecheck "$service"; then
      HAS_CRITICAL_ERRORS=1
    fi
  fi
done

# Final decision
if [ $HAS_CRITICAL_ERRORS -eq 1 ]; then
  echo ""
  echo "❌ Critical TypeScript errors found in source code!"
  echo "   Fix these errors or use 'git push --no-verify' to skip checks"
  exit 1
fi

echo ""
echo "✅ All critical type checks passed!"
exit 0
