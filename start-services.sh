#!/bin/bash

echo "🚀 Starting Muda Services with Docker Compose"
echo "============================================="

# Navigate to the backend directory
cd /Users/<USER>/work/muda/aggregator/backend

# Start all services
echo "📦 Starting all services..."
docker-compose up -d --build

echo "⏳ Waiting for services to start..."
sleep 10

echo "🔍 Checking service status..."
docker-compose ps

echo ""
echo " Services started! You can now:"
echo "  - Trading Service: http://localhost:3006"
echo "  - Wallet Service: http://localhost:3005" 
echo "  - LR Service: http://localhost:8032"
echo "  - RabbitMQ Management: http://localhost:15672"
echo ""
echo "🧪 Run the test:"
echo "  cd trading && node run-test.js"
echo ""
