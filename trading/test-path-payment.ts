import StellarService from './src/helpers/StellarService';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testPathPayment() {
    console.log('🧪 Testing Path Payment Flow\n');
    
    const stellarService = new StellarService();
    
    // Test data - REPLACE WITH YOUR ACTUAL VALUES
    const testData = {
        senderSecretKey: 'SDZG6S63RGBQSSDBO4QGPNONOWBVESW66NGNOAEDCGYBSA4PB4ISOEDD', // Replace with actual secret key
        baseAsset: 'USDT',
        counterAsset: 'UGX',
        amount: '1',
        price: '3700', // Market price from frontend
        orderType: 'buy' as 'buy' | 'sell',
        slippage: 0.5 // 0.5% slippage
    };
    
    console.log('📝 Test Data:');
    console.log({
        baseAsset: testData.baseAsset,
        counterAsset: testData.counterAsset,
        amount: testData.amount,
        price: testData.price,
        orderType: testData.orderType,
        slippage: testData.slippage
    });
    console.log('\n');
    
    // Calculate expected amounts
    if (testData.orderType === 'buy') {
        const estimatedSend = parseFloat(testData.amount) * parseFloat(testData.price);
        const maxSend = estimatedSend * (1 + testData.slippage / 100);
        console.log('💰 Expected Amounts:');
        console.log(`  Want to receive: ${testData.amount} ${testData.baseAsset}`);
        console.log(`  Estimated send: ${estimatedSend.toFixed(7)} ${testData.counterAsset}`);
        console.log(`  Max willing to send: ${maxSend.toFixed(7)} ${testData.counterAsset}`);
    } else {
        const estimatedReceive = parseFloat(testData.amount) * parseFloat(testData.price);
        const minReceive = estimatedReceive * (1 - testData.slippage / 100);
        console.log('💰 Expected Amounts:');
        console.log(`  Want to send: ${testData.amount} ${testData.baseAsset}`);
        console.log(`  Estimated receive: ${estimatedReceive.toFixed(7)} ${testData.counterAsset}`);
        console.log(`  Min willing to receive: ${minReceive.toFixed(7)} ${testData.counterAsset}`);
    }
    console.log('\n');
    
    try {
        console.log('🔍 Finding payment path and executing...\n');
        const result = await stellarService.makePathPayment(testData);
        
        console.log('✅ Result:');
        console.log(JSON.stringify(result, null, 2));
        
        if (result.success) {
            console.log('\n✅ Path payment executed successfully!');
            console.log('Amount:', result.amount);
            console.log('Price:', result.price);
            console.log('Hash:', result.stellarHash);
        } else {
            console.log('\n❌ Path payment failed:');
            console.log('Status:', result.status);
            console.log('Message:', result.message);
        }
        
    } catch (error: any) {
        console.error('\n❌ Error during test:');
        console.error(error.message);
        if (error.response?.data) {
            console.error('Response data:', JSON.stringify(error.response.data, null, 2));
        }
    }
    
    console.log('\n🏁 Test completed');
    process.exit(0);
}

// Instructions
console.log('⚠️  INSTRUCTIONS:');
console.log('1. Replace YOUR_SECRET_KEY_HERE with a real secret key');
console.log('2. Make sure you have the required trustlines');
console.log('3. Make sure you have sufficient balance');
console.log('4. Check that there is liquidity for the trading pair');
console.log('5. Environment variables should be loaded from .env\n');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

// Run the test
testPathPayment().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});

