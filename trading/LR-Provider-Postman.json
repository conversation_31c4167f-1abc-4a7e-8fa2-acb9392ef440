{"info": {"name": "LR Provider - Trading Service", "description": "Test endpoints for Liquidity Rail Provider integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. Generate LR Quote", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100,\n  \"currency\": \"UGX\",\n  \"asset_code\": \"USDT\",\n  \"chain\": \"BSC\",\n  \"provider_id\": 10\n}"}, "url": {"raw": "http://localhost:8039/trading/generate-lr-quote", "protocol": "http", "host": ["localhost"], "port": "8039", "path": ["trading", "generate-lr-quote"]}}, "response": []}, {"name": "2. Confirm LR Quote", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reference_id\": \"REF_{{$timestamp}}\",\n  \"amount\": 100,\n  \"from_currency\": \"USDT\",\n  \"to_currency\": \"UGX\",\n  \"chain\": \"BSC\",\n  \"transaction_type\": \"off_ramp\",\n  \"slippage\": 0.5,\n  \"payment_method\": {\n    \"type\": \"mobile_money\",\n    \"currency\": \"UGX\",\n    \"phone_number\": \"+************\",\n    \"country_code\": \"UG\",\n    \"network\": \"MTN\",\n    \"account_name\": \"Test User\"\n  },\n  \"clientId\": \"TEST_CLIENT\",\n  \"expected_amount\": \"380000\"\n}"}, "url": {"raw": "http://localhost:8039/trading/confirm-lr-quote", "protocol": "http", "host": ["localhost"], "port": "8039", "path": ["trading", "confirm-lr-quote"]}}, "response": []}, {"name": "3. Get LR Transaction", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8039/trading/get-lr-transaction/:transId", "protocol": "http", "host": ["localhost"], "port": "8039", "path": ["trading", "get-lr-transaction", ":transId"], "variable": [{"key": "transId", "value": "REF_1234567890", "description": "Reference ID from confirm quote"}]}}, "response": []}, {"name": "4. Refresh LR Quote", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quote_id\": \"QUOTE_1234567890\",\n  \"amount\": 100,\n  \"currency\": \"UGX\",\n  \"asset_code\": \"USDT\",\n  \"chain\": \"BSC\"\n}"}, "url": {"raw": "http://localhost:8039/trading/refresh-lr-quote", "protocol": "http", "host": ["localhost"], "port": "8039", "path": ["trading", "refresh-lr-quote"]}}, "response": []}, {"name": "5. Muda Events Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event\": \"payment_received\",\n  \"transaction_id\": \"REF_1234567890\",\n  \"status\": \"SUCCESSFUL\",\n  \"amount\": \"100\",\n  \"currency\": \"USDT\",\n  \"hash\": \"0xabc123def456...\"\n}"}, "url": {"raw": "http://localhost:8039/trading/muda-events", "protocol": "http", "host": ["localhost"], "port": "8039", "path": ["trading", "muda-events"]}}, "response": []}], "variable": [{"key": "base_url", "value": "http://localhost:8039/trading", "type": "string"}]}