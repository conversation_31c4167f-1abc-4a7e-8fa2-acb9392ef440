import StellarSDEXTrading from "./src/models/StellarSDEXTrading";

/**
 * Direct test for orderbook endpoint
 * Tests the getOrderBook method directly without HTTP server
 * Using specific Stellar account: GAYL7Q46BMBMNLCOCJA66A4IYGSC4AQNX472HHDUON4KPI6BJZU5UJBC
 */

const TEST_PUBLIC_KEY = 'GAYL7Q46BMBMNLCOCJA66A4IYGSC4AQNX472HHDUON4KPI6BJZU5UJBC';

async function testOrderbookDirect() {
    console.log('\n' + '='.repeat(80));
    console.log('🧪 DIRECT ORDERBOOK TEST');
    console.log('='.repeat(80));
    console.log(`\n📍 Testing Account: ${TEST_PUBLIC_KEY}\n`);

    try {
        // Test 1: Get USDT/UGX orderbook
        console.log('🔍 Test 1: Getting USDT/UGX orderbook...');
        console.log('-'.repeat(80));
        
        const result = await new StellarSDEXTrading().getOrderBook('USDT', 'UGX', TEST_PUBLIC_KEY);
        
        console.log(`\n📊 Response Status: ${result.status}`);
        console.log(`📝 Message: ${result.message}`);
        
        if (result.status === 200) {
            console.log('\n✅ Request successful!\n');
            
            // Access buyOrders and sellOrders from the result directly
            const buyOrders = result.buyOrders || [];
            const sellOrders = result.sellOrders || [];
            const allOrders = result.data || [];
            
            // Display orderbook summary
            console.log('📈 ORDERBOOK SUMMARY:');
            console.log(`   Total Buy Orders: ${buyOrders.length}`);
            console.log(`   Total Sell Orders: ${sellOrders.length}`);
            console.log(`   Total Orders: ${buyOrders.length + sellOrders.length}`);
            
            // Display best prices
            if (buyOrders.length > 0) {
                const bestBuy = buyOrders[0];
                console.log('\n💰 BEST BUY ORDER (Highest Bid):');
                console.log(`   Order ID: ${bestBuy.order_id}`);
                console.log(`   Stellar Offer ID: ${bestBuy.stellar_offer_id}`);
                console.log(`   Price: ${bestBuy.price} ${bestBuy.counter_asset}/${bestBuy.base_asset}`);
                console.log(`   Amount: ${bestBuy.amount} ${bestBuy.base_asset}`);
                console.log(`   Receivable: ${bestBuy.receivable_amount} ${bestBuy.counter_asset}`);
                console.log(`   Client: ${bestBuy.client_id}`);
                console.log(`   Status: ${bestBuy.status}`);
            } else {
                console.log('\n⚠️  No buy orders available');
            }
            
            if (sellOrders.length > 0) {
                const bestSell = sellOrders[0];
                console.log('\n💸 BEST SELL ORDER (Lowest Ask):');
                console.log(`   Order ID: ${bestSell.order_id}`);
                console.log(`   Stellar Offer ID: ${bestSell.stellar_offer_id}`);
                console.log(`   Price: ${bestSell.price} ${bestSell.counter_asset}/${bestSell.base_asset}`);
                console.log(`   Amount: ${bestSell.amount} ${bestSell.base_asset}`);
                console.log(`   Receivable: ${bestSell.receivable_amount} ${bestSell.counter_asset}`);
                console.log(`   Client: ${bestSell.client_id}`);
                console.log(`   Status: ${bestSell.status}`);
            } else {
                console.log('\n⚠️  No sell orders available');
            }
            
            // Display ALL orders with full details
            if (buyOrders.length > 0) {
                console.log('\n📋 ALL BUY ORDERS:');
                console.log('─'.repeat(80));
                buyOrders.forEach((order: any, index: number) => {
                    console.log(`\n   ${index + 1}. BUY Order:`);
                    console.log(`      Order ID: ${order.order_id}`);
                    console.log(`      Stellar Offer: ${order.stellar_offer_id}`);
                    console.log(`      Amount: ${order.amount} ${order.base_asset}`);
                    console.log(`      Price: ${order.price} ${order.counter_asset}/${order.base_asset}`);
                    console.log(`      Receivable: ${order.receivable_amount} ${order.counter_asset}`);
                    console.log(`      Selling: ${order.selling_asset}, Buying: ${order.buying_asset}`);
                    console.log(`      Client: ${order.client_id}`);
                    console.log(`      Status: ${order.status}`);
                });
            }
            
            if (sellOrders.length > 0) {
                console.log('\n📋 ALL SELL ORDERS:');
                console.log('─'.repeat(80));
                sellOrders.forEach((order: any, index: number) => {
                    console.log(`\n   ${index + 1}. SELL Order:`);
                    console.log(`      Order ID: ${order.order_id}`);
                    console.log(`      Stellar Offer: ${order.stellar_offer_id}`);
                    console.log(`      Amount: ${order.amount} ${order.base_asset}`);
                    console.log(`      Price: ${order.price} ${order.counter_asset}/${order.base_asset}`);
                    console.log(`      Receivable: ${order.receivable_amount} ${order.counter_asset}`);
                    console.log(`      Selling: ${order.selling_asset}, Buying: ${order.buying_asset}`);
                    console.log(`      Client: ${order.client_id}`);
                    console.log(`      Status: ${order.status}`);
                });
            }
            
            // Calculate spread if both buy and sell orders exist
            if (buyOrders && buyOrders.length > 0 && sellOrders && sellOrders.length > 0) {
                const bestBidPrice = parseFloat(buyOrders[0].price);
                const bestAskPrice = parseFloat(sellOrders[0].price);
                const spread = bestAskPrice - bestBidPrice;
                const spreadPercent = ((spread / bestBidPrice) * 100).toFixed(4);
                
                console.log('\n📊 SPREAD ANALYSIS:');
                console.log(`   Best Bid: ${bestBidPrice} UGX/USDT`);
                console.log(`   Best Ask: ${bestAskPrice} UGX/USDT`);
                console.log(`   Spread: ${spread.toFixed(2)} UGX (${spreadPercent}%)`);
            }
            
            console.log('\n' + '='.repeat(80));
            console.log('✅ TEST PASSED: Successfully retrieved USDT/UGX orderbook');
            console.log('='.repeat(80) + '\n');
            
            return { success: true, buyOrders, sellOrders, allOrders };
        } else {
            console.log(`\n❌ Request failed`);
            console.log(`Status: ${result.status}`);
            console.log(`Message: ${result.message}`);
            console.log('\n' + '='.repeat(80));
            console.log('❌ TEST FAILED');
            console.log('='.repeat(80) + '\n');
            
            return { success: false, message: result.message };
        }
        
    } catch (error: any) {
        console.error('\n❌ ERROR:', error.message);
        console.error('\nStack trace:', error.stack);
        console.log('\n' + '='.repeat(80));
        console.log('❌ TEST FAILED WITH ERROR');
        console.log('='.repeat(80) + '\n');
        
        return { success: false, error: error.message };
    }
}

// Test 2: Test with different trading pairs
async function testMultiplePairs() {
    console.log('\n' + '='.repeat(80));
    console.log('🧪 TEST 2: Multiple Trading Pairs');
    console.log('='.repeat(80) + '\n');
    
    const pairs = [
        { base: 'USDT', counter: 'UGX' },
        { base: 'USDC', counter: 'UGX' },
        { base: 'XLM', counter: 'USDT' }
    ];
    
    for (const pair of pairs) {
        console.log(`\n🔍 Testing ${pair.base}/${pair.counter}...`);
        console.log('-'.repeat(80));
        
        try {
            const result = await new StellarSDEXTrading().getOrderBook(pair.base, pair.counter, TEST_PUBLIC_KEY);
            
            if (result.status === 200 && result.data) {
                const { buyOrders, sellOrders } = result.data;
                const totalOrders = (buyOrders?.length || 0) + (sellOrders?.length || 0);
                
                console.log(`✅ ${pair.base}/${pair.counter}: ${totalOrders} orders (${buyOrders?.length || 0} buy, ${sellOrders?.length || 0} sell)`);
            } else {
                console.log(`⚠️  ${pair.base}/${pair.counter}: ${result.message}`);
            }
        } catch (error: any) {
            console.log(`❌ ${pair.base}/${pair.counter}: ${error.message}`);
        }
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('✅ MULTIPLE PAIRS TEST COMPLETED');
    console.log('='.repeat(80) + '\n');
}

// Run all tests
async function runAllTests() {
    console.log('\n\n');
    console.log('╔' + '═'.repeat(78) + '╗');
    console.log('║' + ' '.repeat(20) + '🚀 ORDERBOOK DIRECT TEST SUITE' + ' '.repeat(27) + '║');
    console.log('╚' + '═'.repeat(78) + '╝');
    console.log('\n');
    
    const startTime = Date.now();
    
    // Run Test 1
    const result1 = await testOrderbookDirect();
    
    // Run Test 2
    await testMultiplePairs();
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    // Final Summary
    console.log('\n' + '='.repeat(80));
    console.log('📊 FINAL RESULTS');
    console.log('='.repeat(80));
    console.log(`\n⏱️  Total execution time: ${duration}s`);
    console.log(`📍 Test account: ${TEST_PUBLIC_KEY}`);
    
    if (result1.success) {
        console.log('\n🎉 ALL TESTS PASSED!\n');
        process.exit(0);
    } else {
        console.log('\n⚠️  SOME TESTS FAILED\n');
        process.exit(1);
    }
}

// Execute test suite
runAllTests().catch((error) => {
    console.error('\n❌ Test suite crashed:', error);
    console.error('\nStack trace:', error.stack);
    process.exit(1);
});

