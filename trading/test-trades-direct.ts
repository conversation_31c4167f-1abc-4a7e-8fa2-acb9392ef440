import StellarSDEXTrading from "./src/models/StellarSDEXTrading";

/**
 * Direct test for trade history
 * Tests the getTradeHistory method directly without HTTP server
 * Using specific Stellar account: GAYL7Q46BMBMNLCOCJA66A4IYGSC4AQNX472HHDUON4KPI6BJZU5UJBC
 */

const TEST_PUBLIC_KEY = 'GAYL7Q46BMBMNLCOCJA66A4IYGSC4AQNX472HHDUON4KPI6BJZU5UJBC';

async function testTradeHistory() {
    console.log('\n' + '='.repeat(80));
    console.log('🧪 TRADE HISTORY TEST');
    console.log('='.repeat(80));
    console.log(`\n📍 Testing Account: ${TEST_PUBLIC_KEY}\n`);

    try {
        // Test 1: Get trade history for USDT/UGX
        console.log('🔍 Getting trade history for USDT/UGX...');
        console.log('-'.repeat(80));
        
        const result = await new StellarSDEXTrading().getTradeHistory(
            TEST_PUBLIC_KEY,
            'USDT',
            'UGX'
        );
        
        console.log(`\n📊 Response Status: ${result.status}`);
        console.log(`📝 Message: ${result.message}`);
        
        if (result.status === 200 && result.data) {
            console.log('\n✅ Request successful!\n');
            
            const { orders, trades, swaps } = result.data;
            
            // Display summary
            console.log('📈 TRADE HISTORY SUMMARY:');
            console.log(`   Active Orders: ${orders?.length || 0}`);
            console.log(`   Completed Trades: ${trades?.length || 0}`);
            console.log(`   Swaps: ${swaps?.length || 0}`);
            console.log(`   Total: ${(orders?.length || 0) + (trades?.length || 0) + (swaps?.length || 0)}`);
            
            // Display all completed trades with full details
            if (trades && trades.length > 0) {
                console.log('\n' + '='.repeat(80));
                console.log('💰 COMPLETED TRADES');
                console.log('='.repeat(80));
                
                trades.forEach((trade: any, index: number) => {
                    console.log(`\n📌 Trade ${index + 1}:`);
                    console.log('─'.repeat(80));
                    console.log(`   Trade ID: ${trade.trade_id}`);
                    console.log(`   Offer ID: ${trade.offer_id}`);
                    console.log(`   Client ID: ${trade.client_id}`);
                    console.log(`   Type: ${trade.order_type.toUpperCase()}`);
                    console.log(`   Status: ${trade.status}`);
                    console.log(`   Created: ${trade.created_at}`);
                    console.log('');
                    console.log(`   Base Asset: ${trade.base_asset}`);
                    console.log(`   Counter Asset: ${trade.counter_asset}`);
                    console.log('');
                    console.log(`   Base Amount: ${trade.base_amount} ${trade.base_asset}`);
                    console.log(`   Counter Amount: ${trade.counter_amount} ${trade.counter_asset}`);
                    console.log('');
                    console.log(`   Amount Traded: ${trade.amount}`);
                    console.log(`   Amount Received: ${trade.received}`);
                    console.log(`   Price: ${trade.price}`);
                    console.log('─'.repeat(80));
                });
                
                // Summary statistics
                console.log('\n' + '='.repeat(80));
                console.log('📊 TRADE STATISTICS');
                console.log('='.repeat(80));
                
                const buyTrades = trades.filter((t: any) => t.order_type === 'buy');
                const sellTrades = trades.filter((t: any) => t.order_type === 'sell');
                
                console.log(`\n   Buy Trades: ${buyTrades.length}`);
                console.log(`   Sell Trades: ${sellTrades.length}`);
                
                if (buyTrades.length > 0) {
                    const totalBuyAmount = buyTrades.reduce((sum: number, t: any) => sum + parseFloat(t.amount || '0'), 0);
                    const totalBuyReceived = buyTrades.reduce((sum: number, t: any) => sum + parseFloat(t.received || '0'), 0);
                    console.log(`\n   Total Buy Amount: ${totalBuyAmount.toFixed(2)}`);
                    console.log(`   Total Buy Received: ${totalBuyReceived.toFixed(2)}`);
                }
                
                if (sellTrades.length > 0) {
                    const totalSellAmount = sellTrades.reduce((sum: number, t: any) => sum + parseFloat(t.amount || '0'), 0);
                    const totalSellReceived = sellTrades.reduce((sum: number, t: any) => sum + parseFloat(t.received || '0'), 0);
                    console.log(`\n   Total Sell Amount: ${totalSellAmount.toFixed(2)}`);
                    console.log(`   Total Sell Received: ${totalSellReceived.toFixed(2)}`);
                }
                
            } else {
                console.log('\n⚠️  No completed trades found');
            }
            
            // Display active orders
            if (orders && orders.length > 0) {
                console.log('\n' + '='.repeat(80));
                console.log('📋 ACTIVE ORDERS');
                console.log('='.repeat(80));
                
                orders.forEach((order: any, index: number) => {
                    console.log(`\n   ${index + 1}. ${order.order_type.toUpperCase()} Order:`);
                    console.log(`      Order ID: ${order.order_id}`);
                    console.log(`      Stellar Offer: ${order.stellar_offer_id}`);
                    console.log(`      Amount: ${order.amount}`);
                    console.log(`      Price: ${order.price}`);
                    console.log(`      Status: ${order.status}`);
                    console.log(`      Created: ${order.created_at}`);
                });
            } else {
                console.log('\n⚠️  No active orders found');
            }
            
            console.log('\n' + '='.repeat(80));
            console.log('✅ TEST PASSED: Successfully retrieved trade history');
            console.log('='.repeat(80) + '\n');
            
            return { success: true, trades, orders };
        } else {
            console.log(`\n❌ Request failed`);
            console.log(`Status: ${result.status}`);
            console.log(`Message: ${result.message}`);
            console.log('\n' + '='.repeat(80));
            console.log('❌ TEST FAILED');
            console.log('='.repeat(80) + '\n');
            
            return { success: false, message: result.message };
        }
        
    } catch (error: any) {
        console.error('\n❌ ERROR:', error.message);
        console.error('\nStack trace:', error.stack);
        console.log('\n' + '='.repeat(80));
        console.log('❌ TEST FAILED WITH ERROR');
        console.log('='.repeat(80) + '\n');
        
        return { success: false, error: error.message };
    }
}

// Test 2: Get all trades (no filter)
async function testAllTrades() {
    console.log('\n' + '='.repeat(80));
    console.log('🧪 TEST 2: All Trades (No Filter)');
    console.log('='.repeat(80) + '\n');
    
    try {
        const result = await new StellarSDEXTrading().getTradeHistory(TEST_PUBLIC_KEY);
        
        if (result.status === 200 && result.data) {
            const { trades } = result.data;
            
            console.log(`✅ Total trades across all pairs: ${trades?.length || 0}\n`);
            
            if (trades && trades.length > 0) {
                // Group by trading pair
                const pairGroups: any = {};
                trades.forEach((trade: any) => {
                    const pair = `${trade.base_asset}/${trade.counter_asset}`;
                    if (!pairGroups[pair]) {
                        pairGroups[pair] = [];
                    }
                    pairGroups[pair].push(trade);
                });
                
                console.log('📊 Trades by Trading Pair:');
                console.log('─'.repeat(80));
                Object.keys(pairGroups).forEach((pair) => {
                    console.log(`   ${pair}: ${pairGroups[pair].length} trades`);
                });
            }
        } else {
            console.log(`⚠️  Failed to retrieve trades: ${result.message}`);
        }
    } catch (error: any) {
        console.log(`❌ Error: ${error.message}`);
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('✅ ALL TRADES TEST COMPLETED');
    console.log('='.repeat(80) + '\n');
}

// Run all tests
async function runAllTests() {
    console.log('\n\n');
    console.log('╔' + '═'.repeat(78) + '╗');
    console.log('║' + ' '.repeat(22) + '🚀 TRADE HISTORY TEST SUITE' + ' '.repeat(28) + '║');
    console.log('╚' + '═'.repeat(78) + '╝');
    console.log('\n');
    
    const startTime = Date.now();
    
    // Run Test 1
    const result1 = await testTradeHistory();
    
    // Run Test 2
    await testAllTrades();
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    // Final Summary
    console.log('\n' + '='.repeat(80));
    console.log('📊 FINAL RESULTS');
    console.log('='.repeat(80));
    console.log(`\n⏱️  Total execution time: ${duration}s`);
    console.log(`📍 Test account: ${TEST_PUBLIC_KEY}`);
    
    if (result1.success) {
        console.log('\n🎉 ALL TESTS PASSED!\n');
        process.exit(0);
    } else {
        console.log('\n⚠️  SOME TESTS FAILED\n');
        process.exit(1);
    }
}

// Execute test suite
runAllTests().catch((error) => {
    console.error('\n❌ Test suite crashed:', error);
    console.error('\nStack trace:', error.stack);
    process.exit(1);
});

