import * as StellarSdk from 'stellar-sdk';

/**
 * Direct test for Stellar trades
 * Calls Stellar Horizon API directly without database
 * 
 * Usage: npx ts-node test-trades-stellar-direct.ts <PUBLIC_KEY> [network]
 * Example: npx ts-node test-trades-stellar-direct.ts GAYL7Q46BMBMNLCOCJA66A4IYGSC4AQNX472HHDUON4KPI6BJZU5UJBC testnet
 */

// Get public key from command line arguments
const PUBLIC_KEY = process.argv[2] || 'GAYL7Q46BMBMNLCOCJA66A4IYGSC4AQNX472HHDUON4KPI6BJZU5UJBC';
const STELLAR_NETWORK = process.argv[3] || 'testnet'; // or 'public' for mainnet

// Validate public key format (basic check)
function isValidStellarPublicKey(key: string): boolean {
    return /^G[A-Z0-9]{55}$/.test(key);
}

async function getTradesDirect() {
    console.log('\n' + '='.repeat(80));
    console.log('🧪 STELLAR TRADES DIRECT TEST');
    console.log('='.repeat(80));
    console.log(`\n📍 Testing Account: ${PUBLIC_KEY}`);
    console.log(`🌐 Network: ${STELLAR_NETWORK}\n`);

    try {
        // Initialize Stellar server
        const server = STELLAR_NETWORK === 'testnet'
            ? new StellarSdk.Server('https://horizon-testnet.stellar.org')
            : new StellarSdk.Server('https://horizon.stellar.org');

        console.log('🔍 Fetching trades from Stellar Horizon...');
        console.log('-'.repeat(80));

        // Get trades for this account
        const tradesResponse = await server.trades()
            .forAccount(PUBLIC_KEY)
            .order('desc')
            .limit(200)
            .call();

        const trades = tradesResponse.records;

        console.log(`\n✅ Retrieved ${trades.length} trades\n`, trades);

        if (trades.length === 0) {
         
            return { success: true, trades: [] };
        }

        // Display all trades with full details
        console.log('='.repeat(80));
        console.log('💰 COMPLETED TRADES');
        console.log('='.repeat(80));

        trades.forEach((trade: any, index: number) => {
            // Extract asset information
            const baseAsset = trade.base_asset_type === 'native' ? 'XLM' : trade.base_asset_code;
            const counterAsset = trade.counter_asset_type === 'native' ? 'XLM' : trade.counter_asset_code;
            
            // Get amounts
            const baseAmount = parseFloat(trade.base_amount || '0');
            const counterAmount = parseFloat(trade.counter_amount || '0');
            
            // Calculate price
            const price = trade.price ? (trade.price.n / trade.price.d) : 0;
            
            // Determine trade direction from USER's perspective
            const isBaseAccount = trade.base_account === PUBLIC_KEY;
            const isCounterAccount = trade.counter_account === PUBLIC_KEY;
            
            let orderType: string;
            let userAction: string;
            let amountSold: string;
            let amountReceived: string;
            
            if (isBaseAccount) {
                if (trade.base_is_seller) {
                    orderType = 'SELL';
                    userAction = `Sold ${baseAmount.toFixed(7)} ${baseAsset}`;
                    amountSold = `${baseAmount.toFixed(7)} ${baseAsset}`;
                    amountReceived = `${counterAmount.toFixed(7)} ${counterAsset}`;
                } else {
                    orderType = 'BUY';
                    userAction = `Bought ${baseAmount.toFixed(7)} ${baseAsset}`;
                    amountSold = `${counterAmount.toFixed(7)} ${counterAsset}`;
                    amountReceived = `${baseAmount.toFixed(7)} ${baseAsset}`;
                }
            } else if (isCounterAccount) {
                if (trade.base_is_seller) {
                    orderType = 'BUY';
                    userAction = `Bought ${baseAmount.toFixed(7)} ${baseAsset}`;
                    amountSold = `${counterAmount.toFixed(7)} ${counterAsset}`;
                    amountReceived = `${baseAmount.toFixed(7)} ${baseAsset}`;
                } else {
                    orderType = 'SELL';
                    userAction = `Sold ${counterAmount.toFixed(7)} ${counterAsset}`;
                    amountSold = `${counterAmount.toFixed(7)} ${counterAsset}`;
                    amountReceived = `${baseAmount.toFixed(7)} ${baseAsset}`;
                }
            } else {
                orderType = 'UNKNOWN';
                userAction = 'Not directly involved';
                amountSold = 'N/A';
                amountReceived = 'N/A';
            }
            
            console.log(`\n📌 Trade ${index + 1}:`);
            console.log('─'.repeat(80));
            console.log(`   Trade ID: ${trade.id}`);
            console.log(`   Type: ${orderType} - ${userAction}`);
            console.log(`   Time: ${trade.ledger_close_time}`);
            console.log('');
            console.log(`   You Gave: ${amountSold}`);
            console.log(`   You Got: ${amountReceived}`);
            console.log(`   Price: ${price.toFixed(7)} ${counterAsset}/${baseAsset}`);
            console.log('');
            console.log(`   Your Account: ${PUBLIC_KEY}`);
            console.log(`   Counter Party: ${isBaseAccount ? trade.counter_account : trade.base_account}`);
            console.log('');
            
            // Show issuer information
            if (trade.base_asset_type !== 'native') {
                console.log(`   ${baseAsset} Issuer: ${trade.base_asset_issuer}`);
            }
            if (trade.counter_asset_type !== 'native') {
                console.log(`   ${counterAsset} Issuer: ${trade.counter_asset_issuer}`);
            }
            
            console.log('─'.repeat(80));
        });

        // Summary statistics
        console.log('\n' + '='.repeat(80));
        console.log('📊 TRADE STATISTICS');
        console.log('='.repeat(80));

        // Group by trading pair
        const pairGroups: any = {};
        const buyTrades: any[] = [];
        const sellTrades: any[] = [];

        trades.forEach((trade: any) => {
            const baseAsset = trade.base_asset_type === 'native' ? 'XLM' : trade.base_asset_code;
            const counterAsset = trade.counter_asset_type === 'native' ? 'XLM' : trade.counter_asset_code;
            const pair = `${baseAsset}/${counterAsset}`;
            
            if (!pairGroups[pair]) {
                pairGroups[pair] = [];
            }
            pairGroups[pair].push(trade);
            
            // Determine from USER's perspective
            const isBaseAccount = trade.base_account === PUBLIC_KEY;
            const isCounterAccount = trade.counter_account === PUBLIC_KEY;
            
            let isBuy = false;
            if (isBaseAccount) {
                isBuy = !trade.base_is_seller; // User bought base asset
            } else if (isCounterAccount) {
                isBuy = trade.base_is_seller; // User bought base asset from base seller
            }
            
            if (isBuy) {
                buyTrades.push(trade);
            } else {
                sellTrades.push(trade);
            }
        });

        console.log(`\n   Total Trades: ${trades.length}`);
        console.log(`   Buy Trades: ${buyTrades.length}`);
        console.log(`   Sell Trades: ${sellTrades.length}`);

        console.log('\n   Trades by Pair:');
        Object.keys(pairGroups).forEach((pair) => {
            console.log(`      ${pair}: ${pairGroups[pair].length} trades`);
        });

        // Calculate volume by asset
        console.log('\n   Volume by Asset:');
        const volumeByAsset: any = {};
        
        trades.forEach((trade: any) => {
            const baseAsset = trade.base_asset_type === 'native' ? 'XLM' : trade.base_asset_code;
            const counterAsset = trade.counter_asset_type === 'native' ? 'XLM' : trade.counter_asset_code;
            const baseAmount = parseFloat(trade.base_amount || '0');
            const counterAmount = parseFloat(trade.counter_amount || '0');
            
            if (!volumeByAsset[baseAsset]) volumeByAsset[baseAsset] = 0;
            if (!volumeByAsset[counterAsset]) volumeByAsset[counterAsset] = 0;
            
            volumeByAsset[baseAsset] += baseAmount;
            volumeByAsset[counterAsset] += counterAmount;
        });
        
        Object.keys(volumeByAsset).forEach((asset) => {
            console.log(`      ${asset}: ${volumeByAsset[asset].toFixed(2)}`);
        });

        console.log('\n' + '='.repeat(80));
        console.log('✅ TEST PASSED: Successfully retrieved trades from Stellar');
        console.log('='.repeat(80) + '\n');

        return { success: true, trades };

    } catch (error: any) {
        console.error('\n❌ ERROR:', error.message);
        console.error('\nStack trace:', error.stack);
        console.log('\n' + '='.repeat(80));
        console.log('❌ TEST FAILED WITH ERROR');
        console.log('='.repeat(80) + '\n');

        return { success: false, error: error.message };
    }
}

// Run the test
async function runTest() {
    console.log('\n\n');
    console.log('╔' + '═'.repeat(78) + '╗');
    console.log('║' + ' '.repeat(20) + '🚀 STELLAR TRADES DIRECT TEST' + ' '.repeat(28) + '║');
    console.log('╚' + '═'.repeat(78) + '╝');
    console.log('\n');

    // Validate public key
    if (!isValidStellarPublicKey(PUBLIC_KEY)) {
        console.error('❌ Invalid Stellar public key format!');
        console.error(`   Provided: ${PUBLIC_KEY}`);
        console.error('\n💡 Usage: npx ts-node test-trades-stellar-direct.ts <PUBLIC_KEY> [network]');
        console.error('   Example: npx ts-node test-trades-stellar-direct.ts GAYL7Q46BMBMNLCOCJA66A4IYGSC4AQNX472HHDUON4KPI6BJZU5UJBC testnet\n');
        process.exit(1);
    }

    const startTime = Date.now();
    
    const result = await getTradesDirect();
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

   

    if (result.success) {
        console.log(`✅ Found ${result.trades?.length || 0} trades`);
        console.log('\n🎉 TEST PASSED!\n');
        process.exit(0);
    } else {
        console.log('\n⚠️  TEST FAILED\n');
        process.exit(1);
    }
}

// Execute test
runTest().catch((error) => {
    console.error('\n❌ Test crashed:', error);
    console.error('\nStack trace:', error.stack);
    process.exit(1);
});

