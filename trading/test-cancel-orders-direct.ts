import StellarSdk from 'stellar-sdk';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const server = new StellarSdk.Server(
    process.env.STELLAR_HORIZON_URL || 'https://horizon-testnet.stellar.org'
);
const network = process.env.STELLAR_NETWORK_PASSPHRASE || StellarSdk.Networks.TESTNET;

async function cancelAllOrdersDirect(secretKey: string) {
    console.log('🧪 Testing Direct Cancel All Orders\n');
    
    try {
        // Get keypair from secret
        const keypair = StellarSdk.Keypair.fromSecret(secretKey);
        const publicKey = keypair.publicKey();
        
        console.log('📝 Account Info:');
        console.log(`  Public Key: ${publicKey}`);
        console.log(`  Secret Key: ${secretKey.substring(0, 10)}...${secretKey.substring(secretKey.length - 4)}`);
        console.log('\n');
        
        console.log('🔍 Fetching active orders from Stellar SDEX...\n');
        
        // Fetch all active offers for this account from Stellar
        const offersResponse = await server.offers()
            .forAccount(publicKey)
            .limit(200)
            .call();
        
        const offers = offersResponse.records;
        
        if (!offers || offers.length === 0) {
            console.log('ℹ️  No active orders found on the SDEX');
            console.log('\n🏁 Test completed');
            return;
        }
        
        console.log(`📊 Found ${offers.length} active orders:\n`);
        
        offers.forEach((offer: any, index: number) => {
            console.log(`${index + 1}. Offer ID: ${offer.id}`);
            console.log(`   Selling: ${offer.amount} ${offer.selling.asset_code || 'XLM'}`);
            console.log(`   Buying: ${offer.buying.asset_code || 'XLM'}`);
            console.log(`   Price: ${offer.price}`);
            console.log('');
        });
        
        console.log(`🚫 Cancelling all ${offers.length} orders...\n`);
        
        // Get sponsor key from environment
        const sponsorKey = process.env.SPONSOR_KEY;
        if (!sponsorKey) {
            throw new Error('SPONSOR_KEY not found in environment');
        }
        
        const sponsorKeypair = StellarSdk.Keypair.fromSecret(sponsorKey);
        console.log(`💰 Using sponsor account: ${sponsorKeypair.publicKey()}\n`);
        
        // Load the sponsor account (who will pay the fees)
        const sponsorAccount = await server.loadAccount(sponsorKeypair.publicKey());
        
        // Create cancel operations for each offer (set amount to 0)
        const cancelOperations = offers.map((offer: any) => {
            const sellingAsset = offer.selling.asset_type === 'native'
                ? StellarSdk.Asset.native()
                : new StellarSdk.Asset(offer.selling.asset_code, offer.selling.asset_issuer);
            
            const buyingAsset = offer.buying.asset_type === 'native'
                ? StellarSdk.Asset.native()
                : new StellarSdk.Asset(offer.buying.asset_code, offer.buying.asset_issuer);
            
            console.log(`🔄 Preparing cancel for offer ${offer.id}`);
            
            return StellarSdk.Operation.manageSellOffer({
                selling: sellingAsset,
                buying: buyingAsset,
                amount: '0', // Setting amount to 0 cancels the offer
                price: offer.price,
                offerId: offer.id.toString(),
                source: publicKey // User is the source of this operation
            });
        });
        
        console.log('\n📦 Building transaction...');
        
        // Build transaction with sponsor as the fee payer
        const transaction = new StellarSdk.TransactionBuilder(sponsorAccount, {
            fee: (100000 * offers.length).toString(), // Fee per operation
            networkPassphrase: network,
        });
        
        // Add all cancel operations (Stellar allows up to 100 operations per transaction)
        cancelOperations.forEach((op: any) => transaction.addOperation(op));
        
        const builtTransaction = transaction.setTimeout(30).build();
        
        // Sign with both sponsor (fee payer) and user (operation source)
        builtTransaction.sign(sponsorKeypair);
        builtTransaction.sign(keypair);
        
        // Submit the transaction
        console.log(`📤 Submitting sponsored transaction to cancel ${offers.length} offers...\n`);
        const result = await server.submitTransaction(builtTransaction);
        
        console.log('✅ SUCCESS! All orders cancelled!\n');
        console.log(`📋 Transaction Details:`);
        console.log(`   Hash: ${result.hash}`);
        console.log(`   Ledger: ${result.ledger}`);
        console.log(`   Orders cancelled: ${offers.length}`);
        console.log('\n📊 Cancelled Offers:');
        
        offers.forEach((offer: any, index: number) => {
            console.log(`   ${index + 1}. ID ${offer.id}: ${offer.amount} ${offer.selling.asset_code || 'XLM'} → ${offer.buying.asset_code || 'XLM'} @ ${offer.price}`);
        });
        
    } catch (error: any) {
        console.error('\n❌ Error cancelling orders:');
        console.error('Message:', error.message);
        
        if (error.response?.data) {
            console.error('\nStellar Error Details:');
            console.error(JSON.stringify(error.response.data, null, 2));
            
            if (error.response.data.extras?.result_codes) {
                console.error('\nResult Codes:');
                console.error(JSON.stringify(error.response.data.extras.result_codes, null, 2));
            }
        }
    }
    
    console.log('\n🏁 Test completed');
}

// Main execution  
const SECRET_KEY = 'SDZG6S63RGBQSSDBO4QGPNONOWBVESW66NGNOAEDCGYBSA4PB4ISOEDD';

console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('⚠️  DIRECT CANCEL ALL ORDERS TEST');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('\nThis will cancel ALL active orders for the account.');
console.log('No database lookup - uses Stellar directly.');
console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

cancelAllOrdersDirect(SECRET_KEY)
    .then(() => process.exit(0))
    .catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });

