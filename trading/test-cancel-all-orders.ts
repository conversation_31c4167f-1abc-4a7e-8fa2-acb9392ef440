import StellarSDEXTrading from './src/models/StellarSDEXTrading';
import StellarSdk from 'stellar-sdk';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testCancelAllOrders() {
    console.log('🧪 Testing Cancel All Orders\n');
    
    // REPLACE WITH YOUR ACTUAL SECRET KEY
    const secretKey = 'SDZG6S63RGBQSSDBO4QGPNONOWBVESW66NGNOAEDCGYBSA4PB4ISOEDD';
    
    // Get public key from secret
    const keypair = StellarSdk.Keypair.fromSecret(secretKey);
    const publicKey = keypair.publicKey();
    
    console.log('📝 Account Info:');
    console.log(`  Public Key: ${publicKey}`);
    console.log(`  Secret Key: ${secretKey.substring(0, 10)}...${secretKey.substring(secretKey.length - 4)}`);
    console.log('\n');
    
    const trading = new StellarSDEXTrading();
    
    // First, let's check what orders exist on the SDEX for this account
    console.log('🔍 Checking for active orders on Stellar SDEX...\n');
    
    try {
        // We need to get the client_id from the database
        // For testing, we'll use a mock approach by directly calling cancelAllOrders with a derived clientId
        // In production, you'd look this up from your database
        
        // Create a test clientId (you may need to replace this with actual clientId from your DB)
        const testClientId = 'test_client_' + publicKey.substring(0, 10);
        
        console.log(`  Using Client ID: ${testClientId}`);
        console.log('\n');
        
        console.log('📊 Fetching active orders from Stellar...\n');
        
        // Call cancelAllOrders - this fetches directly from Stellar blockchain
        const result = await trading.cancelAllOrders(testClientId);
        
        console.log('✅ Result:');
        console.log(JSON.stringify(result, null, 2));
        console.log('\n');
        
        if (result.status === 200) {
            console.log('✅ Successfully cancelled orders!');
            console.log(`   Total orders cancelled: ${result.data?.total || 0}`);
            console.log(`   Stellar transaction hash: ${result.data?.stellarHash || 'N/A'}`);
            
            if (result.data?.offers && result.data.offers.length > 0) {
                console.log('\n📋 Cancelled offers:');
                result.data.offers.forEach((offer: any, index: number) => {
                    console.log(`   ${index + 1}. Offer ID: ${offer.offerId}`);
                    console.log(`      Selling: ${offer.amount} ${offer.selling}`);
                    console.log(`      Buying: ${offer.buying}`);
                    console.log(`      Price: ${offer.price}`);
                });
            }
        } else if (result.status === 200 && result.data?.total === 0) {
            console.log('ℹ️  No active orders found on the SDEX');
        } else {
            console.log('❌ Failed to cancel orders:');
            console.log(`   Status: ${result.status}`);
            console.log(`   Message: ${result.message}`);
        }
        
    } catch (error: any) {
        console.error('\n❌ Error during test:');
        console.error('Message:', error.message);
        if (error.response?.data) {
            console.error('Response data:', JSON.stringify(error.response.data, null, 2));
        }
        console.error('\nStack:', error.stack);
    }
    
    console.log('\n🏁 Test completed');
    process.exit(0);
}

// Instructions
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('⚠️  CANCEL ALL ORDERS TEST');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('\nThis will cancel ALL active orders for the account.');
console.log('Make sure you have:');
console.log('1. Replaced the secret key with your actual key');
console.log('2. Sufficient XLM balance for transaction fees');
console.log('3. Active orders on the Stellar SDEX to cancel');
console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

// Run the test
testCancelAllOrders().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});

