module.exports = {
  // Test environment
  testEnvironment: 'node',

  // Root directory for tests
  rootDir: '.',

  // Test file patterns
  testMatch: [
    '<rootDir>/tests/**/*.test.ts',
    '<rootDir>/tests/**/*.integration.test.ts'
  ],

  // TypeScript support
  preset: 'ts-jest',

  // Jest globals
  globals: {
    'ts-jest': {
      useESM: false
    }
  },

  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // Transform files
  transform: {
    '^.+\\.ts$': 'ts-jest'
  },

  // Coverage configuration
  collectCoverage: false, // Disable for now to speed up tests
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts'
  ],

  // Test timeout (important for integration tests)
  testTimeout: 120000, // 2 minutes for integration tests

  // Verbose output
  verbose: true,

  // Module path mapping (correct Jest option)
  modulePathIgnorePatterns: [
    '<rootDir>/node_modules/'
  ]
};
