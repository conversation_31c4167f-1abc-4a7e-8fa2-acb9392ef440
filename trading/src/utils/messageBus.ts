/**
 * Simple Trading Service Message Bus
 * Handles SWAP_COMPLETED, DEPOSIT_RECEIVED, PAYOUT_MADE events
 */

import amqp, { Channel, Connection, Message, ChannelModel } from 'amqplib';
import StellarSDEXTrading from '../models/StellarSDEXTrading';
import { WebhookData } from '../models/StellarSDEXTrading';

export interface TradingEvent {
    eventType: 'SWAP_COMPLETED' | 'DEPOSIT_RECEIVED' | 'PAYOUT_MADE' | 'CRYPTO_DEPOSIT_RECEIVED';
  id: string;
  clientId: string;
  fromAsset: string;
  toAsset: string;
  amount: number;
  status: 'completed' | 'failed';
  stellarHash?: string;
  timestamp: string;
}

export class TradingMessageBus {
  private connection: ChannelModel | null = null;
  private channel: Channel | null = null;
  private isConnected: boolean = false;
  private stellarSDEXTrading = new StellarSDEXTrading();
  constructor() {}

  async connect(): Promise<void> {
    const url = `amqp://${process.env.RABBITMQ_USERNAME || 'guest'}:${process.env.RABBITMQ_PASSWORD || 'guest'}@${process.env.RABBITMQ_HOST || 'localhost'}:${process.env.RABBITMQ_PORT || '5672'}`;
    
    this.connection = await amqp.connect(url);
    this.channel = await this.connection.createChannel();
    await this.channel.assertQueue('inter_service_events', { durable: true });
    
    this.isConnected = true;
    console.log('Trading MessageBus connected');
  }

  async publishEvent(event: TradingEvent): Promise<boolean> {
    if (!this.channel || !this.isConnected) return false;

    const message = Buffer.from(JSON.stringify(event));
    const success = this.channel.sendToQueue('inter_service_events', message, { persistent: true });
    
    if (success) {
      console.log(`Published ${event.eventType} for client ${event.clientId}`);
    }
    return success;
  }

  async consumeEvents(callback: (event: TradingEvent) => void): Promise<void> {
    if (!this.channel || !this.isConnected) return;

    await this.channel.consume('inter_service_events', (msg: Message | null) => {
      if (msg) {
        try {
          const event = JSON.parse(msg.content.toString());
          console.log('Received event:', event);
          
          // Handle DEPOSIT_RECEIVED events for auto-swap
          if (event.eventType === 'DEPOSIT_RECEIVED') {
            this.handleDepositReceived(event);
          }
          
          callback(event);
          this.channel?.ack(msg);
        } catch (error) {
          console.error('Error processing event:', error);
          this.channel?.nack(msg, false, true);
        }
      }
    });
  }

  /**
   * Handle DEPOSIT_RECEIVED events for auto-swap
   */
  private async handleDepositReceived(event: TradingEvent) {
    try {
      console.log('🔄 Processing DEPOSIT_RECEIVED event for auto-swap:', event);
      
      // Check if transaction quote exists in LiquidityRail admin
      const hasQuote = await this.checkLiquidityRailQuote(event.clientId, event.fromAsset, event.toAsset);
      
      if (!hasQuote) {
        console.log('⚠️ No transaction quote found in LiquidityRail admin, skipping auto-swap');
        return;
      }

      // const swapResult = await this.stellarSDEXTrading.executeMarketSwapAndPayout(event.id);
      // console.log('✅ Auto-swap completed successfully:', swapResult);

    } catch (error: any) {
      console.error('❌ Error handling DEPOSIT_RECEIVED event:', error);
    }
  }

  /**
   * Check if transaction quote exists in LiquidityRail admin
   */
  private async checkLiquidityRailQuote(clientId: string, fromAsset: string, toAsset: string): Promise<boolean> {
    try {
      const lrAdminUrl = process.env.LR_ADMIN_URL || 'http://localhost:3000';
      const response = await fetch(`${lrAdminUrl}/api/transaction-quotes/check`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.INTERNAL_API_KEY || 'internal-key'}`
        },
        body: JSON.stringify({
          clientId,
          fromAsset,
          toAsset
        })
      });

      const result = await response.json();
      return result.exists || false;

    } catch (error: any) {
      console.error('❌ Error checking LiquidityRail quote:', error);
      return false;
    }
  }

  async close(): Promise<void> {
    if (this.channel) await this.channel.close();
    if (this.connection) await this.connection.close();
    this.isConnected = false;
  }
}