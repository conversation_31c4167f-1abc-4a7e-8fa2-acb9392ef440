/**
 * Simple Trading Event Consumer
 */

import { TradingMessageBus, TradingEvent } from './messageBus';

class TradingEventConsumer {
  private messageBus = new TradingMessageBus();

  async connect() {
    await this.messageBus.connect();
  }

  async startConsuming(onEvent: (event: TradingEvent) => void) {
    await this.messageBus.consumeEvents(onEvent);
  }

  async close() {
    await this.messageBus.close();
  }
}

export const tradingEventConsumer = new TradingEventConsumer();
export default tradingEventConsumer;