/**
 * Simple Trading Event Publisher
 */

import { TradingMessageBus, TradingEvent } from './messageBus';

class TradingEventPublisher {
  private messageBus = new TradingMessageBus();

  async connect() {
    await this.messageBus.connect();
  }

  async publishSwapCompleted(swapId: string, clientId: string, fromAsset: string, toAsset: string, amount: number, status: 'completed' | 'failed' = 'completed', stellarHash?: string) {
    const event: TradingEvent = {
      eventType: 'SWAP_COMPLETED',
      id: swapId,
      clientId,
      fromAsset,
      toAsset,
      amount,
      status,
      stellarHash,
      timestamp: new Date().toISOString()
    };
    return await this.messageBus.publishEvent(event);
  }

  async publishDepositReceived(transactionId: string, clientId: string, fromAsset: string, toAsset: string, amount: number, status: 'completed' | 'failed' = 'completed', stellarHash?: string) {
    const event: TradingEvent = {
      eventType: 'DEPOSIT_RECEIVED',
      id: transactionId,
      clientId,
      fromAsset,
      toAsset,
      amount,
      status,
      stellarHash,
      timestamp: new Date().toISOString()
    };
    return await this.messageBus.publishEvent(event);
  }

  async publishPayoutMade(payoutId: string, clientId: string, fromAsset: string, toAsset: string, amount: number, status: 'completed' | 'failed' = 'completed', stellarHash?: string) {
    const event: TradingEvent = {
      eventType: 'PAYOUT_MADE',
      id: payoutId,
      clientId,
      fromAsset,
      toAsset,
      amount,
      status,
      stellarHash,
      timestamp: new Date().toISOString()
    };
    return await this.messageBus.publishEvent(event);
  }

  async close() {
    await this.messageBus.close();
  }
}

export const tradingEventPublisher = new TradingEventPublisher();
export default tradingEventPublisher;