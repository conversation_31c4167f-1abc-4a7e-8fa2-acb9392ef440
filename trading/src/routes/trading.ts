import express, { Request, Response } from 'express';
import {
    createOffer,
    getOrderBook as getOrderBook<PERSON>ontroller,
    cancelOrder,
    getTradingHistory,
    healthCheck,
    getOrderbookPrice,
    getAllOrders,
    cancelAllOrders,
    attachClientToOrder,
    cacheOffers,
    getTradingPairs,
    createPendingRailOrder,
    getPendingRailOrders,
    getTradingAccountPublicKey,
    getTradingAccountBalances,
    depositReceivedWebhook,
    getPairPrices,
} from '../controllers/trading';
import {
    generateLRQuote,
    getLRTransaction,
    mudaWebhook,
    confirmLRQuote,
    refreshLRQuote
} from '../controllers/lr.provider';
import { JWTMiddleware } from '../helpers/jwt.middleware';

const router = express.Router();

const applyJWTConditionally = (req: Request, res: Response, next: any) => {
    JWTMiddleware.verifyTokenAccess(req, res, next);
};

// Health check
router.get('/health', healthCheck);

// Balance and pricing
router.get('/pairs', getTradingPairs);
router.get('/pair-prices', getPairPrices);
router.post('/price', getOrderbookPrice);

// Order management
router.post('/orders', createOffer);
router.post('/orders/cancel', cancelOrder);
router.post('/orders/cancel-all/:clientId', cancelAllOrders);
router.post('/orders/attach-client', attachClientToOrder);
router.post('/orders/cache-offers', cacheOffers);
router.get('/orders/history', getTradingHistory);
router.get('/orders/all/:clientId', getAllOrders);
router.get('/orderbook/:baseAsset?/:counterAsset?', getOrderBookController);

// Stellar trades endpoint (separate from orders)
router.get('/trades/:clientId', getAllOrders);

// Rail orders (internal use)
router.post('/rail-orders', createPendingRailOrder);
router.get('/rail-orders', getPendingRailOrders);

// Trading account
router.get('/account', applyJWTConditionally, getTradingAccountPublicKey);
router.get('/account/balances', applyJWTConditionally, getTradingAccountBalances);

// ============= LIQUIDITY RAIL PROVIDER ENDPOINTS =============
// These endpoints are called by MudaProvider from liquidityRailAdmin
// They match the interface expected by MudaProvider.ts

router.post('/generate-lr-quote', generateLRQuote);
router.post('/create-payment-intent', confirmLRQuote);
router.post('/confirm-lr-quote', confirmLRQuote);
router.post('/refresh-lr-quote', refreshLRQuote);
router.get('/get-lr-transaction/:transId', getLRTransaction);
router.post('/lr-webhook', mudaWebhook);
router.post('/webhook/deposit-received', depositReceivedWebhook);


export default router; 