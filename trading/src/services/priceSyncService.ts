import cron from 'node-cron';
import StellarSDEXTrading from '../models/StellarSDEXTrading';
import Model from '../helpers/model';

export class PriceSyncService extends Model {
    private stellarSDEXTrading: StellarSDEXTrading;
    private isRunning: boolean = false;

    constructor() {
        super();
        this.stellarSDEXTrading = new StellarSDEXTrading();
    }

    /**
     * Start cron job to sync prices every 5 minutes
     */
    public startPriceSync() {
        return ;
        // Run every 5 minutes: '*/5 * * * *'
        cron.schedule('*/5 * * * *', async () => {
            if (this.isRunning) {
                console.log('⏭️  Price sync already running, skipping...');
                return;
            }

            try {
                this.isRunning = true;
                console.log('🔄 Starting price sync at', new Date().toISOString());
                await this.syncAllPairPrices();
                console.log('✅ Price sync completed at', new Date().toISOString());
            } catch (error: any) {
                console.error('❌ Error in price sync cron:', error);
            } finally {
                this.isRunning = false;
            }
        });

        console.log('📊 Price sync cron job started - runs every 5 minutes');

        // Run once immediately on startup
        setTimeout(() => this.syncAllPairPrices(), 5000);
    }

    /**
     * Sync prices for all enabled trading pairs
     */
    private async syncAllPairPrices() {
        try {
            // Get all enabled pairs from exchange_rates table
            const pairs: any = await this.callRawQuery(`
                SELECT id, base_currency, quote_currency, markup, markdown 
                FROM exchange_rates 
                WHERE enabled = 'active'
            `);

            if (!pairs || pairs.length === 0) {
                console.log('⚠️  No enabled trading pairs found');
                return;
            }

            console.log(`📊 Syncing prices for ${pairs.length} pairs...`);

            // Process pairs in batches to avoid overwhelming the system
            const batchSize = 10;
            for (let i = 0; i < pairs.length; i += batchSize) {
                const batch = pairs.slice(i, i + batchSize);
                await Promise.all(
                    batch.map((pair: any) => this.syncPairPrice(pair))
                );
                
                // Small delay between batches
                if (i + batchSize < pairs.length) {
                    await this.delay(2000);
                }
            }

        } catch (error: any) {
            console.error('❌ Error syncing all pair prices:', error);
        }
    }

    /**
     * Sync price for a single trading pair
     */
    private async syncPairPrice(pair: any) {
        try {
            const { base_currency, quote_currency, markup, markdown } = pair;

            // Get buy price (when user buys base_currency with quote_currency)
            const buyPriceResult = await this.stellarSDEXTrading.getPrice(
                base_currency,
                quote_currency,
                'buy',
                1,
                'market'
            );

            // Get sell price (when user sells base_currency for quote_currency)
            const sellPriceResult = await this.stellarSDEXTrading.getPrice(
                base_currency,
                quote_currency,
                'sell',
                1,
                'market'
            );

            if (!buyPriceResult.success || !sellPriceResult.success) {
                console.log(`⚠️  Could not fetch price for ${base_currency}/${quote_currency}`);
                return;
            }

            const buyPrice = parseFloat(buyPriceResult.price);
            const sellPrice = parseFloat(sellPriceResult.price);

            // Calculate mid price as reference
            const midPrice = ((buyPrice + sellPrice) / 2).toFixed(7);

            // Apply markup/markdown if configured
            const finalBuyPrice = markup ? (buyPrice * (1 + markup / 100)) : buyPrice;
            const finalSellPrice = markdown ? (sellPrice * (1 - markdown / 100)) : sellPrice;

            // Update the exchange_rates table with live rates
            await this.callQuerySafe(`
                UPDATE exchange_rates 
                SET referencePrice = ?,
                    buy_rate = ?,
                    sell_rate = ?,
                    last_price_sync = NOW(),
                    updated_at = NOW(),
                    updatedBy = 'system'
                WHERE base_currency = ? 
                AND quote_currency = ?
            `, [
                midPrice, 
                finalBuyPrice.toFixed(7), 
                finalSellPrice.toFixed(7), 
                base_currency, 
                quote_currency
            ]);

            console.log(`✅ Updated ${base_currency}/${quote_currency}: buy=${finalBuyPrice.toFixed(7)}, sell=${finalSellPrice.toFixed(7)}, mid=${midPrice}`);

        } catch (error: any) {
            console.error(`❌ Error syncing price for ${pair.base_currency}/${pair.quote_currency}:`, error.message);
        }
    }

    /**
     * Manually trigger price sync (useful for testing or on-demand updates)
     */
    public async manualSync() {
        if (this.isRunning) {
            return { success: false, message: 'Price sync already in progress' };
        }

        try {
            this.isRunning = true;
            await this.syncAllPairPrices();
            return { success: true, message: 'Price sync completed successfully' };
        } catch (error: any) {
            console.error('❌ Error in manual price sync:', error);
            return { success: false, message: error.message };
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * Get price sync status
     */
    public getStatus() {
        return {
            isRunning: this.isRunning,
            lastRun: new Date().toISOString()
        };
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Export singleton instance
export const priceSyncService = new PriceSyncService();

