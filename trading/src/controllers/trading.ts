import { Request, Response } from 'express';
import StellarSDEXTrading, { WebhookData } from '../models/StellarSDEXTrading';

// Use SDEX model for main trading operations
const stellarSDEXTrading = new StellarSDEXTrading();

export async function getOrderBook(req: Request, res: Response) {
    try {
        const result = await stellarSDEXTrading.getOrderBook(
            req.params.baseAsset,
            req.params.counterAsset,
            req.body.clientId || ''
        );

        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function getAllOrders(req: Request, res: Response) {
    try {
        // Use SDEX to get trade history from Stellar Horizon
        const result = await stellarSDEXTrading.getTradeHistory(
            req.params.clientId,
            req.query.baseAsset as string,
            req.query.counterAsset as string
        );
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function cancelAllOrders(req: Request, res: Response) {
    try {
        const result = await stellarSDEXTrading.cancelAllOrders(req.body.clientId);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}



export async function getOrderbookPrice(req: Request, res: Response) {
    try {
        const { baseAsset, counterAsset, orderType, amount, type } = req.body;

        // Validate required parameters
        if (!baseAsset || !counterAsset || !orderType) {
            return res.status(400).json({
                status: 400,
                message: 'Missing required parameters: baseAsset, counterAsset, and orderType are required',
                data: null
            });
        }

        if (orderType !== 'buy' && orderType !== 'sell') {
            return res.status(400).json({
                status: 400,
                message: 'Invalid orderType. Must be "buy" or "sell"',
                data: null
            });
        }

        const result = await stellarSDEXTrading.getPrice(
            baseAsset,
            counterAsset,
            orderType,
            amount || 1,
            type || 'limit'
        );

        return res.status(result.success ? 200 : 400).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function createOffer(req: Request, res: Response) {
    try {
        const result = await stellarSDEXTrading.createOffer(req.body);
        return res.status(200).json(result);

    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}


export async function cancelOrder(req: Request, res: Response) {
    try {
        // Use SDEX for cancel order
        const result = await stellarSDEXTrading.cancelOrder(req.body.orderId, req.body.clientId);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}



export async function attachClientToOrder(req: Request, res: Response) {
    try {
        const { orderId, clientId } = req.body;
        if (!orderId || !clientId) {
            return res.status(400).json({ status: 400, message: 'Order ID and Client ID are required', data: null });
        }
        const result = await stellarSDEXTrading.attachClientToOrder(orderId, clientId);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function cacheOffers(req: Request, res: Response) {
    try {
        const { publicKey, clientId } = req.body;
        if (!publicKey || !clientId) {
            return res.status(400).json({ status: 400, message: 'Public Key and Client ID are required', data: null });
        }
        const result = await stellarSDEXTrading.cacheOffers(publicKey, clientId);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}



export async function getTradingHistory(req: Request, res: Response) {
    try {
        // Use SDEX for trade history
        const result = await stellarSDEXTrading.getTradeHistory(
            req.body.clientId || req.query.clientId as string,
            req.body.baseAsset || req.query.baseAsset as string,
            req.body.counterAsset || req.query.counterAsset as string
        );
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function getUserOrders(req: Request, res: Response) {
    try {
        // Use SDEX for order history
        const result = await stellarSDEXTrading.getOrderHistory(
            req.body.clientId,
            req.body.baseAsset,
            req.body.counterAsset
        );
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function healthCheck(req: Request, res: Response) {
    try {
        return res.status(200).json({
            status: 200,
            message: 'Trading service is healthy',
            data: { service: 'stellar-trading', timestamp: new Date().toISOString(), version: '1.0.0' }
        });
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: 'Trading service is unhealthy', data: null });
    }
}

export async function getTradingAccountPublicKey(req: Request, res: Response) {
    try {
        const { clientId } = req.body;
        const result = await stellarSDEXTrading.getTradingAccountPublicKey(clientId);
        if (result) {
            return res.status(200).json({ status: 200, message: 'Trading account retrieved', data: result });
        } else {
            return res.status(404).json({ status: 404, message: 'Trading account not found', data: null });
        }
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function getTradingAccountBalances(req: Request, res: Response) {
    try {
        const { clientId } = req.body;
        const balances = await stellarSDEXTrading.getTradingAccountBalances(clientId);
        return res.status(200).json({ status: 200, message: 'Trading account balances retrieved', data: balances });
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function depositReceivedWebhook(req: Request, res: Response) {
    try {
        const webhookData = req.body as WebhookData;
        const swapResult = await stellarSDEXTrading.handleDepositReceivedWebhook(webhookData);
        console.log('🔄 Auto-swap completed successfully:', swapResult);
        return res.status(swapResult.status).json(swapResult);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}
export async function createPendingRailOrder(req: Request, res: Response) {
    try {
        const result = await stellarSDEXTrading.createPendingRailOrder({
            ...req.body,
            expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : undefined
        });
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}


export async function getTradingPairs(req: Request, res: Response) {
    try {
        const result = await stellarSDEXTrading.getTradingPairs();
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}
export async function getPendingRailOrders(req: Request, res: Response) {
    try {
        const result = await stellarSDEXTrading.getPendingRailOrders(req.body.clientId);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function getRailOrderRate(req: Request, res: Response) {
    try {
        const result = await stellarSDEXTrading.getPrice(req.body.baseAsset, req.body.counterAsset, req.body.orderType);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function getPairPrices(req: Request, res: Response) {
    try {
        const { baseAsset, counterAsset } = req.query;

        // Build query to get pair prices with live rates
        let query = `
            SELECT id, base_currency, quote_currency, pair, referencePrice, 
                   buy_rate, sell_rate, last_price_sync,
                   markup, markdown, updated_at, updatedBy
            FROM exchange_rates 
            WHERE enabled = 'active'
        `;

        const params: any[] = [];

        // Filter by base asset (USDT, USDC, etc.)
        if (baseAsset) {
            query += ` AND base_currency = ?`;
            params.push(baseAsset);
        } else {
            // Default to USDT and USDC pairs
            query += ` AND base_currency IN ('USDT', 'USDC')`;
        }

        // Filter by counter asset if specified
        if (counterAsset) {
            query += ` AND quote_currency = ?`;
            params.push(counterAsset);
        }

        query += ` ORDER BY base_currency, quote_currency`;

        const pairs: any = await stellarSDEXTrading.callQuerySafe(query, params);

        if (!pairs || pairs.length === 0) {
            return res.status(404).json({
                status: 404,
                message: 'No trading pairs found',
                data: []
            });
        }

        // Format response with price details
        const formattedPairs = pairs.map((pair: any) => {
            const midPrice = parseFloat(pair.referencePrice || '0');
            const buyRate = parseFloat(pair.buy_rate || '0');
            const sellRate = parseFloat(pair.sell_rate || '0');
            const markup = parseFloat(pair.markup || '0');
            const markdown = parseFloat(pair.markdown || '0');

            return {
                pair: `${pair.base_currency}/${pair.quote_currency}`,
                baseCurrency: pair.base_currency,
                quoteCurrency: pair.quote_currency,
                midPrice: midPrice.toFixed(7),
                buyRate: buyRate.toFixed(7),
                sellRate: sellRate.toFixed(7),
                markup: markup,
                markdown: markdown,
                lastPriceSync: pair.last_price_sync,
                lastUpdated: pair.updated_at,
                updatedBy: pair.updatedBy
            };
        });

        return res.status(200).json({
            status: 200,
            message: 'Pair prices retrieved successfully',
            data: {
                pairs: formattedPairs,
                count: formattedPairs.length,
                timestamp: new Date().toISOString()
            }
        });

    } catch (error: any) {
        console.error('Error getting pair prices:', error);
        return res.status(500).json({
            status: 500,
            message: error.message,
            data: null
        });
    }
}

export async function getTraderAccessKey(req: Request, res: Response) {
    try {
        const result = await stellarSDEXTrading.getDecryptedApiKey(req.params.clientId);
        return res.status(200).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}