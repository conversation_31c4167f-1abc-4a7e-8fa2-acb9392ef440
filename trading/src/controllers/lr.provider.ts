/**
 * Liquidity Rail Provider Controller
 * Handles endpoints that external LR providers call (like MudaProvider)
 * This service acts as a provider for the LiquidityRailAdmin
 */

import { Request, Response } from 'express';
import StellarSDEXTrading from '../models/StellarSDEXTrading';
import {
    CreatePaymentIntentData,
    QuoteRequest,
    CreatePaymentIntentResponse,
    GetQuoteResponse,
    GetTransactionResponse,
    WebhookResponse,
    CryptoResponse,
    QuoteResponse,
    TransactionResponse,
} from '../interfaces/lr.provider.interfaces';

const stellarSDEXTrading = new StellarSDEXTrading();


/**
 * POST /generate-lr-quote
 * Generates a quote for currency exchange
 * Expected by MudaProvider.getQuote()
 */
export async function generateLRQuote(req: Request, res: Response) {
    try {
        const data: QuoteRequest = req.body;

        // Validate required fields
        if (!data.amount || !data.currency || !data.asset_code) {
            return res.status(400).json({
                status: 400,
                message: 'Missing required fields: amount, currency, asset_code',
                data: null
            });
        }

        // Get market price using getMarketPrice
        const marketPriceResult = await stellarSDEXTrading.getMarketPrice(
            data.asset_code,  // base asset (USDT/USDC)
            data.currency,    // counter asset (UGX/KES/etc)
            data.amount.toString(),
            'sell',  // Selling USDT for UGX
            0.5  // 0.5% slippage
        );

        if (!marketPriceResult.success || marketPriceResult.status !== 200) {
            return res.status(400).json({
                status: 400,
                message: marketPriceResult.message || 'Unable to get exchange rate',
                data: null
            });
        }

        // Calculate amounts
        const fromAmount = parseFloat(data.amount.toString());
        const toAmount = parseFloat(marketPriceResult.data.estimatedReceiveAmount || '0');
        const rate = parseFloat(marketPriceResult.price || '0');
        const fee = toAmount * 0.01;
        const totalAmount = toAmount - fee;

        // Generate quote
        const quoteId = `QUOTE_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const expiresAt = new Date(Date.now() + 5 * 60 * 1000).toISOString(); // 5 minutes
        const chain = data.chain || 'BSC'; // Default to BSC if not provided

        const quoteResponse: QuoteResponse = {
            quote_id: quoteId,
            rate: rate.toString(),
            total_amount: totalAmount.toString(),
            fee: fee.toString(),
            expires_at: expiresAt,
            from_currency: data.asset_code,
            to_currency: data.currency,
            from_amount: fromAmount.toString(),
            to_amount: toAmount.toString(),
            chain: chain
        };

        const response: GetQuoteResponse = {
            status: 200,
            message: 'Quote generated successfully',
            data: quoteResponse
        };

        return res.status(200).json(response);
    } catch (error: any) {
        console.error('Error generating quote:', error);
        return res.status(500).json({
            status: 500,
            message: error.message || 'Failed to generate quote',
            data: null
        });
    }
}

/**
 * GET /get-lr-transaction/:transId
 * Gets transaction status and details
 * Expected by MudaProvider.getTransaction()
 */
export async function getLRTransaction(req: Request, res: Response) {
    try {
        const { transId } = req.params;

        if (!transId) {
            return res.status(400).json({
                status: 400,
                message: 'Transaction ID is required',
                data: null
            });
        }

        // Query database directly for single transaction
        const query = `
            SELECT * FROM quote_requests 
            WHERE request_id = ? OR reference_id = ? OR order_id = ?
            LIMIT 1
        `;
        
        const orders = await stellarSDEXTrading.callRawQuery(query, [transId, transId, transId]);

        if (!orders || orders.length === 0) {
            return res.status(404).json({
                status: 404,
                message: 'Transaction not found',
                data: null
            });
        }

        const order = orders[0];

        // Calculate exchange rate
        const fromAmount = parseFloat(order.amount || '0');
        const toAmount = parseFloat(order.expected_amount || '0');
        const exchangeRate = fromAmount > 0 ? (toAmount / fromAmount).toFixed(7) : '0';
        const fee = (toAmount * 0.01).toFixed(7); // 1% fee

        // Map to TransactionResponse format expected by MudaProvider
        const transactionResponse: TransactionResponse = {
            transaction_id: order.request_id || order.order_id || transId,
            reference_id: order.reference_id || transId,
            status: order.status?.toUpperCase() || 'PENDING',
            from_currency: order.source_asset || 'USDT',
            to_currency: order.destination_asset || 'UGX',
            from_amount: order.amount?.toString() || '0',
            to_amount: order.expected_amount?.toString() || '0',
            exchange_rate: exchangeRate,
            fee: fee,
            payment_method: {
                address: '', // Will be provided after quote confirmation
                asset_code: order.source_asset || 'USDT',
                chain: 'STELLAR',
                memo: order.reference_id || transId
            },
            created_at: order.created_at || new Date().toISOString(),
            updated_at: order.updated_at || new Date().toISOString(),
            expires_at: order.expires_at
        };

        const response: GetTransactionResponse = {
            status: 200,
            message: 'Transaction retrieved successfully',
            data: transactionResponse
        };

        return res.status(200).json(response);
    } catch (error: any) {
        console.error('Error getting transaction:', error);
        return res.status(500).json({
            status: 500,
            message: error.message || 'Failed to get transaction',
            data: null
        });
    }
}


export async function mudaWebhook(req: Request, res: Response) {
    try {
        const eventType = req.headers['x-muda-event-type'] || 'general';
        const signature = req.headers['x-webhook-signature'];
        const timestamp = req.headers['x-webhook-timestamp'];

        console.log('Received webhook:', {
            eventType,
            signature,
            timestamp,
            body: req.body
        });

        // TODO: Verify webhook signature
        // TODO: Process webhook event based on type

        const response: WebhookResponse = {
            status: 200,
            message: 'Webhook received successfully',
            data: { received: true }
        };

        return res.status(200).json(response);
    } catch (error: any) {
        console.error('Error processing webhook:', error);
        return res.status(500).json({
            status: 500,
            message: error.message || 'Failed to process webhook',
            data: null
        });
    }
}


export async function confirmLRQuote(req: Request, res: Response) {
    try {
        const data: CreatePaymentIntentData = req.body;

        // Validate required fields
        if (!data.reference_id || !data.amount || !data.from_currency || !data.to_currency) {
            return res.status(400).json({
                status: 400,
                message: 'Missing required fields: reference_id, amount, from_currency, to_currency',
                data: null
            });
        }

  
        // Create pending rail order using createPendingRailOrder
        const railOrderResult = await stellarSDEXTrading.createPendingRailOrder({
            clientId: req.body.clientId || 'SYSTEM',
            referenceId: data.reference_id,
            sourceAsset: data.from_currency,
            destinationAsset: data.to_currency,
            chain:data.chain || '',
            amount: data.amount.toString(),
            expectedAmount: req.body.expected_amount,
            slippage: req.body.slippage || 0.5,
            payoutData: req.body.payment_method,
            paymentMethod: data.payment_method,
            expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : new Date(Date.now() + 5 * 60 * 1000)
        });

     


        return res.status(railOrderResult.status).json(railOrderResult);
    } catch (error: any) {
        console.error('Error confirming quote:', error);
        return res.status(500).json({
            status: 500,
            message: error.message || 'Failed to confirm quote',
            data: null
        });
    }
}

/**
 * POST /refresh-lr-quote
 * Refreshes an existing quote with new rates
 */
export async function refreshLRQuote(req: Request, res: Response) {
    try {
        // Generate a new quote using the same logic as generateLRQuote
        const data: QuoteRequest = req.body;

        // Validate required fields
        if (!data.amount || !data.currency || !data.asset_code) {
            return res.status(400).json({
                status: 400,
                message: 'Missing required fields: amount, currency, asset_code',
                data: null
            });
        }

        // Get fresh market price
        const marketPriceResult = await stellarSDEXTrading.getMarketPrice(
            data.asset_code,
            data.currency,
            data.amount.toString(),
            'sell',
            0.5
        );

        if (!marketPriceResult.success || marketPriceResult.status !== 200) {
            return res.status(400).json({
                status: 400,
                message: marketPriceResult.message || 'Unable to get exchange rate from orderbook',
                data: null
            });
        }

        // Calculate amounts
        const fromAmount = parseFloat(data.amount.toString());
        const toAmount = parseFloat(marketPriceResult.data.estimatedReceiveAmount || '0');
        const rate = parseFloat(marketPriceResult.price || '0');
        const fee = toAmount * 0.01; // 1% fee
        const totalAmount = toAmount - fee;

        // Generate refreshed quote
        const quoteId = req.body.quote_id || `QUOTE_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const expiresAt = new Date(Date.now() + 5 * 60 * 1000).toISOString(); // 5 minutes
        const chain = data.chain || 'BSC'; // Default to BSC if not provided

        const quoteResponse: QuoteResponse = {
            quote_id: quoteId,
            rate: rate.toString(),
            total_amount: totalAmount.toString(),
            fee: fee.toString(),
            expires_at: expiresAt,
            from_currency: data.asset_code,
            to_currency: data.currency,
            from_amount: fromAmount.toString(),
            to_amount: toAmount.toString(),
            chain: chain
        };

        const response: GetQuoteResponse = {
            status: 200,
            message: 'Quote refreshed successfully',
            data: quoteResponse
        };

        return res.status(200).json(response);
    } catch (error: any) {
        console.error('Error refreshing quote:', error);
        return res.status(500).json({
            status: 500,
            message: error.message || 'Failed to refresh quote',
            data: null
        });
    }
}