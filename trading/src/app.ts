import express, { Request, Response } from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';
import dotenv from 'dotenv';
import tradingRoutes from './routes/trading';  // Stellar trading routes
import tradingEventPublisher from './utils/eventPublisher';
import { tradingEventConsumer } from './utils/eventConsumer';
import { priceSyncService } from './services/priceSyncService';
import logger from './utils/logger';
import './tests/test';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3006; // Default to 3006 for trading service

app.use(cors());
app.use(bodyParser.json());
app.use(express.json({ limit: '50mb' })); // Increase JSON payload limit to 50 MB
app.use(express.urlencoded({ limit: '50mb', extended: true })); // Increase URL-encoded payload limit
app.set('trust proxy', true);

app.use('/trading', tradingRoutes);
app.use('/health', (req: Request, res: Response) => {
  res.status(200).json({ status: 'ok', service: 'trading' });
});

app.listen(PORT, async () => {
  logger.info(`Trading service started on port ${PORT}`);
  
  // Initialize event publisher
  try {
    await tradingEventPublisher.connect();
    logger.info('Trading event publisher connected');
  } catch (error) {
    logger.error('Failed to connect trading event publisher', error);
  }

  // Initialize event consumer for auto-swap
  try {
    await tradingEventConsumer.connect();
    await tradingEventConsumer.startConsuming((event) => {
      logger.info('Trading service received event:', event.eventType);
    });
    logger.info('Trading event consumer started - listening for DEPOSIT_RECEIVED events');
  } catch (error) {
    logger.error('Failed to start trading event consumer', error);
  }

  // Initialize price sync service
  try {
    priceSyncService.startPriceSync();
    logger.info('Price sync service started - runs every 5 minutes');
  } catch (error) {
    logger.error('Failed to start price sync service', error);
  }

});
