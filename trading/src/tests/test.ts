import StellarService from "../helpers/StellarService";
import StellarSDEXTrading from "../models/StellarSDEXTrading";

async function test() {
    const resp1 = await new StellarSDEXTrading().getOrderBook('USDT', 'UGX', 'TEST');
    console.log('🧪 Testing getOrderBook with:', resp1);
    return;
 
}

async function testCreateTradingServiceOrder() {
    const requestData = {
        clientId: '1000',
        referenceId: 'TEST',
        sourceAsset: 'USDT',
        destinationAsset: 'UGX',
        chain: 'BSC',
        amount: '100',
        slippage: 1,
        paymentMethod: {
            type: 'mobile_money' as const,
            currency: 'UGX',
            phone_number: '+************',
            country_code: 'UG',
            network: 'MTN',
            account_name: 'Test User'
        },
        payoutData: {
            mobileNumber: '+************',
            amount: '380000',
            currency: 'UGX',
            provider: 'MTN'
        },
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
    };
    const result = await new StellarSDEXTrading().createPendingRailOrder(requestData);
    console.log('🧪 Testing createTradingServiceOrder with:', result);
}

testCreateTradingServiceOrder();
