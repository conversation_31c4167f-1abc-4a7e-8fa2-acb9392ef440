import BaseModel from "./base.model";
import { v4 as uuidv4 } from 'uuid';
import CryptoJ<PERSON> from "crypto-js";
import { Keypair } from "@stellar/stellar-sdk";
import jwt from "jsonwebtoken";
import axios from "axios";

const SECRET_KEY = process.env.SECRET_KEY || ""

export default class Model extends BaseModel {

    async GetIssuerAccount(asset_code: string, arg1: string) {
        return process.env.STELLAR_PAYOUT_ISSUER_SECRET || "";
    }

    async getDecryptedApiKey(clientId: string) {
        try {

            if(clientId.length > 12 || clientId.length < 3) {
                return null
            }
         
            const tradingAccount: any = await this.selectDataQuerySafe("trading_accounts", { client_id: clientId });

            if (tradingAccount.length > 0) {
                const decryptedSecretKey = CryptoJS.AES.decrypt(tradingAccount[0].secret_key, SECRET_KEY)
                    .toString(CryptoJS.enc.Utf8);

                return {
                    client_id: clientId,
                    public_key: tradingAccount[0].public_key,
                    secret_key: decryptedSecretKey
                }
            }

            console.log(`No trading account found for client ${clientId}, creating new trading account...`);
            return this.createTradingAccount(clientId);

        } catch (error: any) {
            console.error("Error retrieving trading account keys:", error);
            return null;
        }
    }


    async createTradingAccount(clientId: string) {
        try {

            const keyPair = Keypair.random();
            let publicKey = keyPair.publicKey();
            let secretKey = keyPair.secret();


            if (clientId === process.env.TRADER_ACCOUNT) {
                const tradingAccount: any = await this.selectDataQuerySafe("client_wallets", { client_id: clientId });
                if (tradingAccount.length > 0) {
                    const decryptedSecretKey = CryptoJS.AES.decrypt(tradingAccount[0].secret_key, SECRET_KEY)
                        .toString(CryptoJS.enc.Utf8);
                    publicKey = tradingAccount[0].public_key;
                    secretKey = decryptedSecretKey;
                }
            }

            // Encrypt the secret key before storing
            const encryptedSecretKey = CryptoJS.AES.encrypt(secretKey, SECRET_KEY).toString();

            const tradingAccountData = {
                client_id: clientId,
                public_key: publicKey,
                secret_key: encryptedSecretKey,
                status: 'active',
                created_at: new Date(),
                updated_at: new Date()
            };

            await this.insertData('trading_accounts', tradingAccountData);

            console.log(`✅ Trading account created for client ${clientId}:`, publicKey);

            return {
                client_id: clientId,
                public_key: publicKey,
                secret_key: secretKey
            };

        } catch (error: any) {
            console.error("Error creating trading account:", error);
            return null;
        }
    }

    /**
     * Get the issuer for a trading asset
     * All fiat tokens use the same issuer from STELLAR_PAYOUT_ISSUER_PUBLIC
     */
    async GetTradingIssuer(asset: string, clientId: string) {
        // All fiat assets use the same issuer
        const issuer = process.env.STELLAR_PAYOUT_ISSUER_PUBLIC || "";

        if (!issuer) {
            console.error(`⚠️  STELLAR_PAYOUT_ISSUER_PUBLIC not configured for asset ${asset}`);
            throw new Error('STELLAR_PAYOUT_ISSUER_PUBLIC environment variable is required');
        }

        return issuer;
    }

    /**
     * Get all supported trading assets from the database (fiat and stablecoins)
     */
    async getSupportedFiatAssets(): Promise<string[]> {
        try {
            const assets: any = await this.callRawQuery(
                `SELECT DISTINCT asset_code FROM supported_currencies WHERE asset_type IN ('fiat', 'stableCoin') AND asset_code IS NOT NULL`
            );

            if (assets && assets.length > 0) {
                return assets.map((row: any) => row.asset_code);
            }
            return []
        } catch (error: any) {
            console.error("Error getting supported trading assets:", error);
            // Return default supported assets as fallback
            return [];
        }
    }

    /**
     * Get trading account public key (for frontend/wallet service to send transfers)
     * Returns only the public key, not the secret
     */
    async getTradingAccountPublicKey(clientId: string) {
        try {
            const tradingAccount: any = await this.selectDataQuerySafe("trading_accounts", { client_id: clientId });

            if (tradingAccount.length > 0) {
                return {
                    client_id: clientId,
                    public_key: tradingAccount[0].public_key,
                    status: tradingAccount[0].status
                };
            }

            // If no trading account exists, create one
            console.log(`No trading account found for client ${clientId}, creating new trading account...`);
            const newAccount = await this.createTradingAccount(clientId);

            if (newAccount) {
                return {
                    client_id: newAccount.client_id,
                    public_key: newAccount.public_key,
                    status: 'active'
                };
            }

            return null;

        } catch (error: any) {
            console.error("Error retrieving trading account public key:", error);
            return null;
        }
    }

    /**
     * Get trading account balances (actual Stellar balances from trading account)
     * Returns all supported assets with balances minus on-hold amounts
     */
    async getTradingAccountBalances(clientId: string) {
        try {
            const currencyInfo: any = await this.getClientCurrencies(clientId);
            const currencies = currencyInfo.data;

            // Get the balances from StellarService
            const { default: StellarService } = await import("./StellarService");
            const stellar = new StellarService();
            const balances: any = await stellar.getBalance(clientId) || [];

            // For each currency, find a matching balance; if not found, set it to "0"
            for (const currency of currencies) {
                const matchingBalance = balances.find((b: any) => currency.asset_code === b.code);
                if (matchingBalance) {
                    currency.available = Number(matchingBalance.available);
                    currency.total = Number(matchingBalance.total);
                    currency.locked = Number(matchingBalance.locked || 0);
                } else {
                    currency.available = 0;
                    currency.total = 0;
                    currency.locked = 0;
                }
            }

            return currencies;
        } catch (error: any) {
            console.error("Error getting trading account balances:", error);
            throw error;
        }
    }

    /**
     * Check if an asset is supported for trading (fiat or stablecoin)
     */

    async saveTransactionLog(trans_id: string, status: string, step: any, response_code: number, description: string, data: any) {
        try {
            const logData = {
                trans_id: trans_id,
                status: status,
                step: step,
                response_code: response_code,
                description: description,
                data: typeof data === 'string' ? data : JSON.stringify(data)
            }
            return await this.insertData("transactions_log", logData);
        } catch (error: any) {
            console.error("Error saving transaction log:", error);
            return false;
        }

    }

    async updateTransaction(trans_id: string, status: "SUCCESS" | "PENDING_APPROVAL" | "PROCESSING" | "PENDING_REVERSAL" | "FAILED" | "ONHOLD" | "PENDING" | "RECEIVED" | "MINT_INITIATED" | "MINT_FAILED", message: string, data: any = null) {
        try {
            this.saveTransactionLog(trans_id, status, "UPDATE_BALANCE", 200, "UPDATING TRANSACTION", data);
            const transaction = await this.selectDataQuerySafe("transactions", { trans_id });
            if (transaction.length === 0) {
                this.saveTransactionLog(trans_id, status, "UPDATE_BALANCE", 404, "Transaction not found", data);
                return this.makeResponse(404, "Transaction not found");
            }

            const { currency, client_id, asset_code, status: oldStatus } = transaction[0];
            if (oldStatus == "SUCCESS" || oldStatus == "FAILED") {
                return this.makeResponse(200, "Balance updated successfully", data);
            }

            const updated = {
                status: status,
            }
            await this.updateData(
                "transactions",
                `trans_id='${trans_id}'`,
                updated
            );

            try {
                const updatedMessage = {
                    message: `${message}`,
                }
                await this.updateData(
                    "transactions",
                    `trans_id='${trans_id}'`,
                    updatedMessage
                );
            } catch (error: any) {
                console.error("Error updating transaction message:", error);
            }




            // Import StellarService here to avoid circular dependency


            if (status == 'SUCCESS') {

                const { default: StellarService } = await import("./StellarService");
                const stellar = new StellarService();
                const balances = await stellar.getSingleBalance(client_id, asset_code);
                if (balances == null) {
                    return this.makeResponse(404, "Transaction not found");
                }
                const { balance } = balances;
                // Format date for MySQL: YYYY-MM-DD HH:MM:SS
                const mysqlDatetime = new Date().toISOString().slice(0, 19).replace('T', ' ');
                await this.updateData("transactions", `trans_id='${trans_id}'`, {
                    "running_balance": balance,
                    status: status,
                    balance_updated_at: mysqlDatetime
                });
            }

            //   this.saveTransactionLog(trans_id, status, Steps.UPDATE_BALANCE, 200, "Balance updated successfully", data);
            return this.makeResponse(200, "Balance updated successfully");
        } catch (error: any) {
            console.error("Error updating current balance:", error);
            this.saveTransactionLog(trans_id, status, "UPDATE_BALANCE_FAILED", 500, "Failed to update balance", error);
            return this.makeResponse(500, "Failed to update balance", error.message);
        }
    }

    async isAssetSupported(assetCode: string): Promise<boolean> {
        try {
            const asset: any = await this.callQuerySafe(
                `SELECT id FROM supported_currencies WHERE asset_code = ? AND asset_type IN ('fiat', 'stableCoin') LIMIT 1`,
                [assetCode]
            );

            return asset && asset.length > 0;

        } catch (error: any) {
            console.error(`Error checking if asset ${assetCode} is supported:`, error);
            // Fallback to hardcoded list
            const defaultSupported = ['UGX', 'KES', 'USD', 'EUR', 'GBP', 'NGN', 'GHS', 'TZS', 'ZAR', 'USDC', 'USDT'];
            return defaultSupported.includes(assetCode);
        }
    }

    async userFaAuthAccountStatus(data: any) {
        // Mock implementation - return mock 2FA status
        return [{ status: 'active', secret: 'mock_secret' }];
    }

    async confirmUser2Fa(data: any) {
        // Mock implementation - always return success
        return { status: true, message: '' };
    }

    async getUserClientLogin(email: string) {
        // Mock implementation
        return [{ id: 'mock_user_id', client_id: 'mock_client_id' }];
    }

    async getProfile(client_id: string) {
        // Mock implementation
        return [{ client_id, business_name: 'Mock Business' }];
    }

    async defaultRole() {
        return { id: 'default_role_id', name: 'Default Role' };
    }

    async defaultClientRole() {
        return { id: 'default_client_role_id', name: 'Client Role' };
    }

    async getCampanyOwner(clientId: string, userId: string) {
        // Mock implementation
        return [{ id: userId, client_id: clientId }];
    }

    async checkDefaultPasswordExpiry(expiry: string, hours: number) {
        // Mock implementation
        return true;
    }

    async generatePassword(password: string) {
        // Mock implementation
        return 'hashed_' + password;
    }

    async verifyPassword(password: string, hash: string) {
        // Mock implementation
        return hash === 'hashed_' + password;
    }

    async generateSecurePassword(length: number, includeSpecial: boolean) {
        // Mock implementation
        return 'secure_password_' + Math.random().toString(36).substr(2, length);
    }

    async validatePasswordPolicy(password: string) {
        // Mock implementation
        return {
            isValid: true,
            errors: [],
            strength: 'strong',
            score: 100
        };
    }

    async sendEmail(type: string, email: string, name?: string, password?: string) {
        // Mock implementation
        console.log('Mock email sent:', { type, email, name, password });
        return true;
    }

    /**
     * Generate a unique transaction ID
     */
    getTransId(): string {
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 11);
        return `txn_${timestamp}_${randomString}`;
    }

    /**
     * Make a standard API response
     */
    makeResponse(status: number, message: string, data: any = null) {
        return {
            status,
            message,
            data
        };
    }

    async getOtp(email: string, userId: string) {
        // Mock implementation
        return '123456';
    }

    async saveOperationLog(operationType: string, clientId: string, userId: string, userType: string, tableName: string, recordId: string, newData: any, ipAddress: string = "1") {
        // Mock implementation
        console.log('Mock operation log:', { operationType, clientId, userId, userType, tableName, recordId, newData, ipAddress });
        return true;
    }

    async checkAllowedAccess(role: string, userType: string) {
        // Mock implementation
        return true;
    }

    async parseContentData(data: any) {
        // Mock implementation
        return data;
    }



    async getClientCurrencies(clientId: string) {
        const client = await this.callRawQuery(`SELECT * FROM supported_currencies where asset_type in ('fiat', 'stableCoin')`);
        return this.makeResponse(200, "Currencies fetched successfully", client);
    }



    // Removed duplicate methods - using base class methods

    async constructSmsMessage(message: string, name: string, otp: string, listHtml: string = '', code: string = '') {
        // Mock implementation
        return message.replace('{name}', name).replace('{otp}', otp);
    }

    async sendDirectEmail(email: string, subject: string, message: string) {
        // Mock implementation
        console.log('Mock email sent:', { email, subject, message });
    }

    async sendBalanceAlert(trans_id: string, service_name: any, chain: any, amount: string | number, currency: string, clientId: string | number) {
        // Mock implementation
        console.log('Mock balance alert:', { trans_id, service_name, chain, amount, currency, clientId });
    }

    async mapDescription(description: string) {
        if (typeof description === "string" && description.includes("TARGET_AUTHORIZATION_ERROR")) {
            return "Insufficient balance"
        }
        if (typeof description === "string" && description.includes("FAILED")) {
            return "Not authorized"
        }
        return description
    }

    async getPaymentMethodById(payment_method_id: any) {
        // Mock implementation
        return null;
    }

    async composeThirdPartyTransaction(transaction: any) {
        // Mock implementation
        return {
            trans_id: transaction.trans_id,
            client_id: transaction.client_id,
            service_name: transaction.service_name,
            product_id: transaction.product_id,
            trans_type: transaction.trans_type,
            amount: transaction.amount,
            currency: transaction.currency,
            status: 'pending'
        };
    }

    async sendToWallet(data: any) {
        // Mock implementation
        console.log('Mock send to wallet:', data);
        return { success: true };
    }

    async sendNotification(token: string, data: any) {
        // Mock implementation
        console.log('Mock notification sent:', { token, data });
        return { success: true };
    }

    async sendSms(token: string, data: any) {
        // Mock implementation
        console.log('Mock SMS sent:', { token, data });
        return false;
    }

    async sendEmailWithTemplate(email: string, template: string, name: string, otp: string, listHtml: string = '', code: string = '') {
        // Mock implementation
        console.log('Mock email with template sent:', { email, template, name, otp });
        return true;
    }

    protected async generateDepositAddress(data: {
        chain: string;
        referenceId: string;
        clientId: string;
        asset: string;
        amount: number;
    }) {
        try {
            const walletServiceUrl = process.env.WALLET_SERVICE_URL || 'http://localhost:8035';
            const depositEndpoint = `${walletServiceUrl}/payment/generate-deposit-address`;

            console.log('📡 Calling wallet service for deposit address:', depositEndpoint);

            const response = await axios.post(depositEndpoint, data, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${process.env.INTERNAL_API_KEY || 'internal-key'}`
                }
            });

            return response.data;
        } catch (error: any) {
            console.error('❌ Error generating deposit address:', error);
            throw error;
        }
    }

    protected async executeDirectPayout(data: {
        clientId: string;
        amount: string;
        currency: string;
        mobileNumber: string;
        provider: string;
        memo?: string;
        reference_id?: string;
        payment_method_id?: string;
    }) {
        try {
            const { clientId, amount, currency, mobileNumber, provider, memo = "Direct Payout", payment_method_id } = data;
            const walletServiceUrl = process.env.WALLET_SERVICE_URL
            const payoutEndpoint = `${walletServiceUrl}/admin/payment/send-transaction`;
            const payoutData = {
                clientId: clientId,
                reference_id: data.reference_id || 'LR',
                amount,
                asset: currency,
                token: 99999,
                trans_type: "MOBILE_MONEY",
                account_number: mobileNumber,
                payment_method_id: payment_method_id,
                userId: clientId,
                receive_currency: currency
            };

            console.log('📡 Calling wallet service direct payout:', payoutData, payoutEndpoint);
            const response = await axios.post(payoutEndpoint, payoutData, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${process.env.INTERNAL_API_KEY}`
                }
            });
            return response.data;
        } catch (error: any) {
            console.error('❌ Error in executeDirectPayout:', error);
            return this.makeResponse(500, 'Direct payout failed', { error: error.message });
        }
    }
}
