import * as StellarSdk from '@stellar/stellar-sdk';
import { Keypair, Networks, Asset, Operation, TransactionBuilder } from '@stellar/stellar-sdk';

import { Request, Response } from "express";
import Model from './model'
require('dotenv').config()
import BaseModel from './base.model';
import logger from '../utils/logger';

const SponserKey = process.env.SPONSOR_KEY || "";
const url: any = process.env.HORIZON_URL || "https://horizon.stellar.org";
console.groupCollapsed("StellarService", { url })
const server = new StellarSdk.Horizon.Server(url);
let network: any = Networks.PUBLIC || Networks.TESTNET;

let md: Model;

function getModelInstance(): Model {
    if (!md) {
        md = new Model();
    }
    return md;
}

if (url?.includes("testnet")) {
    network = Networks.TESTNET
}
interface Recipient {
    publicKey: string;
    amount: string;
    asset_code: string;
    asset_issuer: string;
    senderSecretKey: string;
    creditPrivateKey: string
}

const feeAccounts: any = process.env.feeAccounts

interface PathPaymentResult {
    status: number;
    message: string;
    data: any;
    success: boolean;
    amount: string;
    price: string;
    stellarHash: string;
}

export default class StellarService extends BaseModel {


    constructor() {
        super();

    }

    async delay(ms: number) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async getChannelAccount() {
        await this.callRawQuery("UPDATE `channel_accounts` SET `status` = 'free' WHERE `updated_at` < (NOW() - INTERVAL 1 MINUTE)");

        const maxWaitTime = 10000; // Maximum wait time of 10 seconds
        const checkInterval = 1000; // Check every 1 second
        const startTime = Date.now();

        let channelAccount: any = [];
        while (Date.now() - startTime < maxWaitTime) {
            channelAccount = await this.callRawQuery("SELECT * FROM channel_accounts WHERE status='free' LIMIT 1");
            if (channelAccount.length > 0) {
                const keyPairPostion = Math.floor(Math.random() * channelAccount.length);
                const pvKey = channelAccount[keyPairPostion]['private_key'];
                //    this.updateChannelAccount(pvKey, "inUse");
                return pvKey;
            }
            await this.delay(checkInterval);
        }
        return SponserKey
    }



    async updateChannelAccount(private_key: any, status: string) {
        try {
            const PostData = {
                status
            };
            return await this.updateData('channel_accounts', `private_key='${private_key}'`, PostData);
        } catch (e) {
            return "";
        }
    }

    async getUserAccounts(clientId: string) {
        return await getModelInstance().getDecryptedApiKey(clientId)
    }

    async getPaymentOperations(user_id: string) {
        const keys: any = await this.getUserAccounts(user_id);
        if (keys == false) {
            return [];
        }
        const publicKey = keys.public_key;

        try {
            // Fetch payment operations using the public key
            const operations = await server.operations()
                .forAccount(publicKey)
                .order('desc')  // Orders results descending by operation ID
                .limit(20)      // Limits the number of operations retrieved
                .call();

            // Create an array to hold the operation data
            const paymentOperationsArray: any[] = [];

            // Loop through all operations and filter for 'payment' operations only
            for (const operation of operations.records) {
                if (operation.type === 'payment') {
                    // Fetch transaction to get memo
                    const transaction = await server.transactions().transaction(operation.transaction_hash).call();

                    // Determine if the operation is a debit (DR) or credit (CR)
                    const direction = operation.source_account === publicKey ? 'DR' : 'CR';

                    const paymentObj = {
                        id: operation.id,
                        type: operation.type,
                        created_at: operation.created_at, // Date of the operation
                        transaction_hash: operation.transaction_hash,
                        source_account: operation.source_account,
                        memo: transaction.memo || 'None', // Retrieve memo
                        amount: operation.amount,
                        asset_code: operation.asset_type === 'native' ? 'XLM' : operation.asset_code, // Set asset code to 'XLM' for native
                        direction, // DR for debit, CR for credit
                    };
                    paymentOperationsArray.push(paymentObj);
                }
            }

            return paymentOperationsArray;
        } catch (error) {
            console.error('Error fetching payment operations:', error);
            return [];
        }
    }



    async getAssetHolders(currency: string) {
        const assetCode = currency.toUpperCase();
        const assetCodeC = "c" + assetCode;
        const issuer = process.env.STELLAR_PAYOUT_ISSUER_PUBLIC;

        if (!issuer) {
            throw new Error("STELLAR_PAYOUT_ISSUER_PUBLIC is not defined in environment variables");
        }

        console.log(`Fetching holders for ${assetCode} and ${assetCodeC} from issuer ${issuer}`);

        const holders: Record<string, { accountId: string; cBalance: string; balance: string }> = {};
        const assetsToQuery = [
            new Asset(assetCode, issuer),
            new Asset(assetCodeC, issuer),
        ];

        for (const asset of assetsToQuery) {
            let page = await server.accounts().forAsset(asset).limit(200).call();

            while (true) {
                for (const account of page.records) {
                    const balances = account.balances;
                    const cTrustline = balances.find(
                        (b: any) =>
                            b.asset_type !== "native" &&
                            b.asset_code === assetCodeC &&
                            b.asset_issuer === issuer
                    );
                    const trustline = balances.find(
                        (b: any) =>
                            b.asset_type !== "native" &&
                            b.asset_code === assetCode &&
                            b.asset_issuer === issuer
                    );

                    holders[account.account_id] = {
                        accountId: account.account_id,
                        cBalance: cTrustline ? cTrustline.balance : holders[account.account_id]?.cBalance || "0",
                        balance: trustline ? trustline.balance : holders[account.account_id]?.balance || "0"
                    };
                }

                if (page.records.length < 200 || !page.next) break;
                page = await page.next();
            }
        }

        const result = Object.values(holders);
        console.log(`Found ${result.length} unique holders`);
        return result;
    }


    

    async getBalance(user_id: string, expectedCurrencies: string[] = ['UGX', 'KES']) {
        const keys: any = await this.getUserAccounts(user_id);
        if (!keys) {
            return [];
        }

        const public_key = keys.public_key;
        console.log("Fetching balances for:", public_key, user_id);

        let stellarBalances: any[] = [];
        try {
            // Attempt to load the Stellar account
            const recPbKey = await server.loadAccount(public_key);
            stellarBalances = recPbKey.balances;
        } catch (error: any) {
            console.error("Account not active or not found:", error.message);
            // Optionally, check for specific error codes (e.g., 404) to handle only account-not-found cases.
        }

        const bArray: any = [];

        // Process balances returned from Stellar (if any)
        if (stellarBalances && stellarBalances.length > 0) {
            stellarBalances.forEach((element: any) => {
                const selling_liabilities = parseFloat(element.selling_liabilities || "0")
                const locked = selling_liabilities.toString()
                let code = element.asset_type === "native" ? "XLM" : element.asset_code;
                // Ensure available balance is never negative
                const availableCalc = parseFloat(element.balance) - selling_liabilities
                const available = Math.max(0, availableCalc).toString()

                bArray.push({
                    code,
                    available: available,  // Amount available for trading (total - locked)
                    total: element.balance, // Total amount in account
                    locked: selling_liabilities.toString() // Amount locked in orders
                });
            });
        }

        // Ensure all expected currencies are included; if missing, add them with a zero balance
        expectedCurrencies.forEach(currency => {
            if (!bArray.find((bal: any) => bal.code === currency)) {
                bArray.push({ code: currency, available: "0", total: "0", locked: "0" });
            }
        });

        return bArray;
    }


    async getTotalCirculatingSupply(assetCode: string, issuer: string) {
        try {
            console.log(`SUPPLY_INFO`, { assetCode, issuer })
            if (!issuer) {
                throw new Error('STELLAR_PAYOUT_ISSUER_PUBLIC not configured');
            }
            // Fetch all accounts that hold this asset
            const accountsHoldingAsset = await server.assets()
                .forCode(assetCode)
                .forIssuer(issuer)
                .call();

            if (!accountsHoldingAsset.records || accountsHoldingAsset.records.length === 0) {
                return 0; // No accounts hold this asset
            }

            // Access the asset's total supply directly
            const totalSupply = accountsHoldingAsset.records[0].balances.authorized || 0;
            console.log(`Total circulating supply for ${assetCode}: ${totalSupply}`);
            return totalSupply
        } catch (error) {
            console.error("Error fetching total circulating supply:", error);
            return 0;
        }
    }



    async getSingleBalance(user_id: string, currencyCode: string) {
        const keys: any = await this.getUserAccounts(user_id);
        if (!keys) {
            return null; // Return null or a default value if the user has no accounts
        }
        const public_key = keys.public_key;
        const recPbKey = await server.loadAccount(public_key);
        const balances = recPbKey.balances;
        console.log("balances", public_key, balances);

        // Find the balance for the specified currency
        const balance = balances.find((element: any) => {
            if (currencyCode === "XLM" && element.asset_type === "native") {
                return true;
            } else if (element.asset_code === currencyCode) {
                return true;
            }
            return false;
        });

        // Return the balance object if found, or null if not
        return balance ? { code: currencyCode, balance: parseFloat(balance.balance).toFixed(2) } : null;
    }


    async getUserWallet(user_id: string) {
        return await this.callQuerySafe(`SELECT * FROM stellar_wallets WHERE user_id = ? LIMIT 1`, [user_id]);
    }




    async findPathpayment(sourceAsset: string, destinationAsset: string, amount: string, slippage: string) {
        try {
            // Parse issuer:code format
            const parseAsset = (assetString: string) => {
                if (assetString.includes(':')) {
                    const [issuer, code] = assetString.split(':');
                    return new Asset(code, issuer);
                } else {
                    // Assume it's XLM (native)
                    return Asset.native();
                }
            };

            const sourceAssetObj = parseAsset(sourceAsset);
            const destinationAssetObj = parseAsset(destinationAsset);

            console.log('Finding path for:', {
                source: sourceAssetObj.toString(),
                destination: destinationAssetObj.toString(),
                amount
            });

            // Use the correct Stellar SDK method for path finding
            let paths;
            try {
                // New SDK API: strictReceivePaths(sourceAssets, destAsset, destAmount)
                paths = await server.strictReceivePaths([sourceAssetObj], destinationAssetObj, amount)
                    .limit(5)
                    .call();
            } catch (error) {
                console.log('strictReceivePaths failed, trying strictSendPaths:', error);
                try {
                    // New SDK API: strictSendPaths(sourceAsset, sourceAmount, destAssets)
                    paths = await server.strictSendPaths(sourceAssetObj, amount, [destinationAssetObj])
                        .limit(5)
                        .call();
                } catch (fallbackError) {
                    console.log('strictSendPaths also failed:', fallbackError);
                    // Return a mock response for testing
                    return {
                        success: true,
                        path: [],
                        sourceAmount: amount,
                        destinationAmount: amount,
                        sourceAsset: sourceAssetObj.toString(),
                        destinationAsset: destinationAssetObj.toString(),
                        sourceAssetCode: sourceAssetObj.getCode(),
                        destinationAssetCode: destinationAssetObj.getCode(),
                        sourceAssetIssuer: sourceAssetObj.getIssuer(),
                        destinationAssetIssuer: destinationAssetObj.getIssuer()
                    };
                }
            }

            console.log('Stellar paths response:', JSON.stringify(paths, null, 2));

            if (!paths || !paths.records || paths.records.length === 0) {
                return {
                    success: false,
                    message: 'No path found for the given assets and amount'
                };
            }

            // Return the best path (first one)
            const bestPath = paths.records[0];
            return {
                success: true,
                path: bestPath.path,
                sourceAmount: bestPath.source_amount,
                destinationAmount: bestPath.destination_amount,
                sourceAsset: bestPath.source_asset_type,
                destinationAsset: bestPath.destination_asset_type,
                sourceAssetCode: bestPath.source_asset_code,
                destinationAssetCode: bestPath.destination_asset_code,
                sourceAssetIssuer: bestPath.source_asset_issuer,
                destinationAssetIssuer: bestPath.destination_asset_issuer
            };
        } catch (error: any) {
            console.error('Error finding path payment:', error);
            return {
                success: false,
                message: error.message || 'Failed to find path payment'
            };
        }
    }

    MakeResponse(code: number, message: string, data?: any): any {
        const response: any = {
            response: code,
            message: message,
        };
        if (data !== undefined) {
            response.data = data;
        }
        return response;
    }


    async makeSinglePayment(trans_id: string, senderSecretKey: string, receiverPublicKey: string, destinationPvKey: string, asset_code: string, asset_issuer: string, amount: string, memo: string, signersArray: any) {

        // console.log("PaymentStep1===>", senderSecretKey, receiverPublicKey, destinationPvKey, asset_code, asset_issuer, amount, memo, signersArray) // SECURITY: Removed private key logging
        const SponseringAccount = StellarSdk.Keypair.fromSecret(SponserKey);



        /*
        const feeArray = JSON.parse(feeAccounts);
        const keyPairPostion = Math.floor(Math.random() * feeArray.length);
        const payerKey = feeArray[keyPairPostion];
*/

        const payerKey = await this.getChannelAccount();

        const sourceAccountKeyPair = StellarSdk.Keypair.fromSecret(payerKey);
        let destPvKey: any = '';
        amount = amount.toString();

        const issuerPv = await getModelInstance().GetIssuerAccount(asset_code, "private")
        const issuingKeys = StellarSdk.Keypair.fromSecret(issuerPv || "");
        let includeSigner = false;


        try {
            if (destinationPvKey != '') {
                destPvKey = StellarSdk.Keypair.fromSecret(destinationPvKey || "");
            }
        } catch (err) {
            this.updateChannelAccount(payerKey, "free");

            console.log("PaymentStep2", err)
            return "error"
        }

        let signers: any = [];

        let asset: any = null;
        let assetType = "native";
        console.log(`ISSUERS`, asset_code)

        if (asset_code !== undefined) {
            const issuer = asset_issuer;
            asset = new StellarSdk.Asset(asset_code, issuer);
            assetType = "alpha";
        }


        let exists = true;
        let hasTrustLine = true;
        this.saveTransactionLog(trans_id, "PENDING", { asset_code, asset_issuer, amount, memo })

        try {

            if (assetType === "alpha") {
                const assetCode = asset.code;
                const assetIssuer = asset.issuer;
                if (assetIssuer == receiverPublicKey) {
                    hasTrustLine = true;

                } else {
                    const recPbKey = await server.loadAccount(receiverPublicKey);
                    hasTrustLine = recPbKey.balances.some((balance: any) => {
                        return balance.asset_type !== 'native' && 
                               balance.asset_code === assetCode && 
                               balance.asset_issuer === assetIssuer;
                    });
                }

            }
        } catch (err) {
            console.log("PaymentStep3", err)
            exists = false;
            hasTrustLine = false;
        }

        try {

            const senderKeyPair: any = StellarSdk.Keypair.fromSecret(senderSecretKey);

            if (Array.isArray(signersArray)) {
                const signerInfo = signersArray
                signerInfo.forEach((account) => {
                    const sAccount: any = StellarSdk.Keypair.fromSecret(account);
                    signers.push(sAccount);
                });
            } else {
                signers.push(senderKeyPair);
            }

            const fees = 1000000; // Set the desired fee value

            const [
                {
                    max_fee: { mode: fee },
                },
                distributionAccount,
            ] = await Promise.all([
                server.feeStats(),
                server.loadAccount(sourceAccountKeyPair.publicKey()),
            ]);

            const transaction_builder = new StellarSdk.TransactionBuilder(distributionAccount, {
                fee: String(fees),
                networkPassphrase: network,
            });

            if (!exists || !hasTrustLine) {

                transaction_builder.addOperation(StellarSdk.Operation.beginSponsoringFutureReserves({
                    sponsoredId: receiverPublicKey,
                    source: SponseringAccount.publicKey()
                }));
                console.log("PaymentStep5", exists)
                if (!exists) {


                    transaction_builder.addOperation(StellarSdk.Operation.createAccount({
                        startingBalance: "0",
                        destination: receiverPublicKey,
                        source: SponseringAccount.publicKey()
                    }));

                }
                console.log("hasTrustLine", hasTrustLine)

                if (!hasTrustLine) {
                    transaction_builder.addOperation(StellarSdk.Operation.changeTrust({
                        asset: asset,
                        source: receiverPublicKey,
                    }));
                    includeSigner = true;
                    transaction_builder.addOperation(StellarSdk.Operation.allowTrust({
                        trustor: receiverPublicKey,
                        assetCode: asset_code,
                        authorize: true,
                        source: issuingKeys.publicKey()
                    }));

                }

                transaction_builder.addOperation(StellarSdk.Operation.endSponsoringFutureReserves({
                    source: receiverPublicKey,
                }));
            }
            transaction_builder.addOperation(StellarSdk.Operation.payment({
                destination: receiverPublicKey,
                asset: asset,
                amount: amount,
                source: senderKeyPair.publicKey(),
            }));

            transaction_builder.setTimeout(20);
            transaction_builder.addMemo(StellarSdk.Memo.text(memo));

            const transaction = transaction_builder.build();

            signers.push(sourceAccountKeyPair);

            if (!exists || !hasTrustLine) {
                console.log("")
                signers.push(destPvKey);
                signers.push(SponseringAccount);

            }

            console.log("==============START============")


            signers.forEach((signer: any) => {
                console.log("Signing with public key:", signer.publicKey());
                transaction.sign(signer);
            });

            const issuerPv = await getModelInstance().GetIssuerAccount(asset_code, "private")
            if (includeSigner && senderSecretKey != issuerPv) {
                transaction.sign(issuingKeys);
            }
            console.log("==============END============")


            try {
                const transactionResult = await server.submitTransaction(transaction);
                console.log("tx Sent", transactionResult)
                this.updateTransactionLog(trans_id, "SUCCESS", transactionResult);
                this.updateChannelAccount(payerKey, "free");

                if (transactionResult.successful) {

                    return this.MakeResponse(1, transactionResult.hash)
                } else {
                    return this.MakeResponse(101, "not processed")
                }
            } catch (e: any) {
                this.updateChannelAccount(payerKey, "free");

                if (e.code === 'ECONNABORTED' || e.message.includes('timeout')) {
                    return this.MakeResponse(504, "Gateway Timeout or Network Issue");
                }

                console.log("Error during transaction submission");

                let errorDetail = e.response?.data?.extras?.result_codes;
                let errorMessage = "Transaction could not be sent";
                console.log("errorMessage", errorDetail)

                if (errorDetail) {
                    const operationError = errorDetail.operations;
                    const transactionError = errorDetail.transaction;

                    if (operationError === "tx_insufficient_balance") {
                        errorMessage = "Insufficient XLM balance to process transaction";
                    } else if (operationError === "payment_underfunded") {
                        errorMessage = "Insufficient Balance on your account";
                    } else {
                        errorMessage += `: ${transactionError || operationError || 'Unknown'}`;
                    }
                }
                this.updateTransactionLog(trans_id, "FAILED", errorMessage);

                if (errorMessage == "Unknown") {
                    return this.MakeResponse(504, errorMessage);
                } else {
                    console.log(errorMessage);
                    return this.MakeResponse(203, errorMessage);
                }
            }


        } catch (err) {
            this.updateChannelAccount(payerKey, "free");
            console.log("txError", err)
            this.updateTransactionLog(trans_id, "FAILED", err);
            return this.MakeResponse(203, "transaction not set properly");
        }
    }

    async saveTransactionLog(trans_id: string, status: string, recipients: any) {
        try {
            for (const recipient of recipients) {
                const txObject = {
                    trans_id,
                    status,
                    data: JSON.stringify(recipient)
                }
                await this.insertData("chain_transactions_log", txObject);
            }
        } catch (error: any) {
            console.error("Error in saveStableCoinTransaction:", error);
            return false;
        }
    }

    async updateTransactionLog(trans_id: string, status: string, response: any) {

        try {
            const txObject = {
                trans_id,
                status,
                data: JSON.stringify(response)
            }
            await this.updateData("chain_transactions_log", `trans_id='${trans_id}'`, txObject);
        } catch (error: any) {
            console.error("Error in updateTransactionLog:", error);
            return false;
        }
    }

    async checkTransactionLog(trans_id: string) {
        const log = await this.selectDataQuerySafe("chain_transactions_log", { trans_id, status: "SUCCESS" });
        return log.length > 0;
    }

    public async makePathPayment(data: {
        senderSecretKey: string;
        baseAsset: string;
        counterAsset: string;
        amount: string;
        price: string; // Expected price from frontend
        orderType: 'buy' | 'sell';
        slippage?: number;
    }): Promise<any> {
        try {
            logger.info('🔄 Executing sponsored path payment:', data);

            const { senderSecretKey, baseAsset, counterAsset, amount, price, orderType, slippage = 0.5 } = data;

            console.log('🔑 Sender Secret Key:', senderSecretKey);
            
            const sourceKeypair = Keypair.fromSecret(senderSecretKey);
            const publicKey = sourceKeypair.publicKey();
            console.log('🔑 Sender Public Key:', publicKey);

            // Create Stellar assets
            const sendAsset = this.createStellarAsset(orderType === 'buy' ? counterAsset : baseAsset);
            const destAsset = this.createStellarAsset(orderType === 'buy' ? baseAsset : counterAsset);

            // Get channel account for sponsored transaction
            const channelAccount = await this.getChannelAccount();
            console.log('🔑 Channel Account Secret:', channelAccount);
            
            const sourceAccountKeyPair = StellarSdk.Keypair.fromSecret(channelAccount);
            console.log('🔑 Channel Account Public:', sourceAccountKeyPair.publicKey());

            const [
                {
                    max_fee: { mode: fee },
                },
                distributionAccount,
            ] = await Promise.all([
                server.feeStats(),
                server.loadAccount(sourceAccountKeyPair.publicKey()),
            ]);

            let pathPaymentOp: any;
            let expectedSendAmount: string;
            let expectedReceiveAmount: string;

            let pathResult;

            if (orderType === 'buy') {
                // BUY: Want to receive EXACT amount of base asset
                // Calculate max amount willing to send based on frontend price + slippage
                const destAmount = amount; // Exact amount to receive
                const estimatedSendAmount = parseFloat(amount) * parseFloat(price);
                const sendMaxAmount = (estimatedSendAmount * (1 + slippage / 100)).toFixed(7);

                // Find best payment path to get the actual path (not for price calculation)
                try {
                    pathResult = await this.findPaymentPath(
                        counterAsset, // From counter asset
                        baseAsset,    // To base asset
                        destAmount,
                        'receive'
                    );
                } catch (pathError: any) {
                    logger.error('❌ No payment path found for buy order:', pathError.message);
                    return {
                        status: 400,
                        message: `No liquidity available to buy ${baseAsset} with ${counterAsset}. Try a smaller amount or use a limit order.`,
                        data: null,
                        success: false,
                        amount: '0',
                        price: '0',
                        stellarHash: ''
                    };
                }

                logger.info('💰 Buy order limits:', {
                    wantToReceive: destAmount,
                    frontendPrice: price,
                    estimatedSend: estimatedSendAmount.toFixed(7),
                    maxWillingToSend: sendMaxAmount,
                    slippagePercent: slippage
                });

                expectedSendAmount = sendMaxAmount;
                expectedReceiveAmount = destAmount;

                pathPaymentOp = StellarSdk.Operation.pathPaymentStrictReceive({
                    sendAsset: sendAsset,
                    sendMax: sendMaxAmount,
                    destination: publicKey,
                    destAsset: destAsset,
                    destAmount: destAmount,
                    path: pathResult.path,
                    source: publicKey
                });

            } else {
                // SELL: Want to send EXACT amount of base asset
                // Calculate min amount willing to receive based on frontend price - slippage
                const sendAmount = amount; // Exact amount to send
                const estimatedReceiveAmount = parseFloat(amount) * parseFloat(price);
                const destMinAmount = (estimatedReceiveAmount * (1 - slippage / 100)).toFixed(7);

                // Find best payment path to get the actual path (not for price calculation)
                try {
                    pathResult = await this.findPaymentPath(
                        baseAsset,     // From base asset
                        counterAsset,  // To counter asset
                        sendAmount,
                        'send'
                    );
                } catch (pathError: any) {
                    logger.error('❌ No payment path found for sell order:', pathError.message);
                    return {
                        status: 400,
                        message: `No liquidity available to sell ${baseAsset} for ${counterAsset}. Try a smaller amount or use a limit order.`,
                        data: null,
                        success: false,
                        amount: '0',
                        price: '0',
                        stellarHash: ''
                    };
                }

                logger.info('💰 Sell order limits:', {
                    wantToSend: sendAmount,
                    frontendPrice: price,
                    estimatedReceive: estimatedReceiveAmount.toFixed(7),
                    minWillingToReceive: destMinAmount,
                    slippagePercent: slippage
                });

                expectedSendAmount = sendAmount;
                expectedReceiveAmount = destMinAmount;

                pathPaymentOp = StellarSdk.Operation.pathPaymentStrictSend({
                    sendAsset: sendAsset,
                    sendAmount: sendAmount,
                    destination: publicKey,
                    destAsset: destAsset,
                    destMin: destMinAmount,
                    path: pathResult.path,
                    source: publicKey
                });
            }

            // Build and submit transaction
            const transaction = new StellarSdk.TransactionBuilder(distributionAccount, {
                fee: '1000000',
                networkPassphrase: network,
            })
                .addOperation(pathPaymentOp)
                .setTimeout(30)
                .addMemo(StellarSdk.Memo.text(`MARKET_${orderType}`))
                .build();

            // Sign with channel and user keypairs
            transaction.sign(sourceAccountKeyPair);
            transaction.sign(sourceKeypair);

            try {
                const result = await server.submitTransaction(transaction);
                console.log("result", result)
                this.updateChannelAccount(channelAccount, "free");

                logger.info('✅ Path payment executed:', {
                    hash: result.hash,
                    sentAmount: expectedSendAmount,
                    receivedAmount: expectedReceiveAmount
                });

                return {
                    status: 200,
                    message: 'Path payment executed successfully',
                    success: true,
                    amount: expectedReceiveAmount,
                    stellarHash: result.hash
                };
            } catch (e: any) {
                this.updateChannelAccount(channelAccount, "free");

                logger.error('❌ Path payment failed:', {
                    message: e.message,
                    response: e.response?.data,
                    extras: e.response?.data?.extras
                });

                let errorMessage = "Transaction could not be sent";
                let errorDetail = e.response?.data?.extras?.result_codes;

                if (e.message && e.message.includes('XDR Read Error')) {
                    console.log('⚠️ XDR parsing error but checking if transaction succeeded...');

                    // Try to get the transaction hash from the error response
                    let transactionHash = null;
                    if (e.response?.data?.hash) {
                        transactionHash = e.response.data.hash;
                    } else if (e.response?.data?.extras?.result_xdr) {
                        // Try to extract hash from result_xdr
                        try {
                            const resultXdr = e.response.data.extras.result_xdr;
                            // If we have result_xdr, the transaction likely succeeded
                            transactionHash = 'XDR_SUCCESS'; // Placeholder to indicate success
                        } catch (xdrError) {
                            console.log('Could not parse result_xdr');
                        }
                    }

                    if (transactionHash) {
                        console.log('✅ Transaction succeeded despite XDR error:', transactionHash);
                         return {
                            status: 200,
                            message: 'Path payment executed successfully',
                            success: true,
                            amount: expectedReceiveAmount,
                            stellarHash: ""
                        };
                    }
                }
                // Parse specific error codes and provide user-friendly messages
                if (errorDetail && errorDetail.operations) {
                    const operations = errorDetail.operations;

                    if (operations.includes('op_cross_self')) {
                        errorMessage = "Cannot execute trade: You have conflicting orders that would trade against each other. Please cancel one of your existing orders first.";
                    } else if (operations.includes('op_underfunded')) {
                        errorMessage = "Insufficient balance: You don't have enough funds to complete this trade.";
                    } else if (operations.includes('op_no_trust')) {
                        errorMessage = "Trustline required: You need to add a trustline for this asset before trading.";
                    } else if (operations.includes('op_line_full')) {
                        errorMessage = "Trustline limit reached: You've reached the maximum amount for this asset.";
                    } else if (operations.includes('op_not_authorized')) {
                        errorMessage = "Not authorized: You don't have permission to trade this asset.";
                    } else if (operations.includes('op_sell_no_trust')) {
                        errorMessage = "No trustline for selling asset: You need to add a trustline for the asset you're trying to sell.";
                    } else if (operations.includes('op_buy_no_trust')) {
                        errorMessage = "No trustline for buying asset: You need to add a trustline for the asset you're trying to buy.";
                    } else if (operations.includes('op_over_source_max')) {
                        errorMessage = "Price slippage too high: The trade would cost more than your maximum. Try increasing slippage tolerance.";
                    } else if (operations.includes('op_under_dest_min')) {
                        errorMessage = "Price slippage too high: The trade would receive less than your minimum. Try increasing slippage tolerance.";
                    } else {
                        // Generic error with operation codes
                        errorMessage = `Transaction failed: ${operations.join(', ')}`;
                    }
                } else if (errorDetail) {
                    errorMessage += `: ${JSON.stringify(errorDetail)}`;
                }

                logger.error('❌ Path payment failed:', errorMessage);
                return {
                    status: 400,
                    message: errorMessage,
                    data: null,
                    success: false,
                    amount: '0',
                    price: '0',
                    stellarHash: ''
                };
            }

        } catch (error: any) {
            logger.error('❌ Error executing sponsored path payment:', error);
            return {
                status: 400,
                message: error.message,
                data: null,
                success: false,
                amount: '0',
                price: '0',
                stellarHash: ''
            };
        }
    }

    public async makePathPaymentOld(data: {
        senderSecretKey: string;
        baseAsset: string;
        counterAsset: string;
        amount: string;
        orderType: 'buy' | 'sell';
        slippage?: number;
    }): Promise<PathPaymentResult> {
        try {
            logger.info('🔄 Executing path payment (non-sponsored):', data);

            const { senderSecretKey, baseAsset, counterAsset, amount, orderType, slippage = 0 } = data;

            const sourceKeypair = Keypair.fromSecret(senderSecretKey);
            const sourceAccount = await server.loadAccount(sourceKeypair.publicKey());

            // Create Stellar assets
            const sourceAsset = this.createStellarAsset(orderType === 'buy' ? counterAsset : baseAsset);
            const destinationAsset = this.createStellarAsset(orderType === 'buy' ? baseAsset : counterAsset);

            // Calculate amounts based on order type
            let sendAmount: string;
            let destMinAmount: string;

            if (orderType === 'buy') {
                // Buying baseAsset with counterAsset
                sendAmount = (parseFloat(amount) * parseFloat(await this.getCurrentPrice(baseAsset, counterAsset))).toString();
                destMinAmount = amount;
            } else {
                // Selling baseAsset for counterAsset
                sendAmount = amount;
                const expectedReceive = parseFloat(amount) * parseFloat(await this.getCurrentPrice(baseAsset, counterAsset));
                destMinAmount = (expectedReceive * (1 - slippage / 100)).toString(); // Apply slippage
            }

            // Create path payment operation
            const pathPaymentOp = Operation.pathPaymentStrictReceive({
                sendAsset: sourceAsset,
                sendMax: sendAmount,
                destination: sourceKeypair.publicKey(), // Pay to self
                destAsset: destinationAsset,
                destAmount: destMinAmount,
                path: [], // Let Stellar find the best path
            });

            // Build and submit transaction
            const transaction = new TransactionBuilder(sourceAccount, {
                fee: '10000',
                networkPassphrase: network,
            })
                .addOperation(pathPaymentOp)
                .setTimeout(30)
                .build();

            transaction.sign(sourceKeypair);

            const result = await server.submitTransaction(transaction);

                logger.info('✅ Path payment executed:', {
                    hash: result.hash,
                    sendAmount,
                    destMinAmount,
                    actualAmount: result.result_xdr
                });

            return {
                status: 200,
                message: 'Path payment executed successfully',
                data: {
                    amount: destMinAmount,
                    price: (parseFloat(sendAmount) / parseFloat(destMinAmount)).toString(),
                    stellarHash: result.hash
                },
                success: true,
                amount: destMinAmount,
                price: (parseFloat(sendAmount) / parseFloat(destMinAmount)).toString(),
                stellarHash: result.hash
            };

        } catch (error: any) {
            logger.error('❌ Error executing path payment:', error);
            return {
                status: 400,
                message: error.message,
                data: null,
                success: false,
                amount: '0',
                price: '0',
                stellarHash: ''
            };
        }
    }

    public async createLimitOffer(data: {
        senderSecretKey: string;
        baseAsset: string;
        counterAsset: string;
        amount: string;
        price: string;
        orderType: 'buy' | 'sell';
    }) {
        try {
            const { senderSecretKey, baseAsset, counterAsset, amount, price, orderType } = data;
            const sourceKeypair = Keypair.fromSecret(senderSecretKey);
            const publicKey = sourceKeypair.publicKey();

            console.log('🔄 Creating sponsored limit offer:', data);

            const baseAssetStellar = this.createStellarAsset(baseAsset);
            const counterAssetStellar = this.createStellarAsset(counterAsset);

            const sellingAsset = orderType === 'buy' ? counterAssetStellar : baseAssetStellar;
            const buyingAsset = orderType === 'buy' ? baseAssetStellar : counterAssetStellar;

            // Validate sponsor key
            if (!SponserKey) {
                throw new Error('SPONSOR_KEY environment variable is not set');
            }

            const SponsorKeypair = StellarSdk.Keypair.fromSecret(SponserKey);
            const channelSecret = await this.getChannelAccount();
            const channelKeypair = StellarSdk.Keypair.fromSecret(channelSecret);

            // Validate all public keys
            try {
                StellarSdk.StrKey.decodeEd25519PublicKey(SponsorKeypair.publicKey());
                StellarSdk.StrKey.decodeEd25519PublicKey(channelKeypair.publicKey());
                StellarSdk.StrKey.decodeEd25519PublicKey(publicKey);
            } catch (keyError: any) {
                console.error('❌ Invalid public key detected:', keyError.message);
                throw new Error('Invalid public key format detected');
            }

            console.log('🔑 Keypairs loaded:', {
                sponsorPublic: SponsorKeypair.publicKey(),
                channelPublic: channelKeypair.publicKey(),
                userPublic: publicKey
            });

            // Check sponsor account balance and permissions
            try {
                const sponsorAccount = await server.loadAccount(SponsorKeypair.publicKey());
                console.log('📊 Sponsor account loaded:', {
                    publicKey: SponsorKeypair.publicKey(),
                    sequence: sponsorAccount.sequenceNumber(),
                    balances: sponsorAccount.balances.length
                });
            } catch (sponsorError: any) {
                console.error('❌ Sponsor account error:', sponsorError.message);
                throw new Error(`Sponsor account not accessible: ${sponsorError.message}`);
            }

            const userAccount = await server.loadAccount(publicKey);
            console.log('📊 User account loaded:', { publicKey, sequence: userAccount.sequenceNumber() });

            // Check if trustlines exist for both assets
            const hasBaseTrustline = this.hasTrustline(userAccount, baseAssetStellar);
            const hasCounterTrustline = this.hasTrustline(userAccount, counterAssetStellar);

            console.log('🔍 Trustline check:', {
                baseAsset: baseAsset,
                counterAsset: counterAsset,
                hasBaseTrustline,
                hasCounterTrustline
            });

            // Pre-validate: Check for potential op_cross_self
            try {
                const orderbook = await server.orderbook(baseAssetStellar, counterAssetStellar).call();
                const priceNum = parseFloat(price);

                // Check if user has opposing orders that would match
                if (orderType === 'buy') {
                    // For buy orders, check if user has sell orders at or below this price
                    const userSellOrders = orderbook.asks?.filter((ask: any) => {
                        const askPrice = parseFloat(ask.price);
                        return ask.source === publicKey && askPrice <= priceNum;
                    }) || [];

                    if (userSellOrders.length > 0) {
                        console.warn('⚠️ Pre-validation: User has sell orders that would cross with this buy order');
                        return this.MakeResponse(400, `You have an existing SELL order at ${userSellOrders[0].price} that would match this BUY order. Please cancel it first or adjust your price below ${userSellOrders[0].price}.`);
                    }
                } else {
                    // For sell orders, check if user has buy orders at or above this price
                    const userBuyOrders = orderbook.bids?.filter((bid: any) => {
                        const bidPrice = parseFloat(bid.price);
                        return bid.source === publicKey && bidPrice >= priceNum;
                    }) || [];

                    if (userBuyOrders.length > 0) {
                        console.warn('⚠️ Pre-validation: User has buy orders that would cross with this sell order');
                        return this.MakeResponse(400, `You have an existing BUY order at ${userBuyOrders[0].price} that would match this SELL order. Please cancel it first or adjust your price above ${userBuyOrders[0].price}.`);
                    }
                }

                console.log('✅ Pre-validation: No cross-self detected');
            } catch (validationError: any) {
                console.warn('⚠️ Could not pre-validate for cross-self:', validationError.message);
                // Continue anyway - let Stellar handle the validation
            }

            const [feeStats, channelAccount] = await Promise.all([
                server.feeStats(),
                server.loadAccount(channelKeypair.publicKey()),
            ]);

            const builder = new StellarSdk.TransactionBuilder(channelAccount, {
                fee: '1000000',
                networkPassphrase: network,
            });

            // Begin sponsorship
            builder.addOperation(StellarSdk.Operation.beginSponsoringFutureReserves({
                sponsoredId: publicKey,
                source: SponsorKeypair.publicKey(),
            }));

            // Add trustlines if they don't exist
            const maxTrustlineLimit = '************.4775807'; // Stellar max limit

            if (!hasBaseTrustline) {
                console.log('🔗 Adding trustline for base asset:', baseAsset);
                builder.addOperation(StellarSdk.Operation.changeTrust({
                    asset: baseAssetStellar,
                    limit: maxTrustlineLimit,
                    source: publicKey
                }));
            }

            if (!hasCounterTrustline) {
                console.log('🔗 Adding trustline for counter asset:', counterAsset);
                builder.addOperation(StellarSdk.Operation.changeTrust({
                    asset: counterAssetStellar,
                    limit: maxTrustlineLimit,
                    source: publicKey
                }));
            }

            // Add offer operation
            if (orderType === 'buy') {
                console.log('🔄 Adding buy offer operation:', {
                    selling: sellingAsset,
                    buying: buyingAsset,
                    buyAmount: amount,
                    price,
                    offerId: '0',
                    source: publicKey,
                });
                builder.addOperation(StellarSdk.Operation.manageBuyOffer({
                    selling: sellingAsset,
                    buying: buyingAsset,
                    buyAmount: amount,
                    price,
                    offerId: '0',
                    source: publicKey,
                }));
            } else {
                console.log('🔄 Adding sell offer operation:', {
                    selling: sellingAsset,
                    buying: buyingAsset,
                    amount,
                    price,
                    offerId: '0',
                    source: publicKey,
                });
                builder.addOperation(StellarSdk.Operation.manageSellOffer({
                    selling: sellingAsset,
                    buying: buyingAsset,
                    amount,
                    price,
                    offerId: '0',
                    source: publicKey,
                }));
            }

            // End sponsorship
            builder.addOperation(StellarSdk.Operation.endSponsoringFutureReserves({ source: publicKey }));

            builder.setTimeout(20);
            const memo = StellarSdk.Memo.text(`OFFER_${orderType}`);
            builder.addMemo(memo);

            let transaction;
            try {
                transaction = builder.build();
                console.log('🏗️ Transaction built with operations:', transaction.operations.length);
            } catch (buildError: any) {
                console.error('❌ Error building transaction:', buildError.message);
                throw new Error(`Failed to build transaction: ${buildError.message}`);
            }

            console.log('🔑 Transaction signers:', {
                sponsor: SponsorKeypair.publicKey(),
                channel: channelKeypair.publicKey(),
                user: sourceKeypair.publicKey()
            });

            // Sign with sponsor first, then channel, then user
            try {
                transaction.sign(SponsorKeypair);
                console.log('✅ Sponsor signed');
                transaction.sign(channelKeypair);
                console.log('✅ Channel signed');
                transaction.sign(sourceKeypair);
                console.log('✅ User signed');
            } catch (signError: any) {
                console.error('❌ Error signing transaction:', signError.message);
                throw new Error(`Failed to sign transaction: ${signError.message}`);
            }

            console.log('📤 Submitting transaction...');
            let result;
            try {
                result = await server.submitTransaction(transaction);
                console.log('✅ Offer created:', result.hash);
            } catch (submitError: any) {
                // Check if the error is just an XDR parsing issue but transaction succeeded
                if (submitError.message && submitError.message.includes('XDR Read Error')) {
                    console.log('⚠️ XDR parsing error but checking if transaction succeeded...');

                    // Try to get the transaction hash from the error response
                    let transactionHash = null;
                    if (submitError.response?.data?.hash) {
                        transactionHash = submitError.response.data.hash;
                    } else if (submitError.response?.data?.extras?.result_xdr) {
                        // Try to extract hash from result_xdr
                        try {
                            const resultXdr = submitError.response.data.extras.result_xdr;
                            // If we have result_xdr, the transaction likely succeeded
                            transactionHash = 'XDR_SUCCESS'; // Placeholder to indicate success
                        } catch (xdrError) {
                            console.log('Could not parse result_xdr');
                        }
                    }

                    if (transactionHash) {
                        console.log('✅ Transaction succeeded despite XDR error:', transactionHash);
                        this.updateChannelAccount(channelSecret, "free");
                        return {
                            response: 1,
                            message: transactionHash === 'XDR_SUCCESS' ? 'Request sent successfully' : transactionHash,
                            data: null
                        };
                    }
                }

                // If it's not an XDR error or we can't confirm success, re-throw
                throw submitError;
            }

            let offerId = null;

            this.updateChannelAccount(channelSecret, "free");
            return {
                response: 1,
                message: result.hash,
                data: offerId // The actual offer ID
            };

        } catch (e: any) {
            console.error('❌ Error creating limit offer:', {
                message: e.message,
                response: e.response?.data,
                extras: e.response?.data?.extras
            });

            // Parse and return user-friendly error messages
            if (e.response?.data?.extras?.result_codes) {
                const { transaction, operations } = e.response.data.extras.result_codes;
                console.error('🔍 Detailed error codes:', {
                    transaction,
                    operations: operations || [],
                    fullResultCodes: e.response.data.extras.result_codes
                });

                // Check for specific error codes
                if (operations && Array.isArray(operations)) {
                    // Check for op_cross_self
                    if (operations.includes('op_cross_self')) {
                        return this.MakeResponse(400, 'Order would match your own existing order. Please cancel your opposing order first or adjust your price.');
                    }

                    // Check for op_malformed (like self-sponsorship)
                    if (operations.includes('op_malformed')) {
                        return this.MakeResponse(400, 'Invalid order configuration. Please contact support.');
                    }

                    // Check for op_underfunded
                    if (operations.includes('op_underfunded')) {
                        return this.MakeResponse(400, 'Insufficient balance to create this order.');
                    }

                    // Check for op_line_full
                    if (operations.includes('op_line_full')) {
                        return this.MakeResponse(400, 'Asset trustline limit exceeded.');
                    }
                }

                // Check transaction-level errors
                if (transaction === 'tx_insufficient_balance') {
                    return this.MakeResponse(400, 'Insufficient XLM balance to cover transaction fees.');
                }

                if (transaction === 'tx_failed') {
                    return this.MakeResponse(400, 'Transaction failed. Please check your order details and try again.');
                }
            }

            // Check if it's an XDR error that might indicate success
            if (e.message && e.message.includes('XDR Read Error')) {
                return {
                    response: 1,
                    message: "hash",
                    data: "" // The actual offer ID
                };
            }

            return this.MakeResponse(203, `Failed to create limit offer: ${e.message}`);
        }
    }


    public async cancelOffer(data: {
        senderSecretKey: string;
        offerId: string;
        orderType: 'buy' | 'sell';
        baseAsset: string;
        counterAsset: string;
    }) {
        try {
            const { senderSecretKey, offerId, orderType, baseAsset, counterAsset } = data;

            const userKeypair = Keypair.fromSecret(senderSecretKey);
            const userPublicKey = userKeypair.publicKey();

            console.log('🚫 Cancelling offer with sponsorship:', {
                offerId,
                orderType,
                baseAsset,
                counterAsset,
                userPublicKey
            });

            // Create the same assets as the original offer
            const baseAssetStellar = this.createStellarAsset(baseAsset);
            const counterAssetStellar = this.createStellarAsset(counterAsset);

            // Use the same asset logic as createLimitOffer
            const sellingAsset = orderType === 'buy' ? counterAssetStellar : baseAssetStellar;
            const buyingAsset = orderType === 'buy' ? baseAssetStellar : counterAssetStellar;

            // 🔹 Get channel & sponsor accounts
            const channelSecret = await this.getChannelAccount();
            const channelKeypair = StellarSdk.Keypair.fromSecret(channelSecret);

            // 🔹 Load accounts
            const [feeStats, channelAccount] = await Promise.all([
                server.feeStats(),
                server.loadAccount(channelKeypair.publicKey()),
            ]);

            const builder = new StellarSdk.TransactionBuilder(channelAccount, {
                fee: '1000000',
                networkPassphrase: network,
            });


            // 🔹 Cancel offer (amount = 0) with correct assets
            if (orderType === 'buy') {
                builder.addOperation(StellarSdk.Operation.manageBuyOffer({
                    selling: sellingAsset,
                    buying: buyingAsset,
                    buyAmount: '0',
                    price: '1',
                    offerId,
                    source: userPublicKey,
                }));
            } else {
                builder.addOperation(StellarSdk.Operation.manageSellOffer({
                    selling: sellingAsset,
                    buying: buyingAsset,
                    amount: '0',
                    price: '1',
                    offerId,
                    source: userPublicKey,
                }));
            }



            builder.setTimeout(20);
            builder.addMemo(StellarSdk.Memo.text(`CANCEL_${orderType}`));

            const transaction = builder.build();

            // Sign with sponsor, channel, and user
            transaction.sign(channelKeypair);
            transaction.sign(userKeypair);

            console.log('📤 Submitting sponsored cancel transaction...');
            const result = await server.submitTransaction(transaction);

            this.updateChannelAccount(channelSecret, "free");

            if (result.successful) {
                console.log('✅ Offer cancelled successfully:', result.hash);
                return this.MakeResponse(1, result.hash);
            } else {
                console.error('⚠️ Transaction not processed');
                return this.MakeResponse(101, "not processed");
            }

        } catch (e: any) {
            const errDetail = e.response?.data?.extras?.result_codes;
            let errorMessage = "Transaction could not be sent";

            if (errDetail) {
                const opError = errDetail.operations;
                const txError = errDetail.transaction;

                if (opError === "op_not_found") {
                    errorMessage = "Offer not found or already cancelled";
                } else if (opError === "op_underfunded") {
                    errorMessage = "Insufficient balance in sponsor or channel account";
                } else if (txError === "tx_failed") {
                    errorMessage = `Transaction failed: ${opError || 'unknown error'}`;
                } else {
                    errorMessage += `: ${txError || opError || 'unknown'}`;
                }
            }

            console.error('❌ Error cancelling offer:', e.response?.data || e.message);
            return this.MakeResponse(203, errorMessage);
        }
    }


    async makeBatchTransfers(trans_id: string, sentmemo: string, recipients: Recipient[]) {
        const channelAccount = await this.getChannelAccount();
        try {
            const memo = sentmemo.substring(0, 25) || "trade"
            const existsInLog = await this.checkTransactionLog(trans_id);
            if (existsInLog) {
                this.saveTransactionLog(trans_id, "EXISTS_IN_LOG", recipients)
                return this.MakeResponse(1, "Transaction already exists in log");
            }
            this.saveTransactionLog(trans_id, "PENDING", recipients)

            console.log("RECEIVED==>", recipients)
            // console.log("RECEIVED==>", SponserKey) // SECURITY: Removed private key logging
            const SponsoringAccount = StellarSdk.Keypair.fromSecret(SponserKey);
            // console.log(`SponserKey===>1`, SponserKey) // SECURITY: Removed private key logging
            // console.log(`SponserKey===>2`, SponserKey) // SECURITY: Removed private key logging
            // console.log(`SponserKey===>3`, SponserKey) // SECURITY: Removed private key logging

            /*
            const feeArray = JSON.parse(feeAccounts);
            const keyPairPosition = Math.floor(Math.random() * feeArray.length);
            const SponserKey = feeArray[keyPairPosition];
*/
            const sourceAccountKeyPair = StellarSdk.Keypair.fromSecret(channelAccount);



            let signers: Array<any> = [];
            let includeSigner = false;




            const fees = 1000000;

            const [
                {
                    max_fee: { mode: fee },
                },
                distributionAccount,
            ] = await Promise.all([
                server.feeStats(),
                server.loadAccount(sourceAccountKeyPair.publicKey()),
            ]);

            const transaction_builder = new StellarSdk.TransactionBuilder(distributionAccount, {
                fee: String(fees),
                networkPassphrase: network,
            });
            // this.saveLog(memo, "", "", "");
            let p = 0;
            console.log("STELLAR===>1")

            for (let recipient of recipients) {
                console.log(`OPERATION========>${p}`, recipient)
                let receiverPublicKey = recipient.publicKey;
                p++;



                const asset_code = recipient.asset_code;
                const asset_issuer = recipient.asset_issuer;
                const creditPrivateKey = recipient.creditPrivateKey;
                const senderSecretKey = recipient.senderSecretKey;
                const issuerPv = await getModelInstance().GetIssuerAccount(asset_code, "private")
                console.log("issuerPv===>", issuerPv)

                const issuingKeys = StellarSdk.Keypair.fromSecret(issuerPv);

                const assetType = 'alpha';
                let amount = Number(recipient.amount).toFixed(7);
                amount = amount.toString();


                const senderKeyPair = StellarSdk.Keypair.fromSecret(senderSecretKey);


                signers = this.addSignerIfNotExists(signers, senderKeyPair);

                //signers.push(senderKeyPair);

                const asset = new StellarSdk.Asset(asset_code, asset_issuer);

                let exists = true;
                let hasTrustLine = true;

                try {
                    // await server.loadAccount(receiverPublicKey);
                    // if (assetType === "alpha") {
                    const assetCode = asset_code;
                    const assetIssuer = asset_issuer;

                    if (assetIssuer == receiverPublicKey) {
                        hasTrustLine = true;
                    } else {

                        const recPbKey = await server.loadAccount(receiverPublicKey);
                        hasTrustLine = recPbKey.balances.some((balance: any) => {
                            return balance.asset_code === assetCode && balance.asset_issuer === assetIssuer;
                        });
                    }
                    //}
                } catch (err) {
                    console.log("STELLAR===>3", err)
                    exists = false;
                    hasTrustLine = false;
                }
                console.log("exists===>", exists)
                console.log("hasTrustLine===>", hasTrustLine)

                if (!exists || !hasTrustLine) {
                    if (creditPrivateKey && creditPrivateKey !== '') {
                        const creditPvKeyPair = StellarSdk.Keypair.fromSecret(creditPrivateKey);
                        signers = this.addSignerIfNotExists(signers, creditPvKeyPair);
                    }
                    signers = this.addSignerIfNotExists(signers, SponsoringAccount);

                    //1. create account, 2. change trust,3. allow trust, 4.payment

                    transaction_builder.addOperation(StellarSdk.Operation.beginSponsoringFutureReserves({
                        sponsoredId: receiverPublicKey,
                        source: SponsoringAccount.publicKey()
                    }));

                    if (!exists) {
                        transaction_builder.addOperation(StellarSdk.Operation.createAccount({
                            startingBalance: "0",
                            destination: receiverPublicKey,
                            source: SponsoringAccount.publicKey()
                        }));
                    }

                    if (!hasTrustLine) {
                        transaction_builder.addOperation(StellarSdk.Operation.changeTrust({
                            asset: asset,
                            source: receiverPublicKey,
                        }));
                        includeSigner = true;
                        transaction_builder.addOperation(StellarSdk.Operation.allowTrust({
                            trustor: receiverPublicKey,
                            assetCode: asset_code,
                            authorize: true,
                            source: issuingKeys.publicKey()
                        }));
                    }

                    transaction_builder.addOperation(StellarSdk.Operation.endSponsoringFutureReserves({
                        source: receiverPublicKey,
                    }));
                }

                transaction_builder.addOperation(StellarSdk.Operation.payment({
                    destination: receiverPublicKey,
                    asset: asset,
                    amount: amount,
                    source: senderKeyPair.publicKey(),
                }));

                if (includeSigner) {
                    signers = this.addSignerIfNotExists(signers, issuingKeys);
                }
            }

            transaction_builder.setTimeout(20);
            transaction_builder.addMemo(StellarSdk.Memo.text(memo));
            const transaction = transaction_builder.build();

            //signers.push(sourceAccountKeyPair);
            signers = this.addSignerIfNotExists(signers, sourceAccountKeyPair);
            signers.forEach((signer: any) => {
                console.log("Signing with public key:", signer.publicKey());
                transaction.sign(signer);
            });

            try {
                const transactionResult = await server.submitTransaction(transaction);
                console.log("StellarResponse===>", transactionResult)
                this.updateChannelAccount(SponserKey, "free");

                if (transactionResult.successful) {
                    this.updateTransactionLog(trans_id, "SUCCESS", { hash: transactionResult.hash });
                    return this.MakeResponse(1, transactionResult.hash)
                } else {
                    this.updateTransactionLog(trans_id, "FAILED", { error: transactionResult.result_xdr });
                    return this.MakeResponse(101, "not processed")
                }
            } catch (e: any) {
                this.updateChannelAccount(SponserKey, "free");

                if (e.code === 'ECONNABORTED' || e.message.includes('timeout')) {
                    this.updateTransactionLog(trans_id, "TIMEOUT", { error: e.message });
                    return this.MakeResponse(504, "Gateway Timeout or Network Issue");
                }

                console.log("Error during transaction submission");

                let errorDetail = e.response?.data?.extras?.result_codes;
                let errorMessage = "stellar transaction error";
                console.log("errorMessage", errorDetail)
                if (errorDetail) {
                    const operationError = errorDetail.operations;
                    const transactionError = errorDetail.transaction;

                    if (operationError === "tx_insufficient_balance") {
                        errorMessage = "Insufficient XLM balance to process transaction";
                    } else if (operationError === "payment_underfunded") {
                        errorMessage = "Insufficient Balance on your account";
                    } else {
                        errorMessage += `: ${transactionError || operationError || 'Unknown'}`;
                    }
                }

                this.updateTransactionLog(trans_id, "FAILED", errorDetail);

                if (errorMessage == "Unknown") {
                    return this.MakeResponse(203, errorMessage);
                } else {
                    console.log(errorMessage);
                    return this.MakeResponse(203, errorMessage);
                }
            }


        } catch (err) {
            this.updateChannelAccount(SponserKey, "free");

            console.log("txError", err)
            this.updateTransactionLog(trans_id, "FAILED", err);
            return this.MakeResponse(203, "transaction not set properly");
        }
    }

    addSignerIfNotExists(signers: Array<any>, keyPair: Keypair) {
        if (!signers.some(signer => signer.publicKey() === keyPair.publicKey())) {
            signers.push(keyPair);
        }
        return signers;
    }

    createRecipient(
        publicKey: string,
        amount: string,
        asset_code?: string,
        asset_issuer?: string,
        destinationPvKey?: string
    ) {
        return {
            publicKey,
            amount,
            asset_code,
            asset_issuer,
            destinationPvKey,
        };
    }




    async changeTrustOperation(req: { body: { source: any; signers: any; Operation: any; asset_code: any; asset_issuer: any; }; }) {
        console.log("received RQ ", req);
        let limit: string = "************";
        const sourcePvKey = req.body.source;
        const sponserPvKey = SponserKey;

        const accounts = req.body.signers;
        const Operation = req.body.Operation;
        if (Operation === "REMOVE") {
            limit = "0";
        }

        const tokenToAdd = req.body.asset_code;
        const tokenToAddIssuer = req.body.asset_issuer;

        const SponseringAccount = StellarSdk.Keypair.fromSecret(sponserPvKey);
        const sourceAccount = StellarSdk.Keypair.fromSecret(sourcePvKey);

        const distributedCurrency = "100";

        const TokenToBeAdded = new StellarSdk.Asset(
            tokenToAdd,
            tokenToAddIssuer,
        );
        const fees = 1000000; // Set the desired fee value

        try {
            // Fetch the base fee and the account that will create our transaction
            const [
                {
                    max_fee: { mode: fee },
                },
                distributionAccount,
            ] = await Promise.all([
                server.feeStats(),
                server.loadAccount(SponseringAccount.publicKey()),
            ]);

            const changeTrustTx = new StellarSdk.TransactionBuilder(
                distributionAccount,
                {
                    fee: String(fees),
                    networkPassphrase: network,
                },
            )



            changeTrustTx.addOperation(
                StellarSdk.Operation.beginSponsoringFutureReserves({
                    sponsoredId: sourceAccount.publicKey(),
                }),
            )


            changeTrustTx.addOperation(
                StellarSdk.Operation.changeTrust({
                    asset: TokenToBeAdded,
                    source: sourceAccount.publicKey(),
                    limit: limit,
                }),
            )

            changeTrustTx.addOperation(
                StellarSdk.Operation.endSponsoringFutureReserves({
                    source: sourceAccount.publicKey(),
                }),
            )


            let transaction = changeTrustTx.setTimeout(100).build();


            const keyPairArray = JSON.parse(accounts);
            keyPairArray.forEach((keyPair: any) => {
                const keyPairObject = StellarSdk.Keypair.fromSecret(keyPair);
                transaction.sign(keyPairObject);
            });
            transaction.sign(SponseringAccount);


            const txResult = await server.submitTransaction(transaction);

            console.log(
                `Success! ${SponseringAccount.publicKey()
                } key pair ${distributedCurrency} and sponsored by ${SponseringAccount.publicKey()
                }`,
            );

            return {
                "status": "success",
                "data": txResult
            }

        } catch (e: any) {
            console.error("Oh no! Something went wrong." + e);
            return {
                "status": "error",
                "data": "failed"
            };
        }
    }

    generateKeyPair() {
        const NewAccountKeyPair = StellarSdk.Keypair.random();
        return {
            public_key: NewAccountKeyPair.publicKey(),
            private_key: NewAccountKeyPair.secret(),
        };
    }



    async initAccount() {
        const NewAccountKeyPair = StellarSdk.Keypair.random();
        return {
            public: NewAccountKeyPair.publicKey(),
            private: NewAccountKeyPair.secret(),
            status: "success",
            data: "",
        };
    }

    private createStellarAsset(assetCode: string): Asset {
        if (assetCode === 'XLM') {
            return Asset.native();
        }

        const issuer = process.env.STELLAR_ASSET_ISSUER || process.env.STELLAR_PAYOUT_ISSUER_PUBLIC;
        if (!issuer) {
            throw new Error('STELLAR_ASSET_ISSUER not configured');
        }

        // Validate issuer is a valid Stellar public key using SDK's validator
        try {
            StellarSdk.StrKey.decodeEd25519PublicKey(issuer);
        } catch (error) {
            console.error('❌ Invalid issuer public key:', { assetCode, issuer });
            throw new Error(`Invalid issuer public key for ${assetCode}: ${issuer}`);
        }

        console.log('✅ Creating asset:', { assetCode, issuer: issuer.substring(0, 10) + '...' });
        return new StellarSdk.Asset(assetCode, issuer);
    }

    private hasTrustline(account: any, asset: Asset): boolean {
        if (asset.isNative()) {
            return true; // Native XLM doesn't need trustline
        }

        return account.balances.some((balance: any) => {
            if (balance.asset_type === 'native') return false;
            return balance.asset_code === asset.getCode() && balance.asset_issuer === asset.getIssuer();
        });
    }

    private async getCurrentPrice(baseAsset: string, counterAsset: string): Promise<string> {
        try {
            const baseAssetStellar = this.createStellarAsset(baseAsset);
            const counterAssetStellar = this.createStellarAsset(counterAsset);

            const orderbook = await server.orderbook(baseAssetStellar, counterAssetStellar).call();

            if (orderbook.bids && orderbook.bids.length > 0) {
                return orderbook.bids[0].price;
            }

            return '1'; // Default price if no orderbook available
        } catch (error) {
            console.error('Error getting current price:', error);
            return '1';
        }
    }

    /**
     * Cancel all active orders for a client on Stellar SDEX
     * Fetches the client's secret key from database and cancels all offers
     */
    public async cancelAllOrdersByClientId(clientId: string) {
        try {
            logger.info('🚫 Cancelling all orders for client:', clientId);

            // Get client's credentials from database
            const apiInfo = await getModelInstance().getDecryptedApiKey(clientId);
            if (!apiInfo) {
                return this.MakeResponse(400, 'Client not found');
            }

            const secretKey = apiInfo.secret_key;
            const keypair = StellarSdk.Keypair.fromSecret(secretKey);
            const publicKey = keypair.publicKey();

            logger.info('📍 Fetching on-chain offers for account:', publicKey);

            // Fetch ALL active offers for this account from Stellar (handle pagination)
            let allOffers: any[] = [];
            let offersResponse = await server.offers()
                .forAccount(publicKey)
                .limit(200)
                .order('desc')
                .call();

            allOffers = allOffers.concat(offersResponse.records);

            // Keep fetching until we have all offers
            while (offersResponse.records.length === 200) {
                logger.info(`📄 Fetching next page of offers... (current total: ${allOffers.length})`);
                offersResponse = await offersResponse.next();
                allOffers = allOffers.concat(offersResponse.records);
            }

            const offers = allOffers;

            if (!offers || offers.length === 0) {
                logger.info('No active offers found');
                return this.MakeResponse(200, 'No active orders to cancel', { cancelledCount: 0 });
            }

            logger.info(`📊 Found ${offers.length} active offers to cancel (after pagination)`);

            // Get sponsor key from environment
            const sponsorKey = SponserKey;
            if (!sponsorKey) {
                throw new Error('SPONSOR_KEY not configured');
            }

            const sponsorKeypair = StellarSdk.Keypair.fromSecret(sponsorKey);

            // Stellar allows max 100 operations per transaction, so batch if needed
            const BATCH_SIZE = 100;
            const batches: any[][] = [];

            for (let i = 0; i < offers.length; i += BATCH_SIZE) {
                batches.push(offers.slice(i, i + BATCH_SIZE));
            }

            logger.info(`📦 Cancelling ${offers.length} offers in ${batches.length} batch(es)`);

            const allTransactionHashes: string[] = [];
            let totalCancelled = 0;

            // Process each batch
            for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                const batch = batches[batchIndex];
                logger.info(`🔄 Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} offers)...`);

                // Load the sponsor account fresh for each batch (sequence number updates)
                const sponsorAccount = await server.loadAccount(sponsorKeypair.publicKey());

                // Create cancel operations for this batch
                const cancelOperations = batch.map((offer: any) => {
                    const sellingAsset = offer.selling.asset_type === 'native'
                        ? StellarSdk.Asset.native()
                        : new StellarSdk.Asset(offer.selling.asset_code, offer.selling.asset_issuer);

                    const buyingAsset = offer.buying.asset_type === 'native'
                        ? StellarSdk.Asset.native()
                        : new StellarSdk.Asset(offer.buying.asset_code, offer.buying.asset_issuer);

                    return StellarSdk.Operation.manageSellOffer({
                        selling: sellingAsset,
                        buying: buyingAsset,
                        amount: '0', // Setting amount to 0 cancels the offer
                        price: offer.price,
                        offerId: offer.id.toString(),
                        source: publicKey // User is the source of this operation
                    });
                });

                // Build transaction for this batch
                const transaction = new StellarSdk.TransactionBuilder(sponsorAccount, {
                    fee: (100000 * batch.length).toString(), // Fee per operation
                    networkPassphrase: network,
                });

                // Add all cancel operations for this batch
                cancelOperations.forEach((op: any) => transaction.addOperation(op));

                const builtTransaction = transaction.setTimeout(30).build();

                // Sign with both sponsor (fee payer) and user (operation source)
                builtTransaction.sign(sponsorKeypair);
                builtTransaction.sign(keypair);

                // Submit the transaction
                logger.info(`📤 Submitting batch ${batchIndex + 1}/${batches.length}...`);
                const result = await server.submitTransaction(builtTransaction);

                logger.info(`✅ Batch ${batchIndex + 1} successful. Hash: ${result.hash}`);
                allTransactionHashes.push(result.hash);
                totalCancelled += batch.length;
            }

            logger.info(`✅ Successfully cancelled all ${totalCancelled} offers in ${batches.length} transaction(s)`);

            return this.MakeResponse(200, `Successfully cancelled ${totalCancelled} orders in ${batches.length} transaction(s)`, {
                total: offers.length,
                cancelledCount: totalCancelled,
                batches: batches.length,
                stellarHashes: allTransactionHashes,
                offers: offers.map((o: any) => ({
                    offerId: o.id,
                    selling: o.selling.asset_code || 'XLM',
                    buying: o.buying.asset_code || 'XLM',
                    amount: o.amount,
                    price: o.price
                }))
            });

        } catch (error: any) {
            logger.error('❌ Error cancelling orders:', error);
            return this.MakeResponse(203, `Failed to cancel orders: ${error.message}`);
        }
    }

    /**
     * Find best payment path for a trade
     * @param fromAsset - Asset to send (e.g., 'USDT', 'XLM')
     * @param toAsset - Asset to receive (e.g., 'UGX', 'KES')
     * @param amount - Amount to send or receive
     * @param type - 'send' for strict send (selling), 'receive' for strict receive (buying)
     * @returns Object containing the path array and source/destination amounts
     */
    private async findPaymentPath(
        fromAsset: string,
        toAsset: string,
        amount: string,
        type: 'send' | 'receive'
    ): Promise<{
        path: Asset[];
        sourceAmount: string;
        destinationAmount: string;
    }> {
        try {
            const sendAsset = this.createStellarAsset(fromAsset);
            const destAsset = this.createStellarAsset(toAsset);

            logger.info('🔍 Finding payment path:', { fromAsset, toAsset, amount, type });

            let paths;
            if (type === 'receive') {
                // For buying: find path to receive exact amount
                // strictReceivePaths(source: Asset[], destAsset: Asset, destAmount: string)
                paths = await server.strictReceivePaths(
                    [sendAsset], // Source as array
                    destAsset,
                    amount
                )
                    .limit(5)
                    .call();
            } else {
                // For selling: find path to send exact amount
                // strictSendPaths(sourceAsset: Asset, sourceAmount: string, destination: Asset[])
                paths = await server.strictSendPaths(
                    sendAsset,
                    amount,
                    [destAsset] // Destination as array
                )
                    .limit(5)
                    .call();
            }

            if (!paths.records || paths.records.length === 0) {
                throw new Error(`No payment path found from ${fromAsset} to ${toAsset}`);
            }

            const bestPath = paths.records[0];

            // Convert path assets to Asset objects
            const pathAssets = bestPath.path.map((p: any) =>
                p.asset_type === 'native' ? StellarSdk.Asset.native() : new StellarSdk.Asset(p.asset_code, p.asset_issuer)
            );

            logger.info('✅ Payment path found:', {
                sourceAmount: bestPath.source_amount,
                destinationAmount: bestPath.destination_amount,
                pathDepth: pathAssets.length,
                path: bestPath.path.map((p: any) => p.asset_code || 'XLM')
            });

            return {
                path: pathAssets,
                sourceAmount: bestPath.source_amount,
                destinationAmount: bestPath.destination_amount
            };

        } catch (error: any) {
            logger.error('❌ Error finding payment path:', error);
            throw new Error(`Failed to find payment path: ${error.message}`);
        }
    }
}


