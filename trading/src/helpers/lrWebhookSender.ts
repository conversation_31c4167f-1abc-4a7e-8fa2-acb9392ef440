/**
 * LR Webhook Sender
 * Sends webhook events from trading service to liquidityRailAdmin
 */

import axios from 'axios';

export interface ChainInfo {
    amount: string;
    chain: string;
    asset_code: string;
    hash: string;
    from_address: string;
    to_address: string;
    contract_address?: string;
    fee?: string;
}
interface LRProviderEvent {
    eventType: 'fiat_sent' | 'fiat_received' | 'crypto_received' | 'crypto_sent';
    transaction_id: string;
    reference_id: string;
    status: 'SUCCESSFUL' | 'PENDING' | 'FAILED' | 'CANCELLED' | 'EXPIRED' | 'ONHOLD';
    data: FiatEvent | CryptoEvent;
}

interface FiatEvent {
    currency: string;
    amount: number;
    amount_delivered: number;
    fee: number;
    external_reference_id: string;
    payment_type: string;
    payment_method_id: string;
}

interface CryptoEvent {
    amount: string;
    chain: string;
    asset_code: string;
    hash: string;
    from_address: string;
    to_address: string;
    contract_address: string;
    fee: string;
}

class LRWebhookSender {
    private liquidityRailAdminUrl: string;
    private clientId: string;

    constructor() {
        this.liquidityRailAdminUrl = process.env.LR_BASE_URL || 'http://localhost:8032';
        this.clientId = process.env.TRADER_ACCOUNT || 'trading-service';
    }

    /**
     * Send crypto received event
     */
    async sendCryptoReceived(transaction_id: string, reference_id: string, chainInfo: ChainInfo){
        try {
            const event: LRProviderEvent = {
                eventType: 'crypto_received',
                transaction_id: transaction_id,
                reference_id: reference_id,
                status: 'SUCCESSFUL',
                data: {
                    amount: chainInfo.amount,
                    chain: chainInfo.chain,
                    asset_code: chainInfo.asset_code,
                    hash: chainInfo.hash || '',
                    from_address: chainInfo.from_address,
                    to_address: chainInfo.to_address,
                    contract_address: chainInfo.contract_address || '',
                    fee: chainInfo.fee || '0'
                }
            };
            console.log('Crypto received webhook event:', event);

            const response = await axios.post(
                `${this.liquidityRailAdminUrl}/accounts/events`,
                {
                    clientId: this.clientId,
                    ...event
                },
                {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 10000
                }
            );

            console.log('Crypto received webhook sent:', response.data);
            return { success: true, message: 'Webhook sent successfully' };
        } catch (error: any) {
            console.error('Error sending crypto received webhook:', error.message);
            return { success: false, message: error.message };
        }
    }

    /**
     * Send fiat sent event
     */
    async sendFiatSent(params: {
        transaction_id: string;
        reference_id: string;
        amount: number;
        currency: string;
        amount_delivered: number;
        fee: number;
        external_reference_id: string;
        payment_type: string;
        payment_method_id: string;
        status?: 'SUCCESSFUL' | 'PENDING' | 'FAILED' | 'CANCELLED' | 'EXPIRED' | 'ONHOLD';
    }): Promise<{ success: boolean; message: string }> {
        try {
            const event: LRProviderEvent = {
                eventType: 'fiat_sent',
                transaction_id: params.transaction_id,
                reference_id: params.reference_id,
                status: params.status || 'SUCCESSFUL',
                data: {
                    currency: params.currency,
                    amount: params.amount,
                    amount_delivered: params.amount_delivered,
                    fee: params.fee,
                    external_reference_id: params.external_reference_id,
                    payment_type: params.payment_type,
                    payment_method_id: params.payment_method_id
                }
            };

            const response = await axios.post(
                `${this.liquidityRailAdminUrl}/accounts/events`,
                {
                    clientId: this.clientId,
                    ...event
                },
                {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 10000
                }
            );

            console.log('Fiat sent webhook sent:', response.data);
            return { success: true, message: 'Webhook sent successfully' };
        } catch (error: any) {
            console.error('Error sending fiat sent webhook:', error.message);
            return { success: false, message: error.message };
        }
    }

    /**
     * Send crypto sent event
     */
    async sendCryptoSent(params: {
        transaction_id: string;
        reference_id: string;
        amount: string;
        asset_code: string;
        chain: string;
        from_address: string;
        to_address: string;
        contract_address?: string;
        hash?: string;
        fee?: string;
        status?: 'SUCCESSFUL' | 'PENDING' | 'FAILED' | 'CANCELLED' | 'EXPIRED' | 'ONHOLD';
    }): Promise<{ success: boolean; message: string }> {
        try {
            const event: LRProviderEvent = {
                eventType: 'crypto_sent',
                transaction_id: params.transaction_id,
                reference_id: params.reference_id,
                status: params.status || 'SUCCESSFUL',
                data: {
                    amount: params.amount,
                    chain: params.chain,
                    asset_code: params.asset_code,
                    hash: params.hash || '',
                    from_address: params.from_address,
                    to_address: params.to_address,
                    contract_address: params.contract_address || '',
                    fee: params.fee || '0'
                }
            };

            const response = await axios.post(
                `${this.liquidityRailAdminUrl}/accounts/events`,
                {
                    clientId: this.clientId,
                    ...event
                },
                {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 10000
                }
            );

            console.log('Crypto sent webhook sent:', response.data);
            return { success: true, message: 'Webhook sent successfully' };
        } catch (error: any) {
            console.error('Error sending crypto sent webhook:', error.message);
            return { success: false, message: error.message };
        }
    }

    /**
     * Send fiat received event
     */
    async sendFiatReceived(params: {
        transaction_id: string;
        reference_id: string;
        amount: number;
        currency: string;
        amount_delivered: number;
        fee: number;
        external_reference_id: string;
        payment_type: string;
        payment_method_id: string;
        status?: 'SUCCESSFUL' | 'PENDING' | 'FAILED' | 'CANCELLED' | 'EXPIRED' | 'ONHOLD';
    }): Promise<{ success: boolean; message: string }> {
        try {
            const event: LRProviderEvent = {
                eventType: 'fiat_received',
                transaction_id: params.transaction_id,
                reference_id: params.reference_id,
                status: params.status || 'SUCCESSFUL',
                data: {
                    currency: params.currency,
                    amount: params.amount,
                    amount_delivered: params.amount_delivered,
                    fee: params.fee,
                    external_reference_id: params.external_reference_id,
                    payment_type: params.payment_type,
                    payment_method_id: params.payment_method_id
                }
            };

            const response = await axios.post(
                `${this.liquidityRailAdminUrl}/accounts/events`,
                {
                    clientId: this.clientId,
                    ...event
                },
                {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 10000
                }
            );

            console.log('Fiat received webhook sent:', response.data);
            return { success: true, message: 'Webhook sent successfully' };
        } catch (error: any) {
            console.error('Error sending fiat received webhook:', error.message);
            return { success: false, message: error.message };
        }
    }
}

export default new LRWebhookSender();

