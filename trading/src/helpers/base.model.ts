import * as db from './db.helper';

class BaseModel {
  public tableName: string;
  public insertion: string | undefined;
  public selectCols: string | undefined;
  public selectWhere: string = '';
  public offsets: number = 0;
  public limits: number = 10;
  public orderBy: string = '';
  public orderIs: string = '';
  public updation: string | undefined;
  public fileId: any;
  public updateWhere: string = '';
  public insertPrimaryKey: string | undefined;
  
  constructor(value: string = '') {
    this.tableName = value;
  }

  public async callRawQuery(query: string, params: any[] = [], connType: string = 'normal') {
    try {
      console.log("RAWQUERY===>", query, "with params:", params);
      const result = await db.default.pdo(query, params, connType);
      return Array.isArray(result) ? result : [result];
    } catch (error) {
      console.log(error);
      return [];
    }
  }

  public async selectDataQuerySafe(tableName: string, conditions: { [key: string]: any } = {}, limit: number = 100) {
    try {
      const whereConditions: string[] = [];
      const params: any[] = [];
      
      Object.entries(conditions).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          whereConditions.push(`${key} = ?`);
          params.push(value);
        }
      });
      
      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : "";
      const query = `SELECT * FROM ${tableName} ${whereClause} LIMIT ?`;
      params.push(limit);
      
      console.log("Safe query:", query, "params:", params);
      const result = await this.callRawQuery(query, params);
      this.resetSelectSettings();
      return Array.isArray(result) ? result : [result];
    } catch (error) {
      console.log("ERROR", error)
      return []
    }
  }

  public async callQuerySafe(query: string, params: any[] = [], connType: string = 'normal') {
    try {
      console.log("Safe callQuery:", query, "with params:", params)
      const result = await db.default.pdo(query, params, connType);
      this.resetSelectSettings();
      return result;
    } catch (error) {
      console.log("ERROR", error)
      return []
    }
  }

  private resetSelectSettings() {
    this.selectWhere = '';
    this.orderBy = '';
    this.orderIs = '';
    this.selectCols = '';
    this.offsets = 0;
  }

  public async updateData(table: string, where: string, data: any) {
    return new Promise(async (resolve, reject) => {
      this.tableName = table;
      this.updateWhere = where;

      const keys = Object.keys(data);
      const values = Object.values(data);

      const updates = keys.map((key, index) => `${key} = ?`).join(', ');
      const query = `UPDATE ${table} SET ${updates} WHERE ${where}`;
      console.log("UPDATE1===>", query, values);

      try {
        const result = await db.default.pdo(query, values);
        console.log("UPDATE2==>", result);
        resolve(true);
      } catch (error) {
        console.log('Error updating data:', error);
        reject(error);
      }
    });
  }

  public async insertData(table: string, data: any) {
    this.tableName = table;
    const keys = Object.keys(data);
    const values: any = Object.values(data);
    const placeholders = keys.map(() => '?').join(',');
    const query = `INSERT INTO ${table} (${keys.join(',')}) VALUES (${placeholders})`;
    console.log("insertData", query)
    try {
      const result: any = await db.default.pdo(query, values);
      const lastId = result.insertId;
      return lastId
    } catch (error: any) {
      console.log('DBINSERTERROR=======>', error);
      throw new Error("Error inserting data ")
    }
  }

  public async deleteData(table: string, where: string = '') {
    const query = `DELETE FROM ${table} where ${where}`;
    const result = db.default.pdo(query);
    this.fileId = '';
    return result;
  }
}

export default BaseModel;
