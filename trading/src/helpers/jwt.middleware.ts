import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

interface JWTPayload {
    clientId: string;
    userId: string;
    email: string;
    role: string;
    apiKey?: string;
    user?: string;
}

export class JWTMiddleware {
    /**
     * Verify JWT token for API access
     */
    static verifyTokenAccess(req: Request, res: Response, next: NextFunction) {
        try {
            const authHeader = req.headers.authorization;
            
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                return res.status(401).json({
                    status: 401,
                    message: 'Authorization header missing or invalid',
                    data: null
                });
            }

            const token = authHeader.substring(7); // Remove 'Bearer ' prefix
            
            if (!token) {
                return res.status(401).json({
                    status: 401,
                    message: 'Token is required',
                    data: null
                });
            }

            const secret = process.env.JWT_SECRET;
            if (!secret) {
                console.error('❌ JWT_SECRET not configured');
                return res.status(500).json({
                    status: 500,
                    message: 'Server configuration error',
                    data: null
                });
            }

            const decoded = jwt.verify(token, secret) as JWTPayload;
            
            // Attach user info to request
            req.body.clientId = decoded.clientId;
            req.body.userId = decoded.userId;
            req.body.userEmail = decoded.email;
            req.body.userRole = decoded.role;
            
            // Check if user has trading permissions
            if (decoded.role === 'API' && decoded.apiKey) {
                // API key authentication
                req.body.apiKey = decoded.apiKey;
            }

            next();
        } catch (error: any) {
            console.error('❌ JWT verification failed:', error.message);
            
            if (error.name === 'TokenExpiredError') {
                return res.status(401).json({
                    status: 401,
                    message: 'Token has expired',
                    data: null
                });
            }
            
            if (error.name === 'JsonWebTokenError') {
                return res.status(401).json({
                    status: 401,
                    message: 'Invalid token',
                    data: null
                });
            }

            return res.status(401).json({
                status: 401,
                message: 'Token verification failed',
                data: null
            });
        }
    }

    /**
     * Verify API key for programmatic access
     */
    static verifyApiKey(req: Request, res: Response, next: NextFunction) {
        try {
            const apiKey = req.headers['x-api-key'] as string;
            const secretKey = req.headers['x-secret-key'] as string;
            
            if (!apiKey || !secretKey) {
                return res.status(401).json({
                    status: 401,
                    message: 'API key and secret key are required',
                    data: null
                });
            }

            // TODO: Implement API key validation against database
            // For now, we'll just pass through
            req.body.apiKey = apiKey;
            req.body.secretKey = secretKey;
            
            next();
        } catch (error: any) {
            console.error('❌ API key verification failed:', error.message);
            return res.status(401).json({
                status: 401,
                message: 'API key verification failed',
                data: null
            });
        }
    }

    /**
     * Optional middleware for rate limiting
     */
    static rateLimit(req: Request, res: Response, next: NextFunction) {
        // TODO: Implement rate limiting logic
        // For now, just pass through
        next();
    }
}
