import * as mysql from 'mysql2';
import * as dotenv from 'dotenv';
dotenv.config();

class DbHelper {
  private normalPool: any;
  private writePool: any;
  private railsPool: any;
  private readPool: any;
  constructor() {
    this.normalPool = this.initializePool('normal');
  }


  public initializePool(connectionType: string) {
    if (connectionType === 'normal') {
      
      return mysql.createPool({
        connectionLimit: 10,
        host: process.env.HOST_NAME,
        database: process.env.DBNAME,
        user: process.env.USER_NAME,
        password: process.env.PASSWORD,
        timezone: 'Z',
        waitForConnections: true,
        queueLimit: 0,
        enableKeepAlive: true,
        keepAliveInitialDelay: 0
      });
    }
    if (connectionType === 'write') {
      return mysql.createPool({
        connectionLimit: 10,
        host: process.env.WRITE_NAME,
        database: process.env.WRITE_DBNAME,
        user: process.env.WRITE_USER_NAME,
        password: process.env.WRITE_PASSWORD,
        timezone: 'Z',
        waitForConnections: true,
        queueLimit: 0,
        enableKeepAlive: true,
        keepAliveInitialDelay: 0
      });
    }

    if (connectionType === 'read') {
      return mysql.createPool({
        connectionLimit: 10,
        host: process.env.READ_HOST_NAME,
        database: process.env.READ_DBNAME,
        user: process.env.READ_USER_NAME,
        password: process.env.READ_PASSWORD,
        timezone: 'Z',
        waitForConnections: true,
        queueLimit: 0,
        enableKeepAlive: true,
        keepAliveInitialDelay: 0
      });
    }

  }

  private getConnectionPool(conType: string) {
    if (conType === 'read') {
      this.readOpreation();
      return this.readPool;
    } else if (conType === 'write') {
      this.writeOpreation();
      return this.writePool;
    }
    return this.normalPool;
  }

  private executeQuery(connection: any, query: string, values: any[] = [], isProcedureCall: boolean = false) {
    return new Promise((resolve, reject) => {
      connection.query(query, values, (error: any, results: any) => {
        connection.release();

        if (error) {
          return reject(error);
        }

        const data = results.length > 0 
          ? JSON.parse(JSON.stringify(isProcedureCall ? results[0] : results))
          : [];
        resolve(data);
      });
    });
  }

  public pdoOld(query: any, conType: string = 'normal') {
    const pdoConnect = this.getConnectionPool(conType);
    console.log("PDO CONNECT===>", pdoConnect);

    return new Promise((resolve, reject) => {
      pdoConnect.getConnection((err: any, connection: any) => {
        if (err) return reject(err);
        const isProcedureCall = query.trim().startsWith('CALL');
        this.executeQuery(connection, query, [], isProcedureCall)
          .then(resolve)
          .catch(reject);
      });
    });
  }

  public pdo(query: string, values: any[] = [], conType: string = 'normal') {

    const pdoConnect = this.getConnectionPool(conType);
    return new Promise((resolve, reject) => {
      pdoConnect.getConnection((err: any, connection: any) => {
        if (err) return reject(err);
        const isProcedureCall = query.trim().startsWith('CALL');
        this.executeQuery(connection, query, values, isProcedureCall)
          .then(resolve)
          .catch(reject);
      });
    });
  }
  
  private async executeTransactionCommand(command: string) {
    return this.pdo(command);
  }

  public beginTransaction() {
    return this.executeTransactionCommand('START TRANSACTION');
  }
  
  public commit() {
    return this.executeTransactionCommand('COMMIT');
  }
  
  public rollback() {
    return this.executeTransactionCommand('ROLLBACK');
  }

  

  public readOpreation() {
    this.readPool = this.initializePool('read');
  }
  public writeOpreation() {
    this.writePool = this.initializePool('read');
  }


}
export default new DbHelper();
