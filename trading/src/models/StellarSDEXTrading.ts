import * as StellarSdk from '@stellar/stellar-sdk';
import { Asset, Keypair, Operation, TransactionBuilder, Networks } from '@stellar/stellar-sdk';
import StellarService from '../helpers/StellarService';
import logger from '../utils/logger';
import Model from '../helpers/model';
import lrWebhookSender from '../helpers/lrWebhookSender';
import { ChainInfo as LRChainInfo } from '../helpers/lrWebhookSender';

function formatDateForMySQL(date: Date = new Date()): string {
    return date.toISOString().slice(0, 19).replace('T', ' ');
}

interface TradeOffer {
    clientId: string;
    baseAsset: string;
    counterAsset: string;
    amount: string;
    price?: string;
    orderType: 'buy' | 'sell';
    type: 'market' | 'limit';
    slippage?: number;
    referenceId?: string;
    payoutData?: any;
}

interface ApiResponse {
    status: number;
    message: string;
    data: any;
}

interface PathPaymentResult extends ApiResponse {
    success: boolean;
    amount: string;
    price: string;
    stellarHash: string;
}

interface PriceResult extends ApiResponse {
    success: boolean;
    price: string;
}

interface OrderBookResult extends ApiResponse {
    success: boolean;
    buyOrders: any[];
    sellOrders: any[];
    bestBid?: string;
    bestAsk?: string;
    spread?: string;
}
export type WebhookData = {
    type: string;
    statusCode: number;
    message: string;
    client_id: string;
    trans_type: string;
    timestamp: string;
    reference_id: string;
    status: string;
    amount: string;
    fee: string;
    currency: string;
    sender_account: string;
    receiver_account: string;
    transaction_id: string;
    meta: string;
    chainInfo?: ChainInfo;
}
type ChainInfo = {
    from_address: string;
    to_address: string;
    amount: string;
    asset_code: string;
    contract_address: string;
    hash: string;
    state: string;
}

class StellarSDEXTrading extends Model {
    private server: any;
    private networkPassphrase: string;

    constructor() {
        super();
        this.networkPassphrase = process.env.STELLAR_NETWORK_PASSPHRASE || Networks.TESTNET;
        this.server = new StellarSdk.Horizon.Server(process.env.STELLAR_HORIZON_URL || 'https://horizon-testnet.stellar.org');
    }

    /**
     * Create an offer using Stellar's native SDEX
     * Handles both market and limit orders through Stellar's built-in matching
     */

    public async getTradingPairs() {
        try {
            // Get all supported assets from database
            const stablecoins: any = await this.callRawQuery(
                `SELECT DISTINCT asset_code FROM supported_currencies 
                 WHERE asset_type = 'stableCoin' AND asset_code IS NOT NULL`
            );

            const fiatCurrencies: any = await this.callRawQuery(
                `SELECT DISTINCT asset_code FROM supported_currencies 
                 WHERE asset_type = 'fiat' AND asset_code IS NOT NULL`
            );

            const stablecoinCodes = stablecoins.map((row: any) => row.asset_code);
            const fiatCodes = fiatCurrencies.map((row: any) => row.asset_code);

            // Generate pairs: stablecoin/fiat combinations
            const pairs: any[] = [];

            // Stablecoin vs Fiat pairs
            stablecoinCodes.forEach((base: string) => {
                fiatCodes.forEach((counter: string) => {
                    pairs.push({
                        baseAsset: base,
                        counterAsset: counter,
                        pair: `${base}/${counter}`,
                        type: 'stablecoin_fiat'
                    });
                });
            });

            // Fiat vs Stablecoin pairs
            fiatCodes.forEach((base: string) => {
                stablecoinCodes.forEach((counter: string) => {
                    pairs.push({
                        baseAsset: base,
                        counterAsset: counter,
                        pair: `${base}/${counter}`,
                        type: 'fiat_stablecoin'
                    });
                });
            });

            // Fiat vs Fiat pairs (cross-fiat trading)
            fiatCodes.forEach((base: string) => {
                fiatCodes.forEach((counter: string) => {
                    if (base !== counter) {
                        pairs.push({
                            baseAsset: base,
                            counterAsset: counter,
                            pair: `${base}/${counter}`,
                            type: 'fiat_fiat'
                        });
                    }
                });
            });

            return this.makeResponse(200, 'Trading pairs retrieved', {
                pairs,
                totalPairs: pairs.length,
                stablecoins: stablecoinCodes,
                fiatCurrencies: fiatCodes
            });

        } catch (error: any) {
            console.error('❌ Error getting trading pairs:', error);
            return this.makeResponse(500, 'Failed to get trading pairs', { error: error.message });
        }
    }



    async supportedAssetCode(asset: string, type: 'stablecoin' | 'fiat'): Promise<boolean> {
        const supportedAssets: any = await this.callQuerySafe(`SELECT asset_code FROM supported_currencies WHERE asset_code = ? AND asset_type = ?`, [asset, type]);
        if (supportedAssets.length > 0) {
            return true;
        }
        return false;
    }

    async validatePaymentMethod(paymentMethod: any): Promise<{ valid: boolean; message: string }> {
        if (!paymentMethod) {
            return { valid: false, message: 'Payment method is required' };
        }

        // Validate mobile money payment method
        if (paymentMethod.type === 'mobile_money') {
            if (!paymentMethod.phone_number) {
                return { valid: false, message: 'Phone number is required' };
            }

            if (!paymentMethod.country_code) {
                return { valid: false, message: 'Country code is required' };
            }
            if (!paymentMethod.network) {
                return { valid: false, message: 'Network provider is required' };
            }
            if (!paymentMethod.account_name) {
                return { valid: false, message: 'Account name is required' };
            }

            // Validate phone number matches country code (accepts with or without +)
            const countryCodeMap: { [key: string]: string } = {
                'UG': '256',
                'KE': '254',
                'TZ': '255',
                'RW': '250',
                'NG': '234',
                'GH': '233',
                'ZA': '27'
            };

            const expectedPrefix = countryCodeMap[paymentMethod.country_code];
            if (expectedPrefix) {
                // Normalize phone number by removing + if present
                const normalizedPhone = paymentMethod.phone_number.replace(/^\+/, '');
                if (!normalizedPhone.startsWith(expectedPrefix)) {
                    return {
                        valid: false,
                        message: `Phone number must start with ${expectedPrefix} or +${expectedPrefix} for country code ${paymentMethod.country_code}`
                    };
                }
            }

            return { valid: true, message: 'Valid mobile money payment method' };
        }

        // Validate bank payment method
        if (paymentMethod.type === 'bank') {
            if (!paymentMethod.bank_name) {
                return { valid: false, message: 'Bank name is required' };
            }
            if (!paymentMethod.account_number) {
                return { valid: false, message: 'Account number is required' };
            }
            if (!paymentMethod.bank_code) {
                return { valid: false, message: 'Bank code is required' };
            }
            return { valid: true, message: 'Valid bank payment method' };
        }

        // Validate crypto payment method (address-based)
        if (paymentMethod.address) {
            if (!paymentMethod.asset_code) {
                return { valid: false, message: 'Asset code is required' };
            }
            if (!paymentMethod.chain) {
                return { valid: false, message: 'Chain is required' };
            }
            return { valid: true, message: 'Valid crypto payment method' };
        }

        return { valid: false, message: 'Invalid payment method type' };
    }
    public async createPendingRailOrder(data: {
        clientId: string;
         referenceId: string;
        sourceAsset: string;
        chain: string;
        destinationAsset: string;
        paymentMethod: any;
        amount: string;
        expectedAmount?: string;
        slippage?: number;
        payoutData?: any;
        expiresAt?: Date;
    }): Promise<any> {
        try {
            const clientId = process.env.TRADER_ACCOUNT || "";
            const supportedchains = ['BSC', 'BASE', 'STELLAR'];
            const supportedAssets = ['USDC', 'USDT'];


            if (!supportedchains.includes(data.chain)) {
                return this.makeResponse(400, 'Chain not supported');
            }

            const paymentMethodValidation = await this.validatePaymentMethod(data.paymentMethod);
            if (!paymentMethodValidation.valid) {
                return this.makeResponse(400, paymentMethodValidation.message);
            }

            const supportedSourceAssetCode = await this.supportedAssetCode(data.sourceAsset, 'stablecoin');
            if (!supportedSourceAssetCode) {
                return this.makeResponse(400, 'Source currency not supported');
            }
            const supportedDestinationAssetCode = await this.supportedAssetCode(data.destinationAsset, 'fiat');
            if (!supportedDestinationAssetCode) {
                return this.makeResponse(400, 'Destination currency not supported');
            }


            let sentchain = 'evm'
            if (data.chain == 'STELLAR') {
                sentchain = 'STELLAR';
            }
            // Add a check for referenceId
            if (!data.referenceId || typeof data.referenceId !== 'string' || data.referenceId.trim() === '') {
                return this.makeResponse(400, 'referenceId is required and must be a non-empty string');
            }

            // Check if referenceId already exists in the database
            const existingOrder: any = await this.callQuerySafe(
                'SELECT id FROM quote_requests WHERE reference_id = ? LIMIT 1',
                [data.referenceId]
            );
            if (existingOrder && existingOrder.length > 0) {
                return this.makeResponse(409, 'referenceId already exists');
            }
            console.log('🚂 Creating pending rail order with deposit address:', data);

            const {
                 referenceId,
                sourceAsset,
                destinationAsset,
                chain,
                paymentMethod,
                amount,
                expectedAmount,
                slippage = 1,
                payoutData,
                expiresAt
            } = data;

            const requestId = this.getTransId();
            const expiresAtFormatted = expiresAt ? formatDateForMySQL(expiresAt) : null;

            // Get market price with slippage for liquidity rail
            const marketPriceResult = await this.getMarketPrice(
                sourceAsset,
                destinationAsset,
                amount,
                'sell',
                slippage
            );
            console.log('marketPriceResult', marketPriceResult)

            if (!marketPriceResult.success) {
                return this.makeResponse(400, `Failed to get market price: ${marketPriceResult.message}`);
            }

            let depositAddress = null;
            const marketPrice = parseFloat(marketPriceResult.price);
            const estimatedReceiveAmount = parseFloat(marketPriceResult.data.estimatedReceiveAmount);

            if (chain == 'STELLAR') {
                depositAddress = process.env.STELLAR_DEPOSIT_ADDRESS || '';
            } else {

                const walletResult = await this.generateDepositAddress({
                    chain: sentchain,
                    referenceId: referenceId,
                    clientId: clientId,
                    asset: sourceAsset,
                    amount: parseFloat(amount)
                });

                if (walletResult.status !== 200) {
                    console.error('❌ Wallet service error:', walletResult);
                    return this.makeResponse(walletResult.status || 500, `Wallet service error: ${walletResult.message || 'Unknown error'}`, walletResult);
                }

                depositAddress = walletResult.data?.deposit_address || walletResult.data?.address || walletResult.data;

            }
            const memo = Math.floor(1000 + Math.random() * 9000).toString();


            // Save to quote_requests table
            const quoteData = {
                request_id: requestId,
                client_id: clientId,
                reference_id: referenceId,
                source_asset: sourceAsset,
                destination_asset: destinationAsset,
                amount: parseFloat(amount),
                expected_amount: expectedAmount ? parseFloat(expectedAmount) : estimatedReceiveAmount,
                slippage: slippage,
                status: 'pending',
                deposit_address: depositAddress,
                payout_data: payoutData ? JSON.stringify(payoutData) : null,
                expires_at: expiresAtFormatted
            };

            const dbResult = await this.insertData('quote_requests', quoteData);

            if (dbResult === false) {
                return this.makeResponse(500, 'Failed to save pending rail order', null);
            }

            console.log('✅ Pending rail order created with deposit address');
            const fee = 0

            // Return response in format expected by Liquidity Rail Admin
            return this.makeResponse(200, 'Rail quote created', {
                id: requestId,
                reference_id: referenceId,
                transaction_type: 'off_ramp',
                from_asset: sourceAsset,
                to_currency: destinationAsset,
                from_amount: parseFloat(amount),
                receive_amount: estimatedReceiveAmount,
                rate: marketPrice,
                fee: fee,
                address: depositAddress,
                memo: chain == 'STELLAR' ? memo : null,
                expires_at: expiresAtFormatted,

            });

        } catch (error: any) {
            console.error('❌ Error creating pending rail order:', error);
            return this.makeResponse(500, 'Failed to create pending rail order', { error: error.message });
        }
    }

    public async getPendingRailOrders(clientId: string): Promise<any> {
        try {
            const orders = await this.callRawQuery(
                `SELECT * FROM quote_requests WHERE client_id = ? AND status = 'pending' ORDER BY created_at DESC`,
                [clientId]
            );
            return this.makeResponse(200, 'Pending rail orders retrieved successfully', orders || []);
        } catch (error: any) {
            console.error('❌ Error getting pending rail orders:', error);
            return this.makeResponse(500, 'Failed to get pending rail orders', { error: error.message });
        }
    }
    async handleDepositReceivedWebhook(data: WebhookData) {
        try {
            console.log('🔄 Handling deposit received webhook:', data);
            const quote = await this.callQuerySafe(
                `SELECT * FROM quote_requests WHERE lower(deposit_address) = ? AND status = 'pending' and source_asset = ? and amount = ? LIMIT 1`,
                [data.chainInfo?.to_address.toLocaleLowerCase(), data.currency, parseFloat(data.amount)]
            );
            if (!quote || !Array.isArray(quote) || quote.length === 0) {
                return this.makeResponse(404, 'No pending rail order found for this address', quote);
            }
            await this.updateData('quote_requests', `reference_id='${quote[0].reference_id}'`, { status: 'processing' });
            return await this.executeMarketSwapAndPayout(data,quote[0].reference_id);
        } catch (error: any) {
            console.error('❌ Error handling deposit received webhook:', error);
            return this.makeResponse(500, 'Failed to handle deposit received webhook', { error: error.message });
        }
    }
    public async executeMarketSwapAndPayout(webhookData: WebhookData,reference_id: string) {
        try {
            const {  transaction_id, amount, currency, chainInfo } = webhookData;

            const pendingOrder = await this.callQuerySafe(
                `SELECT * FROM quote_requests WHERE reference_id = ? AND status = 'processing' LIMIT 1`,
                [reference_id]
            );

            await lrWebhookSender.sendCryptoReceived(reference_id, reference_id, chainInfo as unknown as LRChainInfo);

            if (!pendingOrder || !Array.isArray(pendingOrder) || pendingOrder.length === 0) {
                return this.makeResponse(404, 'No pending rail order found for this reference', null);
            }

            const order = pendingOrder[0];
            try {
                

                const orderData: TradeOffer = {
                    clientId: process.env.TRADER_ACCOUNT || "",
                    baseAsset: order.source_asset,
                    counterAsset: order.destination_asset,
                    amount: order.amount.toString(),
                    orderType: 'sell',
                    type: 'market',
                    slippage: order.slippage,
                    referenceId: order.reference_id
                };

                const swapResult = await this.createOffer(orderData);

                // Handle XDR Read Error - treat as success and continue to payout
                const isXDRReadError = swapResult.message?.includes('XDR Read Error') || 
                                     swapResult.message?.includes('Transaction could not be sent');
                
                if (swapResult.status !== 200 && !isXDRReadError) {
                    await this.updateData('quote_requests', `reference_id='${reference_id}'`, {
                        status: 'SWAP_FAILED',
                        message: swapResult.message
                    });
                    return this.makeResponse(swapResult.status, `Rail order execution failed: ${swapResult.message}`, swapResult.data);
                }

                // If XDR error, log but continue as success
                if (isXDRReadError) {
                    console.log('⚠️ XDR Read Error detected, continuing as success:', swapResult.message);
                }

                await this.updateData('quote_requests', `reference_id='${reference_id}'`, {
                    status: 'RECEIVED'
                });



                console.log('✅ Rail order executed successfully');




                // If payout data exists, execute payout
                if (order.payout_data) {
                    try {
                        // Parse payout_data if it's a string, otherwise use as is
                        const payoutData = typeof order.payout_data === 'string'
                            ? JSON.parse(order.payout_data)
                            : order.payout_data;

                        const payoutResult = await this.executeDirectPayout({
                            clientId: process.env.TRADER_ACCOUNT || "",
                            amount: order.expected_amount,
                            currency: order.destination_asset,
                            mobileNumber: payoutData.phone_number,
                            provider: payoutData.provider,
                            reference_id: order.reference_id,
                            payment_method_id: payoutData.payment_method_id
                        });


                        if (payoutResult.status === 202) {
                            await this.updateData('quote_requests', `reference_id='${reference_id}'`, {
                                status: 'INITIATED',
                                message: payoutResult.message
                            });
                            console.log('✅ Payout executed successfully');

                            // Send fiat_sent webhook to liquidityRailAdmin
                            await lrWebhookSender.sendFiatSent({
                                transaction_id: order.request_id || order.order_id,
                                reference_id: order.reference_id,
                                amount: parseFloat(payoutData.amount),
                                currency: payoutData.currency,
                                amount_delivered: parseFloat(payoutData.amount),
                                fee: 0,
                                external_reference_id: payoutResult.data?.transactionId || order.reference_id,
                                payment_type: 'mobile_money',
                                payment_method_id: payoutData.mobileNumber,
                                status: 'SUCCESSFUL'
                            });
                        } else {
                            await this.updateData('quote_requests', `reference_id='${reference_id}'`, {
                                status: 'ONHOLD',
                                message: payoutResult.message
                            });
                            console.error('❌ Payout failed:', payoutResult.message);

                            // Send failed fiat_sent webhook
                            await lrWebhookSender.sendFiatSent({
                                transaction_id: order.request_id || order.order_id,
                                reference_id: order.reference_id,
                                amount: parseFloat(payoutData.amount),
                                currency: payoutData.currency,
                                amount_delivered: 0,
                                fee: 0,
                                external_reference_id: order.reference_id,
                                payment_type: 'mobile_money',
                                payment_method_id: payoutData.mobileNumber,
                                status: 'FAILED'
                            });
                        }
                    } catch (payoutError: any) {
                        console.error('❌ Error executing payout:', payoutError);
                    }
                }

                return this.makeResponse(200, 'Rail order executed successfully', {
                    requestId: order.request_id,
                    orderId: swapResult.data?.orderId || reference_id,
                    stellarHash: swapResult.data?.stellarHash || '',
                    executedAmount: swapResult.data?.executedTrades || 0
                });

            } catch (executionError: any) {
                // Update status to failed
                await this.updateData('quote_requests', `reference_id='${reference_id}'`, {
                    status: 'SWAP_FAILED',
                    message: executionError.message
                });

                console.error('❌ Rail order execution failed:', executionError);
                return this.makeResponse(500, 'Rail order execution failed', { error: executionError.message });
            }

        } catch (error: any) {
            console.error('❌ Error executing pending rail order:', error);
            return this.makeResponse(500, 'Failed to execute pending rail order', { error: error.message });
        }
    }

    public async createOffer(data: TradeOffer) {
        try {
            logger.info('🔹 Creating SDEX offer:', data);

            const { clientId, baseAsset, counterAsset, amount, price, orderType, type } = data;

            // Get client wallet
            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (!apiInfo) {
                return this.makeResponse(400, 'Client wallet not found');
            }

            const stellarService = new StellarService();

            if (type === 'market') {
                // Get market price with slippage for market orders
                const marketPriceResult = await this.getMarketPrice(
                    baseAsset,
                    counterAsset,
                    amount,
                    orderType,
                    data.slippage || 0.5
                );

                if (!marketPriceResult.success) {
                    return this.makeResponse(400, `Failed to get market price: ${marketPriceResult.message}`);
                }

                logger.info('💰 Using market price:', marketPriceResult.data);

                // For market orders, use path payment for immediate execution
                const response = await stellarService.makePathPayment({
                    senderSecretKey: apiInfo.secret_key,
                    baseAsset,
                    counterAsset,
                    amount,
                    price: marketPriceResult.price, // Use adjusted price (already includes slippage)
                    orderType,
                    slippage: data.slippage || 0.5
                });
                console.log('🔹 Market order response:', response);
                return response





            } else {
                // For limit orders, create a traditional offer
                const result = await stellarService.createLimitOffer({
                    senderSecretKey: apiInfo.secret_key,
                    baseAsset,
                    counterAsset,
                    amount,
                    price: price!,
                    orderType
                });

                if (result.response === 1) {
                    const stellarHash = result.message;
                    const stellarOfferId = result.data; // Get the actual offer ID from Stellar

                    // Generate unique order_id
                    const orderId = `SDEX_${Date.now()}_${clientId}`;

                    // Determine selling and buying assets based on order type
                    let sellingAsset, buyingAsset, sellingAmount, buyingAmount;
                    if (orderType === 'sell') {
                        // Selling base asset for counter asset
                        sellingAsset = baseAsset;
                        buyingAsset = counterAsset;
                        sellingAmount = amount;
                        buyingAmount = (parseFloat(amount) * parseFloat(price!)).toFixed(8);
                    } else {
                        // Buying base asset with counter asset
                        sellingAsset = counterAsset;
                        buyingAsset = baseAsset;
                        sellingAmount = (parseFloat(amount) * parseFloat(price!)).toFixed(8);
                        buyingAmount = amount;
                    }

                    // Store order in database as a log
                    try {
                        const orderData = {
                            order_id: orderId,
                            client_id: clientId,
                            stellar_offer_id: stellarOfferId || stellarHash, // Store actual offer ID or hash as fallback
                            selling_asset: sellingAsset,
                            buying_asset: buyingAsset,
                            amount: parseFloat(amount),
                            transfer_amount: parseFloat(sellingAmount),
                            receivable_amount: parseFloat(buyingAmount), // Amount expected to receive
                            filled_amount: 0,
                            price: parseFloat(price!),
                            order_type: orderType,
                            status: 'active',
                            stellar_hash: stellarHash,
                            created_at: new Date(),
                            updated_at: new Date()
                        };

                        console.log('💾 Storing order in database:', {
                            orderType,
                            amount: orderData.amount,
                            transfer_amount: orderData.transfer_amount,
                            receivable_amount: orderData.receivable_amount,
                            selling_asset: orderData.selling_asset,
                            buying_asset: orderData.buying_asset,
                            price: orderData.price
                        });

                        await this.insertData('stellar_orders', orderData);

                        logger.info(`✅ Order stored in database: ${orderId}, Stellar Offer ID: ${stellarOfferId}`);
                    } catch (dbError: any) {
                        logger.error('⚠️  Failed to store order in database:', dbError.message);
                        // Continue even if DB storage fails - order is on SDEX
                    }

                    // Update the stellar_offer_id from Horizon (async, don't block response)
                    this.cacheOffers(apiInfo.public_key, clientId).catch(err => {
                        logger.warn('⚠️  Failed to cache offers:', err.message);
                    });

                    return this.makeResponse(200, 'Limit offer created successfully', result);
                } else {
                    return this.makeResponse(result.response === 203 ? 400 : 500, result.message);
                }
            }


        } catch (error: any) {
            logger.error('❌ Error creating SDEX offer:', error);
            return this.makeResponse(500, `Failed to create offer: ${error.message}`);
        }
    }

    /**
     * Execute a path payment for immediate execution (market orders)
     * Uses Stellar's built-in path finding to get the best price
     */
    public async makePathPayment(data: {
        clientId: string;
        baseAsset: string;
        counterAsset: string;
        amount: string;
        price: string;
        orderType: 'buy' | 'sell';
        slippage?: number;
    }): Promise<PathPaymentResult> {
        try {
            logger.info('🔄 Executing path payment via StellarService:', data);

            const apiInfo = await this.getDecryptedApiKey(data.clientId);
            if (!apiInfo) {
                return {
                    status: 400,
                    message: 'Client wallet not found',
                    data: null,
                    success: false,
                    amount: '0',
                    price: '0',
                    stellarHash: ''
                };
            }

            const stellarService = new StellarService();
            return await stellarService.makePathPayment({
                senderSecretKey: apiInfo.secret_key,
                baseAsset: data.baseAsset,
                counterAsset: data.counterAsset,
                amount: data.amount,
                price: data.price,
                orderType: data.orderType,
                slippage: data.slippage
            });

        } catch (error: any) {
            logger.error('❌ Error executing path payment:', error);
            return {
                status: 400,
                message: error.message,
                data: null,
                success: false,
                amount: '0',
                price: '0',
                stellarHash: ''
            };
        }
    }


    public async getPrice(baseAsset: string, counterAsset: string, orderType: 'buy' | 'sell', amount: number = 1, type = 'limit'): Promise<PriceResult> {
        try {
            if (type === 'market') {
                const marketPriceResult = await this.getMarketPrice(
                    baseAsset,
                    counterAsset,
                    amount.toFixed(7),
                    orderType,
                    0.1
                );
                return marketPriceResult;
            }

            logger.info('💰 Getting price for:', { baseAsset, counterAsset, orderType });

            const baseAssetStellar = await this.createStellarAsset(baseAsset);
            const counterAssetStellar = await this.createStellarAsset(counterAsset);

            // Get orderbook
            const orderbook = await this.server.orderbook(baseAssetStellar, counterAssetStellar).call();

            let price: string;

            if (orderType === 'sell') {
                // For SELL orders: Get best bid price (highest price buyers are willing to pay)
                if (!orderbook.bids || orderbook.bids.length === 0) {
                    return {
                        status: 400,
                        message: 'No bids available',
                        data: null,
                        success: false,
                        price: '0'
                    };
                }
                const bestBid = orderbook.bids[0];
                price = bestBid.price;
            } else {
                // For BUY orders: Get best ask price (lowest price sellers are offering)
                if (!orderbook.asks || orderbook.asks.length === 0) {
                    return {
                        status: 400,
                        message: 'No asks available',
                        data: null,
                        success: false,
                        price: '0'
                    };
                }
                const bestAsk = orderbook.asks[0];
                price = bestAsk.price;
            }

            logger.info('✅ Current price:', { baseAsset, counterAsset, orderType, price });

            return {
                status: 200,
                message: 'Price retrieved successfully',
                data: { price },
                success: true,
                price: price
            };

        } catch (error: any) {
            logger.error('❌ Error getting price:', error);
            return {
                status: 400,
                message: error.message,
                data: null,
                success: false,
                price: '0'
            };
        }
    }

    /**
     * Get market price with slippage for path payments
     * Uses Stellar's path finding to get actual executable price
     */
    public async getMarketPrice(
        baseAsset: string,
        counterAsset: string,
        amount: string,
        orderType: 'buy' | 'sell',
        slippage: number = 0.5
    ): Promise<PriceResult> {
        try {
            logger.info('💰 Getting market price with slippage:', {
                baseAsset,
                counterAsset,
                amount,
                orderType,
                slippage
            });

            const baseAssetStellar = await this.createStellarAsset(baseAsset);
            const counterAssetStellar = await this.createStellarAsset(counterAsset);

            // Determine source and destination based on order type
            let sourceAsset: Asset;
            let destAsset: Asset;
            let sourceAmount: string;

            if (orderType === 'sell') {
                // Selling base for counter
                sourceAsset = baseAssetStellar;
                destAsset = counterAssetStellar;
                sourceAmount = amount;
            } else {
                // Buying base with counter
                sourceAsset = counterAssetStellar;
                destAsset = baseAssetStellar;
                // For buy, we need to estimate how much counter asset we need
                const estimatedPrice = await this.getPrice(baseAsset, counterAsset, orderType);
                if (!estimatedPrice.success) {
                    return estimatedPrice;
                }
                sourceAmount = (parseFloat(amount) * parseFloat(estimatedPrice.price)).toFixed(7);
            }

            // Use Stellar's strict send path to find the best price
            const paths = await this.server
                .strictSendPaths(sourceAsset, sourceAmount, [destAsset])
                .call();

            if (!paths.records || paths.records.length === 0) {
                return {
                    status: 409,
                    message: 'Not enough liquidity to fill your order',
                    data: null,
                    success: false,
                    price: '0'
                };
            }

            // Get the best path (first one is usually best)
            const bestPath = paths.records[0];
            const destAmount = parseFloat(bestPath.destination_amount);
            const srcAmount = parseFloat(bestPath.source_amount);

            // Calculate actual market price
            let marketPrice: number;
            if (orderType === 'sell') {
                // Price = how much counter you get per base
                marketPrice = destAmount / parseFloat(amount);
            } else {
                // Price = how much counter per base
                marketPrice = srcAmount / destAmount;
            }

            // Apply slippage tolerance
            const slippageMultiplier = 1 + (slippage / 100);
            const priceWithSlippage = (marketPrice * slippageMultiplier).toFixed(7);

            logger.info('✅ Market price calculated:', {
                baseAsset,
                counterAsset,
                orderType,
                marketPrice: marketPrice.toFixed(7),
                priceWithSlippage,
                slippage: `${slippage}%`,
                destAmount: bestPath.destination_amount,
                sourceAmount: bestPath.source_amount
            });

            return {
                status: 200,
                message: 'Market price retrieved successfully',
                data: {
                    price: priceWithSlippage,
                    marketPrice: marketPrice.toFixed(7),
                    priceWithSlippage,
                    estimatedReceiveAmount: bestPath.destination_amount,
                    slippage: slippage
                },
                success: true,
                price: priceWithSlippage // Return adjusted price as main price
            };

        } catch (error: any) {
            logger.error('❌ Error getting market price:', error);
            return {
                status: 400,
                message: error.message,
                data: null,
                success: false,
                price: '0'
            };
        }
    }

    public async getOrderBook(baseAsset: string, counterAsset: string, clientId?: string): Promise<OrderBookResult> {
        try {
            // Default to USDT/UGX if 'all' is specified
            if (!baseAsset || baseAsset === 'all') {
                baseAsset = 'USDT';
            }
            if (!counterAsset || counterAsset === 'all') {
                counterAsset = 'UGX';
            }

            logger.info('📊 Getting SDEX orderbook for:', { baseAsset, counterAsset, clientId });

            const baseAssetStellar = await this.createStellarAsset(baseAsset, clientId);
            const counterAssetStellar = await this.createStellarAsset(counterAsset, clientId);

            // Get orderbook from Stellar
            const orderbook = await this.server.orderbook(baseAssetStellar, counterAssetStellar).call();

            // Fetch stored orders from database to get original amounts
            let storedOrders: any[] = [];
            try {
                const dbOrders = await this.callRawQuery(
                    `SELECT stellar_offer_id, amount as original_amount, transfer_amount, receivable_amount 
                    FROM stellar_orders 
                     WHERE status = 'active' AND stellar_offer_id IS NOT NULL`,
                    []
                );
                storedOrders = dbOrders || [];
                logger.info(`📋 Fetched ${storedOrders.length} stored orders from database`);
            } catch (dbError) {
                logger.warn('⚠️ Could not fetch stored orders from database:', dbError);
            }

            // Create a map for quick lookup of original amounts
            const originalAmountsMap = new Map();
            storedOrders.forEach((order: any) => {
                originalAmountsMap.set(String(order.stellar_offer_id), {
                    original_amount: order.original_amount,
                    transfer_amount: order.transfer_amount,
                    receivable_amount: order.receivable_amount
                });
            });

            // Map SDEX orders to match the old database format
            // Note: Stellar bids/asks are from the perspective of the orderbook (base/counter)
            // - Bids: Orders to BUY base (UGX) with counter (USDT)
            // - Asks: Orders to SELL base (UGX) for counter (USDT)
            // Stellar's price is always "price of what you're buying per what you're selling"
            // For bids: price = counter per base (e.g., USDT per UGX)
            // For asks: price = counter per base (e.g., USDT per UGX)
            console.log('orderbook====>', JSON.stringify(orderbook, null, 2));
            const allOrders = [
                ...orderbook.bids.map((bid: any) => {
                    // Bid: buying base (UGX), selling counter (USDT)
                    // Calculate price correctly from price_r (numerator/denominator)
                    const price = bid.price_r ? (bid.price_r.n / bid.price_r.d) : parseFloat(bid.price);
                    const normalizedPrice = price.toFixed(8);

                    // Stellar orderbook doesn't include seller info, so all orders are marked as 'SDEX'
                    const actualClientId = 'SDEX';
                    const actualOrderId = `SDEX_BID_${bid.id}`;

                    // Get original amount from database if available
                    const storedData = originalAmountsMap.get(String(bid.id));
                    const currentAmount = (parseFloat(bid.amount) / parseFloat(normalizedPrice)).toFixed(7);
                    const originalAmount = storedData?.original_amount || currentAmount;
                    const filledAmount = storedData ? (parseFloat(originalAmount) - parseFloat(currentAmount)).toFixed(7) : '0';

                    return {
                        order_id: actualOrderId,
                        stellar_offer_id: bid.id,
                        client_id: actualClientId,
                        base_asset: baseAsset,
                        counter_asset: counterAsset,
                        selling_asset: counterAsset,
                        buying_asset: baseAsset,
                        amount: currentAmount,
                        receivable_amount: currentAmount,
                        price: normalizedPrice,
                        order_type: 'buy',
                        status: 'active',
                        created_at: new Date().toISOString(),
                        filled_amount: filledAmount,
                        original_amount: originalAmount,
                        original_receivable_amount: storedData?.receivable_amount || (parseFloat(bid.amount) * parseFloat(normalizedPrice)).toFixed(8)
                    };
                }),
                ...orderbook.asks.map((ask: any) => {
                    // Ask: selling base (UGX), buying counter (USDT)
                    // Calculate price correctly from price_r (numerator/denominator)
                    const price = ask.price_r ? (ask.price_r.n / ask.price_r.d) : parseFloat(ask.price);
                    const normalizedPrice = price.toFixed(8);

                    // Stellar orderbook doesn't include seller info, so all orders are marked as 'SDEX'
                    const actualClientId = 'SDEX';
                    const actualOrderId = `SDEX_ASK_${ask.id}`;

                    // Get original amount from database if available
                    const storedData = originalAmountsMap.get(String(ask.id));
                    const currentAmount = ask.amount; // Base asset amount
                    const originalAmount = storedData?.original_amount || currentAmount;
                    const filledAmount = storedData ? (parseFloat(originalAmount) - parseFloat(currentAmount)).toFixed(7) : '0';

                    return {
                        order_id: actualOrderId,
                        stellar_offer_id: ask.id,
                        client_id: actualClientId,
                        base_asset: baseAsset,
                        counter_asset: counterAsset,
                        selling_asset: baseAsset,
                        buying_asset: counterAsset,
                        amount: currentAmount, // Current remaining amount
                        receivable_amount: (parseFloat(currentAmount) * parseFloat(normalizedPrice)).toFixed(8),
                        price: normalizedPrice, // Counter per base (UGX per USDT)
                        order_type: 'sell',
                        status: 'active',
                        created_at: new Date().toISOString(),
                        filled_amount: filledAmount,
                        original_amount: originalAmount,
                        original_receivable_amount: storedData?.receivable_amount || (parseFloat(ask.amount) * parseFloat(normalizedPrice)).toFixed(8)
                    };
                })
            ];

            // Separate buy and sell orders for frontend
            const buyOrders = allOrders.filter(order => order.order_type === 'buy');
            const sellOrders = allOrders.filter(order => order.order_type === 'sell');

            logger.info('✅ SDEX orderbook retrieved:', {
                totalOrders: allOrders.length,
                buyOrders: buyOrders.length,
                sellOrders: sellOrders.length
            });

            return {
                status: 200,
                message: 'Orders retrieved successfully',
                data: allOrders,
                success: true,
                buyOrders: buyOrders,
                sellOrders: sellOrders
            };

        } catch (error: any) {
            logger.error('❌ Error getting SDEX orderbook:', error);
            return {
                status: 400,
                message: error.message,
                data: null,
                success: false,
                buyOrders: [],
                sellOrders: []
            };
        }
    }

    /**
     * Cancel an order on Stellar SDEX
     * Works directly on-chain, no database lookup needed
     */
    public async cancelOrder(orderId: string, clientId: string) {
        try {
            logger.info('🚫 Cancelling SDEX order:', { orderId, clientId });

            // Get client wallet
            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (!apiInfo) {
                return this.makeResponse(400, 'Client wallet not found');
            }

            const publicKey = apiInfo.public_key;
            logger.info('🔍 Fetching on-chain offers for:', publicKey);

            // Fetch all active offers from Stellar for this account
            const offersResponse = await this.server.offers()
                .forAccount(publicKey)
                .limit(200)
                .call();

            logger.info(`📋 Found ${offersResponse.records.length} active offers on-chain`);

            // Find the specific offer by ID
            const offer = offersResponse.records.find((o: any) => o.id === orderId);

            if (!offer) {
                logger.warn('❌ Offer not found on-chain:', orderId);
                return this.makeResponse(400, 'Order not found on Stellar network. It may have already been filled or cancelled.');
            }

            logger.info('✅ Found offer on-chain:', {
                id: offer.id,
                selling: offer.selling.asset_code || 'XLM',
                buying: offer.buying.asset_code || 'XLM',
                amount: offer.amount,
                price: offer.price
            });

            // Cancel the offer on Stellar by setting amount to 0
            const stellarService = new StellarService();
            const result = await stellarService.cancelOffer({
                senderSecretKey: apiInfo.secret_key,
                offerId: orderId,
                orderType: 'sell', // Type doesn't matter for cancellation
                baseAsset: offer.selling.asset_code || '',
                counterAsset: offer.buying.asset_code || ''
            });

            if (result.response === 1) {
                // Update order status in database if it exists
                try {
                    await this.callRawQuery(
                        `UPDATE stellar_orders SET status = 'cancelled', updated_at = NOW() WHERE order_id = ? OR stellar_offer_id = ?`,
                        [orderId, orderId]
                    );
                } catch (dbError) {
                    logger.warn('⚠️ Could not update database (order may not exist in DB):', dbError);
                }

                logger.info('✅ Order cancelled successfully on-chain:', orderId);
                return this.makeResponse(200, 'Order cancelled successfully', { orderId });
            } else {
                return this.makeResponse(result.response === 203 ? 400 : 500, result.message);
            }
        } catch (error: any) {
            logger.error('❌ Error cancelling order:', error);
            return this.makeResponse(500, `Failed to cancel order: ${error.message}`);
        }
    }

    /**
     * Cancel all active orders for a client
     * Delegates to StellarService for sponsored fee payment
     */
    public async cancelAllOrders(clientId: string) {
        try {
            const stellarService = new StellarService();
            const result = await stellarService.cancelAllOrdersByClientId(clientId);

            // Update database records to mark orders as cancelled if successful
            if (result.response === 1 && result.data?.offers) {
                const offerIds = result.data.offers.map((o: any) => o.offerId.toString());
                if (offerIds.length > 0) {
                    const placeholders = offerIds.map(() => '?').join(',');
                    await this.callRawQuery(
                        `UPDATE stellar_orders SET status = 'cancelled', updated_at = NOW() WHERE stellar_offer_id IN (${placeholders}) AND client_id = ?`,
                        [...offerIds, clientId]
                    );
                }
            }

            return this.makeResponse(result.response === 1 ? 200 : result.response, result.message, result.data);
        } catch (error: any) {
            logger.error('❌ Error cancelling orders:', error);
            return this.makeResponse(500, `Failed to cancel orders: ${error.message}`);
        }
    }

    /**
     * Get order history for a client from Stellar
     * Database stores orders as logs, but we read from Stellar for real-time status
     */
    public async getOrderHistory(clientId: string, baseAsset?: string, counterAsset?: string) {
        try {
            logger.info('📜 Getting order history from Stellar:', { clientId, baseAsset, counterAsset });

            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (!apiInfo) {
                return {
                    status: 400,
                    message: 'Client wallet not found',
                    data: []
                };
            }

            // Get account operations from Stellar for real-time data
            const operations = await this.server.operations()
                .forAccount(apiInfo.public_key)
                .order('desc')
                .limit(200)
                .call();

            // Filter for manage offer operations
            const offerOps = operations.records.filter((op: any) =>
                op.type === 'manage_sell_offer' || op.type === 'manage_buy_offer'
            );

            const orders = offerOps.map((op: any) => ({
                order_id: op.id,
                client_id: clientId,
                transaction_hash: op.transaction_hash,
                created_at: op.created_at,
                type: op.type,
                amount: op.amount,
                price: op.price,
                offer_id: op.offer_id
            }));

            logger.info('✅ Order history retrieved from Stellar:', { count: orders.length });

            return {
                status: 200,
                message: 'Order history retrieved successfully',
                data: orders
            };

        } catch (error: any) {
            logger.error('❌ Error getting order history from Stellar:', error);
            return {
                status: 400,
                message: error.message,
                data: []
            };
        }
    }

    /**
     * Get trade history for a client - returns orders, trades, and swaps
     */
    public async getTradeHistory(clientId: string, baseAsset?: string, counterAsset?: string) {
        try {
            logger.info('📈 Getting trade history:', { clientId, baseAsset, counterAsset });

            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (!apiInfo) {
                return {
                    status: 400,
                    message: 'Client wallet not found',
                    data: {
                        orders: [],
                        trades: [],
                        swaps: []
                    }
                };
            }

            // Get active orders (offers) from Stellar
            const offers = await this.server.offers()
                .forAccount(apiInfo.public_key)
                .limit(200)
                .call();

            const activeOrders = offers.records.map((offer: any) => {
                const sellingAsset = offer.selling.asset_type === 'native' ? 'XLM' : offer.selling.asset_code;
                const buyingAsset = offer.buying.asset_type === 'native' ? 'XLM' : offer.buying.asset_code;
                const offerAmount = parseFloat(offer.amount || '0');
                const offerPrice = parseFloat(offer.price || '0');

                // Calculate what the user will receive
                const amountToReceive = offerAmount * offerPrice;

                // Determine base currency (USDC/USDT are base currencies)
                const isBaseCurrency = (asset: string) => ['USDC', 'USDT'].includes(asset);
                const isSellingBase = isBaseCurrency(sellingAsset);
                const isBuyingBase = isBaseCurrency(buyingAsset);

                // Determine order direction and amounts from user perspective
                let orderType: 'buy' | 'sell';
                let baseAsset: string;
                let counterAsset: string;
                let amountOffering: string;
                let amountWanting: string;
                let pricePerUnit: string;

                if (isSellingBase && !isBuyingBase) {
                    // Selling USDC/USDT for crypto = SELL USDC/USDT
                    orderType = 'sell';
                    baseAsset = sellingAsset; // The USDC/USDT you're selling
                    counterAsset = buyingAsset; // The crypto you're buying
                    amountOffering = offerAmount.toString(); // USDC/USDT amount
                    amountWanting = amountToReceive.toString(); // Crypto amount
                    pricePerUnit = offerPrice.toString();
                } else if (!isSellingBase && isBuyingBase) {
                    // Selling crypto for USDC/USDT = BUY USDC/USDT
                    orderType = 'buy';
                    baseAsset = buyingAsset; // The USDC/USDT you're buying
                    counterAsset = sellingAsset; // The crypto you're selling
                    amountOffering = offerAmount.toString(); // Crypto amount
                    amountWanting = amountToReceive.toString(); // USDC/USDT amount
                    pricePerUnit = offerPrice.toString();
                } else {
                    // Neither is base currency, use original logic
                    orderType = 'sell';
                    baseAsset = sellingAsset;
                    counterAsset = buyingAsset;
                    amountOffering = offerAmount.toString();
                    amountWanting = amountToReceive.toString();
                    pricePerUnit = offerPrice.toString();
                }

                return {
                    order_id: offer.id,
                    client_id: clientId,
                    base_asset: baseAsset,
                    counter_asset: counterAsset,
                    amount: amountOffering,
                    amount_wanting: amountWanting,
                    price: pricePerUnit,
                    order_type: orderType,
                    status: 'active',
                    created_at: offer.last_modified_time || new Date().toISOString(),
                    stellar_offer_id: offer.id,
                    // Additional fields for clarity
                    selling_asset: sellingAsset,
                    buying_asset: buyingAsset,
                    amount_offering: amountOffering,
                    amount_receiving: amountWanting,
                    filled_amount: '0', // Set to 0 for now since Stellar doesn't provide this directly
                    // Original Stellar offer data (fallback to current values for now)
                    original_amount: offerAmount.toString(),
                    original_price: offerPrice.toString(),
                    original_selling: sellingAsset,
                    original_buying: buyingAsset,
                    original_amount_to_receive: amountToReceive.toString()
                };
            });

            // Get completed trades from Stellar
            const trades = await this.server.trades()
                .forAccount(apiInfo.public_key)
                .order('desc')
                .limit(200)
                .call();
            console.log("trades", trades)

            const tradeHistory = trades.records.map((trade: any) => {
                console.log("trade", trade)

                const stellarBaseAmount = parseFloat(trade.base_amount || '0');
                const stellarCounterAmount = parseFloat(trade.counter_amount || '0');

                const stellarBaseAsset = trade.base_asset_type === 'native' ? 'XLM' : trade.base_asset_code;
                const stellarCounterAsset = trade.counter_asset_type === 'native' ? 'XLM' : trade.counter_asset_code;

                // Determine if current account is base_account or counter_account
                const isBaseAccount = trade.base_account === apiInfo.public_key;
                const isCounterAccount = trade.counter_account === apiInfo.public_key;

                // Determine what assets the user gave and received
                let userGaveAsset: string;
                let userGaveAmount: number;
                let userReceivedAsset: string;
                let userReceivedAmount: number;

                if (isBaseAccount) {
                    if (trade.base_is_seller) {
                        // User sold base asset, received counter asset
                        userGaveAsset = stellarBaseAsset;
                        userGaveAmount = stellarBaseAmount;
                        userReceivedAsset = stellarCounterAsset;
                        userReceivedAmount = stellarCounterAmount;
                    } else {
                        // User bought base asset with counter asset
                        userGaveAsset = stellarCounterAsset;
                        userGaveAmount = stellarCounterAmount;
                        userReceivedAsset = stellarBaseAsset;
                        userReceivedAmount = stellarBaseAmount;
                    }
                } else if (isCounterAccount) {
                    if (trade.base_is_seller) {
                        // base_account sold base, so user bought base with counter
                        userGaveAsset = stellarCounterAsset;
                        userGaveAmount = stellarCounterAmount;
                        userReceivedAsset = stellarBaseAsset;
                        userReceivedAmount = stellarBaseAmount;
                    } else {
                        // base_account bought base, so user sold counter
                        userGaveAsset = stellarBaseAsset;
                        userGaveAmount = stellarBaseAmount;
                        userReceivedAsset = stellarCounterAsset;
                        userReceivedAmount = stellarCounterAmount;
                    }
                } else {
                    // Fallback
                    userGaveAsset = stellarBaseAsset;
                    userGaveAmount = stellarBaseAmount;
                    userReceivedAsset = stellarCounterAsset;
                    userReceivedAmount = stellarCounterAmount;
                }

                // Normalize: USDT/USDC should always be the base_asset
                // User is always buying or selling USDT/USDC
                const isBaseCurrency = (asset: string) => ['USDC', 'USDT'].includes(asset);

                let baseAsset: string;
                let counterAsset: string;
                let orderType: 'buy' | 'sell';
                let amount: string;
                let received: string;
                let displayPrice: string;

                if (isBaseCurrency(userGaveAsset)) {
                    // User sold USDT/USDC
                    baseAsset = userGaveAsset; // USDT/USDC
                    counterAsset = userReceivedAsset; // UGX, KES, etc.
                    orderType = 'sell';
                    amount = userGaveAmount.toString(); // USDT amount
                    received = userReceivedAmount.toString(); // UGX amount
                    displayPrice = (userReceivedAmount / userGaveAmount).toString(); // UGX per USDT
                } else if (isBaseCurrency(userReceivedAsset)) {
                    // User bought USDT/USDC
                    baseAsset = userReceivedAsset; // USDT/USDC
                    counterAsset = userGaveAsset; // UGX, KES, etc.
                    orderType = 'buy';
                    amount = userReceivedAmount.toString(); // USDT amount
                    received = userGaveAmount.toString(); // UGX amount
                    displayPrice = (userGaveAmount / userReceivedAmount).toString(); // UGX per USDT
                } else {
                    // Neither is base currency (e.g. XLM/BTC trade)
                    // Keep original orientation
                    baseAsset = stellarBaseAsset;
                    counterAsset = stellarCounterAsset;
                    orderType = isBaseAccount && trade.base_is_seller ? 'sell' : 'buy';
                    amount = userGaveAmount.toString();
                    received = userReceivedAmount.toString();
                    displayPrice = (userReceivedAmount / userGaveAmount).toString();
                }

                return {
                    trade_id: trade.id,
                    client_id: clientId,
                    base_asset: baseAsset, // Normalized: always USDT/USDC
                    counter_asset: counterAsset, // Normalized: always UGX/KES/etc
                    base_amount: amount, // USDT/USDC amount (what user bought/sold)
                    counter_amount: received, // Other currency amount (what user received/gave)
                    amount: amount, // USDT/USDC amount traded
                    received: received, // Other currency amount
                    price: displayPrice, // Counter per base (UGX per USDT)
                    order_type: orderType, // buy/sell from USDT/USDC perspective
                    created_at: trade.ledger_close_time,
                    offer_id: trade.offer_id,
                    trade_type: orderType, // For compatibility
                    status: 'completed'
                };
            });

            logger.info('✅ Trade history retrieved:', {
                orders: activeOrders.length,
                trades: tradeHistory.length
            });

            return {
                status: 200,
                message: 'Trade history retrieved successfully',
                data: {
                    orders: activeOrders,
                    trades: tradeHistory,
                    swaps: [] // For now, swaps are handled separately
                }
            };

        } catch (error: any) {
            logger.error('❌ Error getting trade history:', error);
            return {
                status: 400,
                message: error.message,
                data: {
                    orders: [],
                    trades: [],
                    swaps: []
                }
            };
        }
    }




    public async attachClientToOrder(orderId: string, clientId: string): Promise<ApiResponse> {
        try {
            logger.info('🔗 Attaching client to order:', { orderId, clientId });

            // Check if order exists
            const orderQuery = `SELECT * FROM stellar_orders WHERE order_id = ?`;
            const existingOrder = await this.callRawQuery(orderQuery, [orderId]);

            if (!existingOrder || existingOrder.length === 0) {
                return this.makeResponse(404, 'Order not found');
            }

            // Check if client exists
            const clientQuery = `SELECT * FROM trading_accounts WHERE client_id = ?`;
            const existingClient = await this.callRawQuery(clientQuery, [clientId]);

            if (!existingClient || existingClient.length === 0) {
                return this.makeResponse(404, 'Client not found');
            }

            // Update the order with the client ID
            const updateQuery = `UPDATE stellar_orders SET client_id = ?, updated_at = NOW() WHERE order_id = ?`;
            await this.callRawQuery(updateQuery, [clientId, orderId]);

            logger.info('✅ Client attached to order successfully');

            return this.makeResponse(200, 'Client attached to order successfully', {
                orderId,
                clientId,
                updated: true
            });

        } catch (error: any) {
            logger.error('❌ Error attaching client to order:', error);
            return this.makeResponse(500, `Failed to attach client: ${error.message}`);
        }
    }



    /**
     * Cache offers from Stellar into the database
     * This stores all active offers for an account in the database
     */
    public async cacheOffers(publicKey: string, clientId: string): Promise<ApiResponse> {
        try {
            logger.info('💾 Caching offers for account:', { publicKey, clientId });

            // Wait a bit for the offer to be available on Stellar
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Fetch all active offers for this account
            const offers = await this.server.offers().forAccount(publicKey).call();

            if (!offers.records || offers.records.length === 0) {
                logger.info('No offers found for account:', publicKey);
                return this.makeResponse(200, 'No offers to cache', { cachedCount: 0 });
            }

            logger.info(`📊 Found ${offers.records.length} offers to cache`);

            let cachedCount = 0;
            const errors: string[] = [];

            // Process each offer
            for (const offer of offers.records) {
                try {
                    // Extract asset information
                    const sellingAsset = offer.selling.asset_type === 'native' ? 'XLM' : offer.selling.asset_code;
                    const buyingAsset = offer.buying.asset_type === 'native' ? 'XLM' : offer.buying.asset_code;

                    // Calculate amounts
                    const amount = parseFloat(offer.amount);
                    const price = parseFloat(offer.price);
                    const receivableAmount = amount * price;

                    // Determine order type based on what's being sold/bought
                    const orderType = sellingAsset === 'XLM' ? 'sell' : 'buy';

                    // Generate unique order ID
                    const orderId = `SDEX_${Date.now()}_${offer.id}`;

                    // Prepare order data
                    const orderData = {
                        order_id: orderId,
                        client_id: clientId,
                        stellar_offer_id: offer.id.toString(),
                        selling_asset: sellingAsset,
                        buying_asset: buyingAsset,
                        amount: amount,
                        transfer_amount: amount,
                        receivable_amount: receivableAmount,
                        filled_amount: 0,
                        price: price,
                        order_type: orderType,
                        status: 'active',
                        stellar_hash: null, // Not available from offers endpoint
                        created_at: new Date(),
                        updated_at: new Date()
                    };

                    // Check if offer already exists in database
                    const existingQuery = `SELECT id FROM stellar_orders WHERE stellar_offer_id = ?`;
                    const existing = await this.callRawQuery(existingQuery, [offer.id.toString()]);

                    if (existing && existing.length > 0) {
                        logger.info(`⏭️ Offer ${offer.id} already cached, skipping`);
                        continue;
                    }

                    // Insert the offer into database
                    await this.insertData('stellar_orders', orderData);
                    cachedCount++;

                    logger.info(`✅ Cached offer ${offer.id}: ${sellingAsset} → ${buyingAsset} (${amount} @ ${price})`);

                } catch (offerError: any) {
                    const errorMsg = `Failed to cache offer ${offer.id}: ${offerError.message}`;
                    logger.error('❌', errorMsg);
                    errors.push(errorMsg);
                }
            }

            logger.info(`✅ Successfully cached ${cachedCount} offers`);

            return this.makeResponse(200, `Cached ${cachedCount} offers successfully`, {
                totalOffers: offers.records.length,
                cachedCount: cachedCount,
                errors: errors.length > 0 ? errors : null
            });

        } catch (error: any) {
            logger.error('❌ Error caching offers:', error);
            return this.makeResponse(500, `Failed to cache offers: ${error.message}`);
        }
    }

    public async updateOfferIdByHash(publicKey: string): Promise<ApiResponse> {
        try {
            await new Promise(resolve => setTimeout(resolve, 1000));

            let offerId = null;
            try {
                const offers = await this.server.offers().forAccount(publicKey).call();
                // Find the offer we just created (latest one)
                const newOffer = offers.records.find((offer: any) =>
                    offer.selling && offer.buying
                );

                if (newOffer) {
                    offerId = newOffer.id;
                    console.log('📝 Offer ID:', offerId);
                }
            } catch (queryError: any) {
                console.error('⚠️ Could not retrieve offer ID:', queryError.message);
            }

            return {
                status: 200,
                message: 'Offer ID updated successfully',
                data: offerId
            };
        } catch (error: any) {
            return {
                status: 500,
                message: error.message,
                data: null
            };
        }
    }

    /**
     * Create Stellar Asset object
     */
    private async createStellarAsset(assetCode: string, clientId?: string): Promise<Asset> {
        if (assetCode === 'XLM') {
            return Asset.native();
        }

        // Use GetTradingIssuer to get the issuer
        const issuer = await this.GetTradingIssuer(assetCode, clientId || 'default');
        return new Asset(assetCode, issuer);
    }


}

export default StellarSDEXTrading;
