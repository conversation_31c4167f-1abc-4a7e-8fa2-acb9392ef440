
export interface CreatePaymentIntentData {
    reference_id: string;
    amount: number | string;
    from_currency: string;
    to_currency: string;
    chain?: string;
    transaction_type: 'on_ramp' | 'off_ramp';
    payment_method: {
        address: string;
        asset_code: string;
        chain: string;
        memo: string;
    } | MobileMoneyPayment | BankPayment | cryptoPaymentMethod;
}
interface cryptoPaymentMethod {
    address: string;
    asset_code: string;
    chain: string;
    memo: string;
}
interface MobileMoneyPayment {
    type: 'mobile_money';
    currency: string;
    phone_number: string;
    country_code: string;
    network?: string;
    account_name?: string;
}
interface BankPayment {
    type: 'bank';
    bank_name: string;
    bank_code: string;
    currency: string;
    account_number: string;
    account_name?: string;
    swift_code?: string;
    bank_country?: string;
}
export interface QuoteRequest {
    amount: number;
    currency: string;
    asset_code: string;
    chain?: string;
    provider_id?: number;
}

export interface GetTransactionRequest {
    transaction_id: string;
}

export interface WebhookEvent {
    event_type: string;
    transaction_id: string;
    status: string;
    data: any;
}

// ============= RESPONSE INTERFACES =============

export interface FiatResponse {
    transaction_id: string;
    status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED" | "EXPIRED";
    send_amount: string;
    payment_type: "CARD" | "BANK" | "MOBILE_MONEY";
    payment_url: string;
}

export interface CryptoResponse {
    address: string;
    asset_code: string;
    chain: "BSC" | "TRON" | "STELLAR";
    memo: string;
}

export interface QuoteResponse {
    quote_id: string;
    rate: number | string;
    total_amount: number | string;
    fee: number | string;
    expires_at: string;
    from_currency: string;
    to_currency: string;
    from_amount: number | string;
    to_amount: number | string;
    chain?: string;
}

export interface TransactionResponse {
    transaction_id: string;
    reference_id: string;
    status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED" | "EXPIRED" | "CANCELLED";
    from_currency: string;
    to_currency: string;
    from_amount: string;
    to_amount: string;
    exchange_rate: string;
    fee: string;
    payment_method: {
        address?: string;
        asset_code?: string;
        chain?: string;
        memo?: string;
    };
    created_at: string;
    updated_at: string;
    expires_at?: string;
}

export interface RateResponse {
    provider: string;
    providerQuoteId: string;
    from: string;
    providerId: number;
    to: string;
    fiatAmount: number | string;
    toAmount: number | string;
    cryptoAmount: number | string;
    fee: number | string;
    quoteId: string;
    expiresAt?: string;
    quotedPrice?: number | string;
    [key: string]: any;
}

// ============= API RESPONSE WRAPPERS =============

export interface ApiResponse<T = any> {
    status: number;
    message: string;
    data: T;
}

export type CreatePaymentIntentResponse = ApiResponse<FiatResponse | CryptoResponse>;
export type GetQuoteResponse = ApiResponse<QuoteResponse>;
export type GetTransactionResponse = ApiResponse<TransactionResponse>;
export type WebhookResponse = ApiResponse<{ received: boolean }>;

