import StellarTrading from '../src/models/StellarTrading';

async function verifyDatabaseSchema() {
    try {
        const stellarTrading = new StellarTrading();
        
        console.log('🔍 Verifying Database Schema and Current Data\n');
        
        // Check if receivable_amount column exists
        console.log('1. Checking if receivable_amount column exists...');
        try {
            const columns = await stellarTrading.callRawQuery(`DESCRIBE stellar_orders`);
            const hasReceivableAmount = columns.some((col: any) => col.Field === 'receivable_amount');
            
            if (hasReceivableAmount) {
                console.log('✅ receivable_amount column exists');
            } else {
                console.log('❌ receivable_amount column missing - need to run migration');
                console.log('   Run: ALTER TABLE stellar_orders ADD COLUMN receivable_amount DECIMAL(20,8) NULL AFTER transfer_amount;');
            }
        } catch (error: any) {
            console.log('❌ Error checking schema:', error.message);
        }
        
        console.log('\n2. Checking current orders in database...');
        try {
            const orders = await stellarTrading.callRawQuery(`
                SELECT 
                    order_id,
                    order_type,
                    selling_asset,
                    buying_asset,
                    amount,
                    transfer_amount,
                    receivable_amount,
                    price,
                    status
                FROM stellar_orders 
                WHERE status IN ('active', 'partially_filled')
                ORDER BY created_at DESC
                LIMIT 10
            `);
            
            if (orders && orders.length > 0) {
                console.log(`✅ Found ${orders.length} active orders:`);
                console.log('\n' + '-'.repeat(100));
                console.log('Order ID    | Type | Selling | Buying | Amount     | Transfer   | Receivable | Price | Status');
                console.log('-'.repeat(100));
                
                orders.forEach((order: any) => {
                    const orderId = order.order_id.substring(0, 8);
                    const type = order.order_type.padEnd(4);
                    const selling = order.selling_asset.padEnd(7);
                    const buying = order.buying_asset.padEnd(7);
                    const amount = parseFloat(order.amount).toFixed(2).padStart(10);
                    const transfer = parseFloat(order.transfer_amount || 0).toFixed(2).padStart(10);
                    const receivable = parseFloat(order.receivable_amount || 0).toFixed(2).padStart(10);
                    const price = parseFloat(order.price).toFixed(2).padStart(8);
                    const status = order.status.padEnd(6);
                    
                    console.log(`${orderId} | ${type} | ${selling} | ${buying} | ${amount} | ${transfer} | ${receivable} | ${price} | ${status}`);
                });
                
                console.log('-'.repeat(100));
                
                // Analyze the data
                console.log('\n3. Data Analysis:');
                
                const sellOrders = orders.filter((o: any) => o.order_type === 'sell');
                const buyOrders = orders.filter((o: any) => o.order_type === 'buy');
                
                console.log(`   - Sell orders: ${sellOrders.length}`);
                console.log(`   - Buy orders: ${buyOrders.length}`);
                
                // Check for amount consistency
                let inconsistentOrders = 0;
                orders.forEach((order: any) => {
                    if (order.amount !== order.transfer_amount) {
                        inconsistentOrders++;
                    }
                });
                
                if (inconsistentOrders === 0) {
                    console.log('   ✅ All orders have consistent amount = transfer_amount');
                } else {
                    console.log(`   ⚠️  ${inconsistentOrders} orders have inconsistent amount vs transfer_amount`);
                }
                
                // Check receivable_amount
                const ordersWithoutReceivable = orders.filter((o: any) => !o.receivable_amount);
                if (ordersWithoutReceivable.length === 0) {
                    console.log('   ✅ All orders have receivable_amount populated');
                } else {
                    console.log(`   ⚠️  ${ordersWithoutReceivable.length} orders missing receivable_amount`);
                }
                
            } else {
                console.log('📊 No active orders found in database');
            }
            
        } catch (error: any) {
            console.log('❌ Error querying orders:', error.message);
        }
        
        console.log('\n4. Testing Order Book API...');
        try {
            const orderBook = await stellarTrading.getOrderBook('USDT', 'UGX', 'TEST');
            
            if (orderBook.status === 200) {
                console.log('✅ Order book API working');
                console.log(`   - Buy orders: ${orderBook.data.buyOrders.length}`);
                console.log(`   - Sell orders: ${orderBook.data.sellOrders.length}`);
                
                if (orderBook.data.buyOrders.length > 0) {
                    const sampleBuy = orderBook.data.buyOrders[0];
                    console.log(`   - Sample buy: ${sampleBuy.amount} ${sampleBuy.selling_asset} for ${sampleBuy.receivable_amount} ${sampleBuy.buying_asset} @ ${sampleBuy.price}`);
                }
                
                if (orderBook.data.sellOrders.length > 0) {
                    const sampleSell = orderBook.data.sellOrders[0];
                    console.log(`   - Sample sell: ${sampleSell.amount} ${sampleSell.selling_asset} for ${sampleSell.receivable_amount} ${sampleSell.buying_asset} @ ${sampleSell.price}`);
                }
            } else {
                console.log('❌ Order book API failed:', orderBook.message);
            }
        } catch (error: any) {
            console.log('❌ Error testing order book:', error.message);
        }
        
        console.log('\n🎉 Database verification completed!');
        
    } catch (error: any) {
        console.error('❌ Verification failed:', error);
    }
}

// Run the verification
verifyDatabaseSchema().then(() => {
    console.log('\n✅ Verification script finished');
    process.exit(0);
}).catch((error) => {
    console.error('\n❌ Verification script failed:', error);
    process.exit(1);
});
