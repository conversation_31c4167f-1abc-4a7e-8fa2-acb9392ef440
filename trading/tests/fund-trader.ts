import StellarTrading from '../src/models/StellarTrading';

async function fundTrader() {
    try {
        const stellarTrading = new StellarTrading();
        
        console.log('💰 Funding TRADER account with USDT from client ********\n');
        
        const TRADER_PUBLIC = process.env.TRADING_ACCOUNT_PUBLIC || 'GC7FRADCC6H2VEWXNIW75B6TJIB7UK5OP2OJ2OOZJUHS57FTBSYV4AKL';
        const USDT_ISSUER = process.env.STELLAR_PAYOUT_ISSUER_PUBLIC || 'GBYZLQHHGZ6NAE4WZRCFXFINMS2SIC2GI7QDQA3AZ4JVBWEFGW7XVPY6';
        
        console.log('From: Client ********');
        console.log('To: TRADER', TRADER_PUBLIC);
        console.log('Asset: USDT');
        console.log('Issuer:', USDT_ISSUER);
        console.log('Amount: 500 USDT\n');
        
        // Transfer USDT from client ******** to TRADER
        const result = await stellarTrading.makeWalletTransaction({
            debitClientId: '********',
            creditClientId: 'TRADER',
            amount: '500',
            assetCode: 'USDT',
            assetIssuer: USDT_ISSUER,
            productId: 'TRADER_FUNDING',
            memo: 'Fund TRADER with USDT for testing',
            transType: 'TRANSFER',
            serviceName: 'TRADING_SETUP'
        });
        
        console.log('\n✅ Result:');
        console.log('Response:', result.response === 1 ? 'SUCCESS' : 'FAILED');
        console.log('Message:', result.message);
        
        if (result.response === 1) {
            console.log('\n🎉 TRADER account now has USDT!');
            console.log('Hash:', result.stellarHash);
            console.log('\nYou can now run: npx ts-node tests/test-rail-order.ts');
            process.exit(0);
        } else {
            console.log('\n❌ Failed to fund TRADER');
            process.exit(1);
        }

    } catch (error: any) {
        console.error('\n❌ Error:', error.message);
        console.error('Full error:', error);
        process.exit(1);
    }
}

fundTrader();

