import StellarTrading from '../src/models/StellarTrading';

async function fixOrderStatus() {
    try {
        const stellarTrading = new StellarTrading();
        
        console.log('🔧 Fixing database schema...\n');
        
        // 1. Fix stellar_orders status enum
        console.log('1. Adding "partially_filled" to stellar_orders status enum...');
        await stellarTrading.callRawQuery(`
            ALTER TABLE stellar_orders 
            MODIFY COLUMN status ENUM('pending', 'active', 'partially_filled', 'filled', 'cancelled') DEFAULT 'pending'
        `);
        console.log('✅ stellar_orders.status updated');
        
        // 2. Add missing columns to quote_requests
        console.log('\n2. Checking quote_requests table...');
        const columns: any = await stellarTrading.callRawQuery(`DESCRIBE quote_requests`);
        const hasOrderId = columns.some((c: any) => c.Field === 'order_id');
        
        if (!hasOrderId) {
            console.log('Adding order_id column...');
            await stellarTrading.callRawQuery(`
                ALTER TABLE quote_requests 
                ADD COLUMN order_id VARCHAR(100) NULL COMMENT 'Trading order ID when executed' AFTER status
            `);
            console.log('✅ order_id column added');
        } else {
            console.log('✅ order_id column already exists');
        }
        
        // 3. Check and add stellar_hash column
        const hasStellarHash = columns.some((c: any) => c.Field === 'stellar_hash');
        if (!hasStellarHash) {
            console.log('Adding stellar_hash column...');
            await stellarTrading.callRawQuery(`
                ALTER TABLE quote_requests 
                ADD COLUMN stellar_hash VARCHAR(100) NULL COMMENT 'Stellar transaction hash' AFTER order_id
            `);
            console.log('✅ stellar_hash column added');
        } else {
            console.log('✅ stellar_hash column already exists');
        }
        
        console.log('\n✅ All schema fixes complete!');
        process.exit(0);

    } catch (error: any) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
}

fixOrderStatus();

