import * as dotenv from 'dotenv';
dotenv.config();

import StellarSDEXTrading from '../src/models/StellarSDEXTrading';

async function runTests() {
    try {
        const tradingService = new StellarSDEXTrading();
        
        // Step 1: Create pending rail order
        console.log('\n🧪 Step 1: Creating Pending Rail Order...\n');
        
        const pendingOrder = await tradingService.createPendingRailOrder({
            clientId: '********',
            referenceId: `REF_${Date.now()}`,
            sourceAsset: 'USDT',
            destinationAsset: 'UGX',
            amount: '100',
            expectedAmount: '340000',
            slippage: 1,
            payoutData: {
                amount: '340000',
                currency: 'UGX',
                mobileNumber: '+256700000000',
                provider: 'MTN'
            }
        });
        
        console.log('✅ Pending Rail Order Created:', JSON.stringify(pendingOrder, null, 2));
        
        if (pendingOrder.status !== 200) {
            console.log('❌ Failed to create pending order, stopping test');
            return;
        }
        
        // Step 2: Execute market swap and payout using data from step 1
        console.log('\n🧪 Step 2: Executing Market Swap and Payout...\n');
        console.log('📝 Using Reference ID:', pendingOrder.data.referenceId);
        console.log('📍 Deposit Address:', pendingOrder.data.address);
        
        const webhookData = {
            type: 'deposit_confirmed',
            statusCode: 200,
            message: 'Deposit confirmed',
            client_id: '********',
            trans_type: 'DEPOSIT',
            timestamp: new Date().toISOString(),
            reference_id: pendingOrder.data.referenceId,
            status: 'SUCCESS',
            amount: '100',
            fee: '0',
            currency: 'USDT',
            sender_account: '0x123...',
            receiver_account: pendingOrder.data.address,
            transaction_id: `TX_${Date.now()}`,
            meta: '{}'
        };
        
        const swapResult = await tradingService.executeMarketSwapAndPayout(webhookData);
        console.log('✅ Market Swap and Payout Result:', JSON.stringify(swapResult, null, 2));
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        // Exit cleanly to prevent hanging
        setTimeout(() => process.exit(0), 1000);
    }
}

runTests();

