"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var StellarSDEXTrading_1 = require("../src/models/StellarSDEXTrading");
function runTests() {
    return __awaiter(this, void 0, void 0, function () {
        var tradingService, pendingOrder, webhookData, swapResult, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 3, 4, 5]);
                    tradingService = new StellarSDEXTrading_1.default();
                    // Step 1: Create pending rail order
                    console.log('\n🧪 Step 1: Creating Pending Rail Order...\n');
                    return [4 /*yield*/, tradingService.createPendingRailOrder({
                            clientId: '********',
                            referenceId: "REF_".concat(Date.now()),
                            sourceAsset: 'USDT',
                            destinationAsset: 'UGX',
                            amount: '100',
                            expectedAmount: '340000',
                            slippage: 1,
                            payoutData: {
                                amount: '340000',
                                currency: 'UGX',
                                mobileNumber: '+256700000000',
                                provider: 'MTN'
                            }
                        })];
                case 1:
                    pendingOrder = _a.sent();
                    console.log('✅ Pending Rail Order Created:', JSON.stringify(pendingOrder, null, 2));
                    if (pendingOrder.status !== 200) {
                        console.log('❌ Failed to create pending order, stopping test');
                        return [2 /*return*/];
                    }
                    // Step 2: Execute market swap and payout using data from step 1
                    console.log('\n🧪 Step 2: Executing Market Swap and Payout...\n');
                    console.log('📝 Using Reference ID:', pendingOrder.data.referenceId);
                    console.log('📍 Deposit Address:', pendingOrder.data.address);
                    webhookData = {
                        type: 'deposit_confirmed',
                        statusCode: 200,
                        message: 'Deposit confirmed',
                        client_id: '********',
                        trans_type: 'DEPOSIT',
                        timestamp: new Date().toISOString(),
                        reference_id: pendingOrder.data.referenceId,
                        status: 'SUCCESS',
                        amount: '100',
                        fee: '0',
                        currency: 'USDT',
                        sender_account: '0x123...',
                        receiver_account: pendingOrder.data.address,
                        transaction_id: "TX_".concat(Date.now()),
                        meta: '{}'
                    };
                    return [4 /*yield*/, tradingService.executeMarketSwapAndPayout(webhookData)];
                case 2:
                    swapResult = _a.sent();
                    console.log('✅ Market Swap and Payout Result:', JSON.stringify(swapResult, null, 2));
                    return [3 /*break*/, 5];
                case 3:
                    error_1 = _a.sent();
                    console.error('❌ Test failed:', error_1);
                    return [3 /*break*/, 5];
                case 4:
                    // Exit cleanly to prevent hanging
                    setTimeout(function () { return process.exit(0); }, 1000);
                    return [7 /*endfinally*/];
                case 5: return [2 /*return*/];
            }
        });
    });
}
runTests();
