import axios from 'axios';

const BASE_URL = process.env.TRADING_API_URL || 'http://127.0.0.1:8039';

interface TestResult {
    endpoint: string;
    method: string;
    status: 'PASS' | 'FAIL';
    statusCode?: number;
    message?: string;
    data?: any;
}

async function testOrderbookAll() {
    console.log('🚀 Testing /trading/orderbook/all/all Endpoint');
    console.log('=====================================');
    console.log(`Base URL: ${BASE_URL}\n`);

    try {
        // Test 1: GET /trading/orderbook/all/all
        console.log('\n🧪 Test 1: GET /trading/orderbook/all/all');
        console.log('   Expected: Should default to USDT/UGX orderbook');
        
        const response = await axios({
            method: 'GET',
            url: `${BASE_URL}/trading/orderbook/all/all`,
            headers: {
                'Content-Type': 'application/json'
            },
            validateStatus: () => true // Don't throw on any status code
        });

        console.log(`\n   Status Code: ${response.status}`);
        console.log(`   Message: ${response.data?.message || 'N/A'}`);
        
        // Check if response is successful
        if (response.status >= 200 && response.status < 300) {
            console.log('   ✅ Request successful\n');
            
            // Validate response structure
            const { data } = response.data;
            
            if (data && Array.isArray(data)) {
                console.log(`   📊 Orderbook Data:`);
                console.log(`      Total orders: ${data.length}`);
                
                // Separate buy and sell orders
                const buyOrders = data.filter((order: any) => order.order_type === 'buy');
                const sellOrders = data.filter((order: any) => order.order_type === 'sell');
                
                console.log(`      Buy orders: ${buyOrders.length}`);
                console.log(`      Sell orders: ${sellOrders.length}`);
                
                // Display sample orders if available
                if (buyOrders.length > 0) {
                    console.log(`\n   📈 Sample Buy Order:`);
                    const sampleBuy = buyOrders[0];
                    console.log(`      Base Asset: ${sampleBuy.base_asset}`);
                    console.log(`      Counter Asset: ${sampleBuy.counter_asset}`);
                    console.log(`      Amount: ${sampleBuy.amount}`);
                    console.log(`      Price: ${sampleBuy.price}`);
                }
                
                if (sellOrders.length > 0) {
                    console.log(`\n   📉 Sample Sell Order:`);
                    const sampleSell = sellOrders[0];
                    console.log(`      Base Asset: ${sampleSell.base_asset}`);
                    console.log(`      Counter Asset: ${sampleSell.counter_asset}`);
                    console.log(`      Amount: ${sampleSell.amount}`);
                    console.log(`      Price: ${sampleSell.price}`);
                }
                
                // Verify that orders are for USDT/UGX
                const allOrdersCorrectPair = data.every((order: any) => 
                    (order.base_asset === 'USDT' && order.counter_asset === 'UGX') ||
                    (order.base_asset === 'UGX' && order.counter_asset === 'USDT')
                );
                
                if (allOrdersCorrectPair || data.length === 0) {
                    console.log('\n   ✅ All orders are for USDT/UGX trading pair (or orderbook is empty)');
                    console.log('\n   🎉 TEST PASSED: /trading/orderbook/all/all correctly defaults to USDT/UGX');
                    return { success: true, data };
                } else {
                    console.log('\n   ❌ Some orders are not for USDT/UGX pair');
                    console.log('\n   ⚠️  TEST FAILED: Orders do not match expected trading pair');
                    return { success: false, message: 'Orders do not match expected USDT/UGX pair' };
                }
                
            } else {
                console.log('   ⚠️  Response data is empty or not an array');
                console.log('   Response:', JSON.stringify(response.data, null, 2));
                return { success: false, message: 'Invalid response format' };
            }
            
        } else {
            console.log(`   ❌ Request failed with status ${response.status}`);
            console.log('   Response:', JSON.stringify(response.data, null, 2));
            return { success: false, statusCode: response.status, message: response.data?.message };
        }
        
    } catch (error: any) {
        console.error('\n   ❌ Error:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.error('\n   💡 Hint: Make sure the trading service is running on', BASE_URL);
        }
        return { success: false, message: error.message };
    }
}

// Additional test: Compare with explicit USDT/UGX call
async function compareWithExplicitCall() {
    console.log('\n\n🔄 Test 2: Comparing /all/all with explicit /USDT/UGX');
    console.log('=====================================\n');
    
    try {
        const [allResponse, explicitResponse] = await Promise.all([
            axios.get(`${BASE_URL}/trading/orderbook/all/all`, {
                validateStatus: () => true
            }),
            axios.get(`${BASE_URL}/trading/orderbook/USDT/UGX`, {
                validateStatus: () => true
            })
        ]);
        
        console.log('   Response from /all/all:', allResponse.status);
        console.log('   Response from /USDT/UGX:', explicitResponse.status);
        
        if (allResponse.status === explicitResponse.status && 
            allResponse.status >= 200 && allResponse.status < 300) {
            
            const allData = allResponse.data?.data || [];
            const explicitData = explicitResponse.data?.data || [];
            
            console.log(`\n   Orders from /all/all: ${allData.length}`);
            console.log(`   Orders from /USDT/UGX: ${explicitData.length}`);
            
            if (allData.length === explicitData.length) {
                console.log('\n   ✅ Both endpoints return the same number of orders');
                console.log('   🎉 TEST PASSED: /all/all behaves identically to /USDT/UGX');
                return { success: true };
            } else {
                console.log('\n   ⚠️  Order counts differ between endpoints');
                return { success: false, message: 'Order counts differ' };
            }
        } else {
            console.log('\n   ⚠️  One or both requests failed');
            return { success: false, message: 'Request(s) failed' };
        }
        
    } catch (error: any) {
        console.error('\n   ❌ Error:', error.message);
        return { success: false, message: error.message };
    }
}

// Run all tests
async function runAllTests() {
    console.log('\n' + '='.repeat(80));
    console.log('🧪 ORDERBOOK /all/all TEST SUITE');
    console.log('='.repeat(80) + '\n');
    
    const results = [];
    
    // Test 1: Basic functionality
    const test1 = await testOrderbookAll();
    results.push({ name: 'Orderbook /all/all defaults to USDT/UGX', ...test1 });
    
    // Test 2: Compare with explicit call
    const test2 = await compareWithExplicitCall();
    results.push({ name: 'Compare /all/all with /USDT/UGX', ...test2 });
    
    // Summary
    console.log('\n\n' + '='.repeat(80));
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('='.repeat(80));
    
    const passed = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log(`\n✅ PASSED: ${passed}/${results.length}`);
    console.log(`❌ FAILED: ${failed}/${results.length}\n`);
    
    results.forEach((result, idx) => {
        const icon = result.success ? '✅' : '❌';
        console.log(`${idx + 1}. ${icon} ${result.name}`);
        if (result.message) {
            console.log(`   Message: ${result.message}`);
        }
    });
    
    console.log('\n' + '='.repeat(80));
    
    if (failed === 0) {
        console.log('\n🎉 ALL TESTS PASSED!\n');
        process.exit(0);
    } else {
        console.log(`\n⚠️  ${failed} test(s) failed.\n`);
        process.exit(1);
    }
}

// Execute test suite
runAllTests().catch((error) => {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
});

