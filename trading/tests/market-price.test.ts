import StellarSDEXTrading from '../src/models/StellarSDEXTrading';

async function testGetMarketPrice() {
    const tradingService = new StellarSDEXTrading();

    console.log('\n🧪 Testing getMarketPrice...\n');

    // Test 1: Sell USDT for UGX
    const sellResult = await tradingService.getMarketPrice(
        'USDT',
        'UGX',
        '100000',
        'sell',
        1.0
    );

    console.log('💰 SELL 100 USDT for UGX:', JSON.stringify(sellResult, null, 2));

    // Test 2: Buy USDT with UGX
    const buyResult = await tradingService.getMarketPrice(
        'USDT',
        'UGX',
        '50000',
        'buy',
        0.5
    );

    console.log('\n💰 BUY 50 USDT with UGX:', JSON.stringify(buyResult, null, 2));
}

testGetMarketPrice();

