import StellarSDEXTrading from './src/models/StellarSDEXTrading';

async function testMarketOrder() {
    console.log('🧪 Testing Market Order Flow\n');
    
    const trading = new StellarSDEXTrading();
    
    // Test data - you'll need to replace with real values
    const testData = {
        clientId: 'YOUR_CLIENT_ID', // Replace with actual client ID
        baseAsset: 'USDT',
        counterAsset: 'UGX',
        amount: '1',
        price: '3700', // Market price from frontend
        orderType: 'buy' as 'buy' | 'sell',
        type: 'market' as 'market' | 'limit',
        slippage: 0.5 // 0.5% slippage
    };
    
    console.log('📝 Test Order Data:');
    console.log(JSON.stringify(testData, null, 2));
    console.log('\n');
    
    try {
        console.log('🚀 Creating market order...\n');
        const result = await trading.createOffer(testData);
        
        console.log('✅ Result:');
        console.log(JSON.stringify(result, null, 2));
        
        if (result.status === 200) {
            console.log('\n✅ Market order executed successfully!');
            console.log('Hash:', result.data?.stellarHash);
        } else {
            console.log('\n❌ Market order failed:');
            console.log('Status:', result.status);
            console.log('Message:', result.message);
        }
        
    } catch (error: any) {
        console.error('\n❌ Error during test:');
        console.error(error.message);
        console.error(error.stack);
    }
    
    console.log('\n🏁 Test completed');
    process.exit(0);
}

// Run the test
testMarketOrder().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});

