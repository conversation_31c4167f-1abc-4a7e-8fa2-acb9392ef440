import StellarTrading from './src/models/StellarTrading';

async function migrateDatabase() {
    try {
        const stellarTrading = new StellarTrading();
        
        console.log('🔧 Running Database Migration\n');
        
        // Check if receivable_amount column exists
        console.log('1. Checking current schema...');
        const columns = await stellarTrading.callRawQuery(`DESCRIBE stellar_orders`);
        const hasReceivableAmount = columns.some((col: any) => col.Field === 'receivable_amount');
        
        if (hasReceivableAmount) {
            console.log('✅ receivable_amount column already exists');
        } else {
            console.log('📝 Adding receivable_amount column...');
            
            // Add the column
            await stellarTrading.callRawQuery(`
                ALTER TABLE stellar_orders 
                ADD COLUMN receivable_amount DECIMAL(20,8) NULL COMMENT 'Amount of buying_asset that user expects to receive' 
                AFTER transfer_amount
            `);
            
            console.log('✅ Column added successfully');
        }
        
        // Update existing records
        console.log('\n2. Updating existing records...');
        
        // CORRECTED: amount is always the base asset amount
        // receivable_amount should be the counter asset amount (amount * price)
        const updateResult = await stellarTrading.callRawQuery(`
            UPDATE stellar_orders 
            SET receivable_amount = ROUND(amount * price, 8)
            WHERE receivable_amount IS NULL
        `);
        
        console.log(`✅ Updated ${updateResult.affectedRows || 0} orders`);
        
        // Make the column NOT NULL
        console.log('\n3. Making column NOT NULL...');
        await stellarTrading.callRawQuery(`
            ALTER TABLE stellar_orders 
            MODIFY COLUMN receivable_amount DECIMAL(20,8) NOT NULL COMMENT 'Amount of buying_asset that user expects to receive'
        `);
        
        console.log('✅ Column is now NOT NULL');
        
        // Verify the migration
        console.log('\n4. Verifying migration...');
        const sampleOrders = await stellarTrading.callRawQuery(`
            SELECT 
                order_id,
                order_type,
                selling_asset,
                buying_asset,
                amount,
                receivable_amount,
                price
            FROM stellar_orders 
            LIMIT 5
        `);
        
        if (sampleOrders && sampleOrders.length > 0) {
            console.log('✅ Sample orders after migration:');
            console.log('\n' + '-'.repeat(80));
            console.log('Order ID    | Type | Selling | Buying | Amount     | Receivable | Price');
            console.log('-'.repeat(80));
            
            sampleOrders.forEach((order: any) => {
                const orderId = order.order_id.substring(0, 8);
                const type = order.order_type.padEnd(4);
                const selling = order.selling_asset.padEnd(7);
                const buying = order.buying_asset.padEnd(7);
                const amount = parseFloat(order.amount).toFixed(2).padStart(10);
                const receivable = parseFloat(order.receivable_amount || 0).toFixed(2).padStart(10);
                const price = parseFloat(order.price).toFixed(2).padStart(8);
                
                console.log(`${orderId} | ${type} | ${selling} | ${buying} | ${amount} | ${receivable} | ${price}`);
            });
            
            console.log('-'.repeat(80));
        }
        
        console.log('\n🎉 Database migration completed successfully!');
        console.log('\nNext steps:');
        console.log('1. Run the tests: npx ts-node run-tests.ts');
        console.log('2. Start your trading service');
        console.log('3. Test order creation and market order execution');
        
    } catch (error: any) {
        console.error('❌ Migration failed:', error);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Run the migration
migrateDatabase().then(() => {
    console.log('\n✅ Migration script finished');
    process.exit(0);
}).catch((error) => {
    console.error('\n❌ Migration script failed:', error);
    process.exit(1);
});
