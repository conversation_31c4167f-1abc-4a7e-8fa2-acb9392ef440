# Postman Testing Guide - LR Provider

## Import Collection

1. Open Postman
2. Click **Import** button
3. Select `LR-Provider-Postman.json`
4. Collection will appear in your sidebar

## Quick Test Flow

### 1️⃣ Generate Quote
**Request:**
```
POST http://localhost:8039/trading/generate-lr-quote
```
```json
{
  "amount": 100,
  "currency": "UGX",
  "asset_code": "USDT",
  "chain": "BSC",
  "provider_id": 10
}
```

**Response:**
```json
{
  "status": 200,
  "message": "Quote generated successfully",
  "data": {
    "quote_id": "QUOTE_1761896051518_a3ufa17co",
    "rate": "3800",
    "total_amount": "376020",
    "fee": "3980",
    "expires_at": "2025-10-31T07:39:11.518Z",
    "from_currency": "USDT",
    "to_currency": "UGX",
    "from_amount": "100",
    "to_amount": "380000",
    "chain": "BSC"
  }
}
```

### 2️⃣ Confirm Quote
**Request:**
```
POST http://localhost:8039/trading/confirm-lr-quote
```
```json
{
  "reference_id": "REF_1761896051532",
  "amount": 100,
  "from_currency": "USDT",
  "to_currency": "UGX",
  "chain": "BSC",
  "transaction_type": "off_ramp",
  "slippage": 0.5,
  "payment_method": {
    "type": "mobile_money",
    "currency": "UGX",
    "phone_number": "+************",
    "country_code": "UG",
    "network": "MTN",
    "account_name": "Test User"
  },
  "clientId": "TEST_CLIENT",
  "expected_amount": "380000"
}
```

**Response:**
```json
{
  "status": 200,
  "message": "Pending rail order created successfully",
  "data": {
    "request_id": "REQ_1234567890",
    "reference_id": "REF_1761896051532",
    "deposit_address": "0x742d35Cc6634C0532925a3b844Bc9e7595f0bEb",
    "chain": "BSC",
    "asset": "USDT",
    "amount": "100",
    "status": "pending_deposit"
  }
}
```

### 3️⃣ Get Transaction
**Request:**
```
GET http://localhost:8039/trading/get-lr-transaction/REF_1761896051532
```

**Response:**
```json
{
  "status": 200,
  "message": "Transaction found",
  "data": {
    "request_id": "REQ_1234567890",
    "reference_id": "REF_1761896051532",
    "status": "pending_deposit",
    "source_asset": "USDT",
    "destination_asset": "UGX",
    "amount": "100",
    "created_at": "2025-10-31T07:34:11.518Z"
  }
}
```

### 4️⃣ Refresh Quote (Optional)
Get updated rates for an existing quote.

### 5️⃣ Send Webhook Event (Testing Only)
Simulate webhook events from external systems.

## Tips

- **Reference IDs**: Use format `REF_{{$timestamp}}` for unique IDs
- **Variables**: Postman auto-generates `{{$timestamp}}`
- **Save Response**: Copy `quote_id` and `reference_id` for next requests
- **Chain**: Use `BSC`, `TRON`, or `STELLAR`
- **Transaction Type**: `off_ramp` or `on_ramp`

## Common Issues

### "connect ECONNREFUSED"
→ Trading service not running. Start with: `npm start`

### "Transaction not found"
→ Use correct `reference_id` from confirm quote response

### "Rate not found"
→ Check that USDT/UGX pair exists in database

## Environment Setup

Make sure trading service is running:
```bash
cd aggregator/backend/trading
npm start
```

Service should be available at: `http://localhost:8039`

