-- Safe migration that checks if columns exist before adding them
-- Run this if you're not sure whether columns already exist

-- Check and add buy_rate
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'exchange_rates' 
    AND COLUMN_NAME = 'buy_rate'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE exchange_rates ADD COLUMN buy_rate DECIMAL(20, 7) NULL COMMENT "Live buy rate from Stellar"',
    'SELECT "Column buy_rate already exists" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add sell_rate
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'exchange_rates' 
    AND COLUMN_NAME = 'sell_rate'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE exchange_rates ADD COLUMN sell_rate DECIMAL(20, 7) NULL COMMENT "Live sell rate from Stellar"',
    'SELECT "Column sell_rate already exists" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add last_price_sync
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'exchange_rates' 
    AND COLUMN_NAME = 'last_price_sync'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE exchange_rates ADD COLUMN last_price_sync TIMESTAMP NULL COMMENT "When price was last synced from Stellar"',
    'SELECT "Column last_price_sync already exists" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add index
SET @index_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_NAME = 'exchange_rates' 
    AND INDEX_NAME = 'idx_last_price_sync'
);

SET @sql = IF(@index_exists = 0,
    'CREATE INDEX idx_last_price_sync ON exchange_rates(last_price_sync)',
    'SELECT "Index idx_last_price_sync already exists" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT 'Migration completed successfully' AS status;

