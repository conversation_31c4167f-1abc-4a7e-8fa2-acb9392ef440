-- Add live rate columns to exchange_rates table
-- Run this migration if you want to manually add the columns

-- Add buy_rate column
ALTER TABLE exchange_rates 
ADD COLUMN buy_rate DECIMAL(20, 7) NULL COMMENT 'Live buy rate from Stellar';

-- Add sell_rate column
ALTER TABLE exchange_rates 
ADD COLUMN sell_rate DECIMAL(20, 7) NULL COMMENT 'Live sell rate from Stellar';

-- Add last_price_sync column
ALTER TABLE exchange_rates 
ADD COLUMN last_price_sync TIMESTAMP NULL COMMENT 'When price was last synced from Stellar';

-- Add index for faster queries on last_price_sync
CREATE INDEX idx_last_price_sync ON exchange_rates(last_price_sync);

SELECT 'Migration completed: Added buy_rate, sell_rate, and last_price_sync columns' AS status;

