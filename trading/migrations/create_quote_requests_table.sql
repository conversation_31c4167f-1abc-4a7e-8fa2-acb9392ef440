-- Create quote_requests table for pending rail orders
CREATE TABLE IF NOT EXISTS `quote_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `request_id` varchar(100) NOT NULL COMMENT 'Unique request identifier',
  `client_id` varchar(50) NOT NULL COMMENT 'Client who made the request',
  `reference_id` varchar(100) NOT NULL COMMENT 'Reference ID from LR admin',
  `source_asset` varchar(20) NOT NULL COMMENT 'Source asset (e.g., USDT)',
  `destination_asset` varchar(20) NOT NULL COMMENT 'Destination asset (e.g., UGX)',
  `amount` decimal(20,8) NOT NULL COMMENT 'Amount to swap',
  `expected_amount` decimal(20,8) NULL COMMENT 'Expected destination amount',
  `slippage` decimal(5,2) DEFAULT 1.00 COMMENT 'Slippage tolerance (%)',
  `status` enum('pending', 'executing', 'completed', 'failed', 'expired') DEFAULT 'pending' COMMENT 'Request status',
  `order_id` varchar(100) NULL COMMENT 'Trading order ID when executed',
  `stellar_hash` varchar(100) NULL COMMENT 'Stellar transaction hash',
  `payout_data` JSON NULL COMMENT 'Payout details (mobile, amount, etc.)',
  `expires_at` timestamp NULL COMMENT 'Request expiration time',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `request_id_unique` (`request_id`),
  UNIQUE KEY `reference_id_unique` (`reference_id`),
  KEY `idx_client_id` (`client_id`),
  KEY `idx_status` (`status`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Pending rail orders waiting for deposit confirmation';
