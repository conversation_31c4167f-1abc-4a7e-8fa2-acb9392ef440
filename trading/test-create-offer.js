require('dotenv').config();

// Import directly using the compiled approach
const StellarService = require('./src/helpers/StellarService.ts').default;

const stellarService = new StellarService();

const testData = {
    senderSecretKey: 'SBIEU7X4QPGT3AUQNNC6S5NJBZHZQBRA7E6HXQMZ4JDXBRXDEW2VXQV4',
    baseAsset: 'USDT',
    counterAsset: 'UGX',
    amount: '1',
    price: '3050.000000',
    orderType: 'buy'
};

console.log('🧪 Testing createLimitOffer with:');
console.log(JSON.stringify(testData, null, 2));
console.log('\n' + '='.repeat(80));

stellarService.createLimitOffer(testData)
    .then(result => {
        console.log('\n' + '='.repeat(80));
        console.log('✅ SUCCESS!');
        console.log('Result:', JSON.stringify(result, null, 2));
    })
    .catch(error => {
        console.log('\n' + '='.repeat(80));
        console.error('❌ FAILED!');
        console.error('Error:', error.message);
        if (error.response?.data) {
            console.error('Response:', JSON.stringify(error.response.data, null, 2));
        }
    });

