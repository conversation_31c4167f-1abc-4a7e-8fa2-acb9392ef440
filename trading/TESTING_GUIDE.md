# Trading System Testing Guide

This guide will help you test the trading system to ensure all fixes are working correctly.

## 🚀 Quick Start

### 1. Run Database Migration (if needed)
```bash
npx ts-node migrate-database.ts
```

### 2. Run All Tests
```bash
npx ts-node run-tests.ts
```

### 3. Run Individual Tests
```bash
# Database verification
npx ts-node tests/verify-database-schema.ts

# Order creation and amount logic
npx ts-node tests/test-order-amounts.ts

# Market order execution
npx ts-node tests/test-market-order-logic.ts
```

## 📋 What Each Test Does

### 1. Database Schema Verification
- ✅ Checks if `receivable_amount` column exists
- ✅ Verifies current order data structure
- ✅ Tests order book API
- ✅ Analyzes amount consistency

### 2. Order Amount Logic Test
- ✅ Creates SELL orders and verifies amounts
- ✅ Creates BUY orders and verifies amounts
- ✅ Tests order book display
- ✅ Tests market order execution
- ✅ Tests manual order matching

### 3. Market Order Execution Test
- ✅ Creates a sell order to match against
- ✅ Creates a market buy order
- ✅ Verifies correct currency handling
- ✅ Checks mathematical accuracy
- ✅ Verifies order book updates

## 🎯 Expected Results

### Database Structure
After migration, your orders should look like this:

**SELL Orders (USDT → UGX):**
```
amount: 10.00000000 (USDT being sold)
receivable_amount: 34000.00000000 (UGX expected)
```

**BUY Orders (UGX → USDT):**
```
amount: 16500.00000000 (UGX being sold)
receivable_amount: 5.00000000 (USDT expected)
```

### Market Order Behavior
- **Market BUY**: Should use UGX to buy USDT
- **Market SELL**: Should use USDT to get UGX
- **Amounts**: Should be mathematically correct
- **Currencies**: Should transfer the right currencies

### Order Book Display
- Shows remaining amounts correctly
- Shows receivable amounts correctly
- Displays prices properly
- Groups buy/sell orders correctly

## 🔧 Troubleshooting

### Common Issues

1. **"receivable_amount column missing"**
   - Run: `npx ts-node migrate-database.ts`

2. **"No active orders found"**
   - Create some test orders first
   - Check if orders are in 'active' status

3. **"Market order execution failed"**
   - Ensure there are compatible orders to match against
   - Check slippage settings
   - Verify balance requirements

4. **"Amount mismatch"**
   - Check if database migration ran correctly
   - Verify order creation logic
   - Check price calculations

### Manual Testing

You can also test manually using the API:

```bash
# Create a sell order
curl -X POST http://localhost:3006/trading/orders \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "TEST_CLIENT",
    "baseAsset": "USDT",
    "counterAsset": "UGX", 
    "amount": "1",
    "orderType": "sell",
    "type": "limit",
    "price": "3400"
  }'

# Create a market buy order
curl -X POST http://localhost:3006/trading/orders \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "TEST_CLIENT_2",
    "baseAsset": "USDT",
    "counterAsset": "UGX",
    "amount": "1", 
    "orderType": "buy",
    "type": "market",
    "slippage": 1
  }'

# Trigger manual order matching
curl -X POST http://localhost:3006/trading/match-orders

# Get order book
curl http://localhost:3006/trading/orderbook/USDT/UGX
```

## 📊 Success Criteria

All tests should pass with these criteria:

- ✅ Database schema is correct
- ✅ Order amounts are stored properly
- ✅ Market orders use correct currencies
- ✅ Order book displays accurate information
- ✅ Mathematical calculations are correct
- ✅ Cron service can match orders
- ✅ Receivable amounts are calculated correctly

## 🎉 What's Fixed

1. **Amount Storage**: Orders now store amounts consistently
2. **Market Order Currency**: Market orders now use correct currencies
3. **Order Matching**: Cron service automatically matches compatible orders
4. **Database Clarity**: Added `receivable_amount` for better understanding
5. **Enhanced Logging**: Better debugging and monitoring capabilities

## 📞 Support

If you encounter issues:
1. Check the logs for detailed error messages
2. Verify database schema is correct
3. Ensure test client accounts exist
4. Check network connectivity and service status

The system should now work correctly for all trading scenarios!
