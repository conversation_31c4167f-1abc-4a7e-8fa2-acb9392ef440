import StellarSDEXTrading from './src/models/StellarSDEXTrading';

async function testCacheOffers() {
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('🧪 TESTING CACHE OFFERS FUNCTION');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    const stellarSDEXTrading = new StellarSDEXTrading();

    try {
        // Test with a public key that has offers
        const publicKey = 'GAYL7Q46BMBMNLCOCJA66A4IYGSC4AQNX472HHDUON4KPI6BJZU5UJBC';
        const clientId = 'test_client_123';
        
        console.log('💾 Testing cache offers...');
        console.log(`Public Key: ${publicKey}`);
        console.log(`Client ID: ${clientId}`);
        
        const result = await stellarSDEXTrading.cacheOffers(publicKey, clientId);
        
        console.log('✅ Cache offers result:', {
            status: result.status,
            message: result.message,
            data: result.data
        });

        if (result.data) {
            console.log('📊 Cached offers details:', {
                totalOffers: result.data.totalOffers,
                cachedCount: result.data.cachedCount,
                errors: result.data.errors
            });
        }

    } catch (error: any) {
        console.error('❌ Error testing cache offers:', error.message);
    }

    console.log('\n🏁 Test completed');
}

// Run the test
testCacheOffers();
