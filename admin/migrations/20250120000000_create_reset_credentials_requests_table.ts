import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('reset_credentials_requests');
  
  if (!tableExists) {
    // Create the table with all fields
    await knex.schema.createTable('reset_credentials_requests', (table) => {
      table.string('id', 60).primary(); // VARCHAR(60) for UUID
      table.string('user_id', 50).notNullable(); // VARCHAR(50) NOT NULL
      table.string('client_id', 50).notNullable(); // VARCHAR(50) NOT NULL
      table.enum('reset_type', ['PASSWORD', '2FA']).notNullable(); // ENUM('PASSWORD','2FA') NOT NULL
      table.enum('request_initiator', ['TEAM', 'CLIENT_ADMIN', 'SYSTEM_USER']).notNullable(); // ENUM('TEAM','CLIENT_ADMIN','SYSTEM_USER') NOT NULL
      table.enum('request_approver', ['CLIENT_ADMIN', 'SYSTEM_ADMIN']).nullable(); // ENUM('CLIENT_ADMIN','SYSTEM_ADMIN') DEFAULT NULL
      table.string('request_approver_id', 100).nullable(); // VARCHAR(100) DEFAULT NULL
      table.timestamp('expires_at').notNullable(); // TIMESTAMP NOT NULL
      table.boolean('used').notNullable().defaultTo(false); // TINYINT(1) NOT NULL DEFAULT '0'
      table.timestamp('created_at').defaultTo(knex.fn.now()); // TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable(); // TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('reset_credentials_requests', (table) => {
      table.index(['user_id']);
      table.index(['client_id']);
      table.index(['reset_type']);
      table.index(['request_initiator']);
      table.index(['request_approver']);
      table.index(['used']);
      table.index(['expires_at']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created reset_credentials_requests table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table reset_credentials_requests exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM reset_credentials_requests');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  const requiredFields = [
    { name: 'id', type: 'string', length: 60, nullable: false, primary: true },
    { name: 'user_id', type: 'string', length: 50, nullable: false },
    { name: 'client_id', type: 'string', length: 50, nullable: false },
    { name: 'reset_type', type: 'enum', values: ['PASSWORD', '2FA'], nullable: false },
    { name: 'request_initiator', type: 'enum', values: ['TEAM', 'CLIENT_ADMIN', 'SYSTEM_USER'], nullable: false },
    { name: 'request_approver', type: 'enum', values: ['CLIENT_ADMIN', 'SYSTEM_ADMIN'], nullable: true },
    { name: 'request_approver_id', type: 'string', length: 100, nullable: true },
    { name: 'expires_at', type: 'timestamp', nullable: false },
    { name: 'used', type: 'boolean', nullable: false, defaultValue: false },
    { name: 'created_at', type: 'timestamp', nullable: false, defaultValue: 'CURRENT_TIMESTAMP' },
    { name: 'updated_at', type: 'timestamp', nullable: false, defaultValue: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP' }
  ];

  for (const field of requiredFields) {
    if (!existingColumns.includes(field.name)) {
      console.log(`➕ Adding missing field: ${field.name}`);
      
      if (field.type === 'enum') {
        await knex.schema.alterTable('reset_credentials_requests', (table) => {
          const enumValues = (field as any).values as readonly string[];
          const col = table.enum(field.name, enumValues);
          if (field.nullable) {
            col.nullable();
          } else {
            col.notNullable();
          }
        });
      } else if (field.type === 'boolean') {
        await knex.schema.alterTable('reset_credentials_requests', (table) => {
          const col = table.boolean(field.name);
          if (field.nullable) {
            col.nullable();
          } else {
            col.notNullable();
          }
          if (field.defaultValue !== undefined) {
            col.defaultTo(field.defaultValue as any);
          }
        });
      } else if (field.type === 'timestamp') {
        await knex.schema.alterTable('reset_credentials_requests', (table) => {
          const col = table.timestamp(field.name);
          if (field.nullable) {
            col.nullable();
          } else {
            col.notNullable();
          }
          col.defaultTo(knex.fn.now());
        });
      } else if (field.type === 'string') {
        await knex.schema.alterTable('reset_credentials_requests', (table) => {
          if (field.primary) {
            table.string(field.name, field.length).primary();
          } else {
            const col = table.string(field.name, field.length);
            if (field.nullable) {
              col.nullable();
            } else {
              col.notNullable();
            }
          }
        });
      }
    }
  }

  // Check and add missing indexes
  const existingIndexes = await knex.raw('SHOW INDEX FROM reset_credentials_requests');
  const existingIndexNames = existingIndexes[0].map((idx: any) => idx.Key_name);
  
  const requiredIndexes = ['user_id', 'client_id', 'reset_type', 'request_initiator', 'request_approver', 'used', 'expires_at', 'created_at'];
  
  for (const indexField of requiredIndexes) {
    const indexName = `${indexField}_index`;
    if (!existingIndexNames.includes(indexName) && !existingIndexNames.includes(indexField)) {
      console.log(`➕ Adding missing index: ${indexField}`);
      await knex.schema.alterTable('reset_credentials_requests', (table) => {
        table.index([indexField]);
      });
    }
  }

  console.log('✅ Migration completed for reset_credentials_requests table');
}

export async function down(knex: Knex): Promise<void> {
  // Drop the table if it exists
  const tableExists = await knex.schema.hasTable('reset_credentials_requests');
  
  if (tableExists) {
    await knex.schema.dropTable('reset_credentials_requests');
    console.log('✅ Dropped reset_credentials_requests table');
  } else {
    console.log('ℹ️ Table reset_credentials_requests does not exist, nothing to drop');
  }
}
