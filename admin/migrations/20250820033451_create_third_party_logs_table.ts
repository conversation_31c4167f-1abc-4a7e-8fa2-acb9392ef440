import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('third_party_logs');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('third_party_logs', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('trans_id', 50).nullable();
      table.string('client_id', 50).nullable();
      table.text('request_data').nullable();
      table.text('response_data').nullable();
      table.string('log_type', 50).nullable();
      table.string('service_name', 100).nullable();
      table.string('error_message', 100).nullable();
      table.string('request_type', 50).nullable();
      table.string('timestamp', 40).nullable();
      table.timestamp('created_on').notNullable().defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.index(['trans_id']);
      table.index(['client_id']);
      table.index(['log_type']);
      table.index(['service_name']);
      table.index(['request_type']);
      table.index(['timestamp']);
      table.index(['created_on']);
    });
    
    console.log('✅ Created third_party_logs table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table third_party_logs exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM third_party_logs');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('trans_id')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.string('trans_id', 50).nullable();
    });
    console.log('✅ Added trans_id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.string('client_id', 50).nullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('request_data')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.text('request_data').nullable();
    });
    console.log('✅ Added request_data field');
  }
  
  if (!existingColumns.includes('response_data')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.text('response_data').nullable();
    });
    console.log('✅ Added response_data field');
  }
  
  if (!existingColumns.includes('log_type')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.string('log_type', 50).nullable();
    });
    console.log('✅ Added log_type field');
  }
  
  if (!existingColumns.includes('service_name')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.string('service_name', 100).nullable();
    });
    console.log('✅ Added service_name field');
  }
  
  if (!existingColumns.includes('error_message')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.string('error_message', 100).nullable();
    });
    console.log('✅ Added error_message field');
  }
  
  if (!existingColumns.includes('request_type')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.string('request_type', 50).nullable();
    });
    console.log('✅ Added request_type field');
  }
  
  if (!existingColumns.includes('timestamp')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.string('timestamp', 40).nullable();
    });
    console.log('✅ Added timestamp field');
  }
  
  if (!existingColumns.includes('created_on')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.timestamp('created_on').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_on field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM third_party_logs');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('third_party_logs_trans_id_index')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.index(['trans_id']);
    });
    console.log('✅ Added trans_id index');
  }
  
  if (!existingIndexes.includes('third_party_logs_client_id_index')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('third_party_logs_log_type_index')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.index(['log_type']);
    });
    console.log('✅ Added log_type index');
  }
  
  if (!existingIndexes.includes('third_party_logs_service_name_index')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.index(['service_name']);
    });
    console.log('✅ Added service_name index');
  }
  
  if (!existingIndexes.includes('third_party_logs_request_type_index')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.index(['request_type']);
    });
    console.log('✅ Added request_type index');
  }
  
  if (!existingIndexes.includes('third_party_logs_timestamp_index')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.index(['timestamp']);
    });
    console.log('✅ Added timestamp index');
  }
  
  if (!existingIndexes.includes('third_party_logs_created_on_index')) {
    await knex.schema.alterTable('third_party_logs', (table) => {
      table.index(['created_on']);
    });
    console.log('✅ Added created_on index');
  }
  
  console.log('✅ Field check complete for third_party_logs table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 