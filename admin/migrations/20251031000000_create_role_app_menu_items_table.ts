import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('role_app_menu_items');

  if (!tableExists) {
    await knex.schema.createTable('role_app_menu_items', (table) => {
      table.string('id', 60).primary().notNullable(); // CHAR(60) NOT NULL PRIMARY KEY
      table.string('role_id', 60).nullable(); // VARCHAR(60) DEFAULT NULL
      table.string('app_menu_item_id', 60).nullable(); // VARCHAR(60) DEFAULT NULL
      table.enum('status', ['active', 'inactive']).notNullable().defaultTo('active');
      table.timestamp('deleted_at').nullable();
      table.timestamp('updated_at').nullable();
      table.timestamp('created_at').nullable();
    });

    // Add foreign keys
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table
        .foreign('role_id')
        .references('id')
        .inTable('roles')
        .onDelete('CASCADE');
      table
        .foreign('app_menu_item_id')
        .references('id')
        .inTable('app_menu_items')
        .onDelete('CASCADE');
    });

    // Add indexes for better performance
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table.index(['role_id']);
      table.index(['app_menu_item_id']);
      table.index(['status']);
      table.index(['deleted_at']);
      table.index(['created_at']);
    });

    console.log('✅ Created role_app_menu_items table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table role_app_menu_items exists, checking for missing fields...');

  const columns = await knex.raw('SHOW COLUMNS FROM role_app_menu_items');
  const existingColumns = columns[0].map((col: any) => col.Field);

  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table.string('id', 60).primary().notNullable();
    });
    console.log('✅ Added id field');
  }

  if (!existingColumns.includes('role_id')) {
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table.string('role_id', 60).nullable();
    });
    console.log('✅ Added role_id field');

    // Try to add the foreign key constraint
    try {
      await knex.raw(
        'ALTER TABLE role_app_menu_items ADD CONSTRAINT fk_role_app_menu_items_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE'
      );
      console.log('✅ Added foreign key fk_role_app_menu_items_role on role_id');
    } catch (error) {
      console.log('⚠️  Foreign key fk_role_app_menu_items_role may already exist or could not be added');
    }
  }

  if (!existingColumns.includes('app_menu_item_id')) {
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table.string('app_menu_item_id', 60).nullable();
    });
    console.log('✅ Added app_menu_item_id field');

    // Try to add the foreign key constraint
    try {
      await knex.raw(
        'ALTER TABLE role_app_menu_items ADD CONSTRAINT fk_role_app_menu_items_menu FOREIGN KEY (app_menu_item_id) REFERENCES app_menu_items(id) ON DELETE CASCADE'
      );
      console.log('✅ Added foreign key fk_role_app_menu_items_menu on app_menu_item_id');
    } catch (error) {
      console.log('⚠️  Foreign key fk_role_app_menu_items_menu may already exist or could not be added');
    }
  }

  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table.enum('status', ['active', 'inactive']).notNullable().defaultTo('active');
    });
    console.log('✅ Added status field');
  }

  if (!existingColumns.includes('deleted_at')) {
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table.timestamp('deleted_at').nullable();
    });
    console.log('✅ Added deleted_at field');
  }

  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table.timestamp('updated_at').nullable();
    });
    console.log('✅ Added updated_at field');
  }

  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table.timestamp('created_at').nullable();
    });
    console.log('✅ Added created_at field');
  }

  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM role_app_menu_items');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);

  if (!existingIndexes.includes('role_app_menu_items_role_id_index')) {
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table.index(['role_id']);
    });
    console.log('✅ Added role_id index');
  }

  if (!existingIndexes.includes('role_app_menu_items_app_menu_item_id_index')) {
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table.index(['app_menu_item_id']);
    });
    console.log('✅ Added app_menu_item_id index');
  }

  if (!existingIndexes.includes('role_app_menu_items_status_index')) {
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }

  if (!existingIndexes.includes('role_app_menu_items_deleted_at_index')) {
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table.index(['deleted_at']);
    });
    console.log('✅ Added deleted_at index');
  }

  if (!existingIndexes.includes('role_app_menu_items_created_at_index')) {
    await knex.schema.alterTable('role_app_menu_items', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }

  console.log('✅ Field check complete for role_app_menu_items table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
}

