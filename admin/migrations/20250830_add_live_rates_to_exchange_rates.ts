import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  const hasTable = await knex.schema.hasTable('exchange_rates');
  
  if (!hasTable) {
    console.log('⚠️  exchange_rates table does not exist, skipping migration');
    return;
  }

  const existingColumns = await knex('exchange_rates').columnInfo().then(cols => Object.keys(cols));
  console.log('📋 Existing columns:', existingColumns);

  // Add buy_rate column
  if (!existingColumns.includes('buy_rate')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.decimal('buy_rate', 20, 7).nullable().comment('Live buy rate from Stellar');
    });
    console.log('✅ Added buy_rate column');
  }

  // Add sell_rate column
  if (!existingColumns.includes('sell_rate')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.decimal('sell_rate', 20, 7).nullable().comment('Live sell rate from Stellar');
    });
    console.log('✅ Added sell_rate column');
  }

  // Add last_price_sync column
  if (!existingColumns.includes('last_price_sync')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.timestamp('last_price_sync').nullable().comment('When price was last synced from Stellar');
    });
    console.log('✅ Added last_price_sync column');
  }

  console.log('✅ Migration completed: Added live rate columns to exchange_rates table');
}

export async function down(knex: Knex): Promise<void> {
  const hasTable = await knex.schema.hasTable('exchange_rates');
  
  if (!hasTable) {
    console.log('⚠️  exchange_rates table does not exist, skipping rollback');
    return;
  }

  await knex.schema.alterTable('exchange_rates', (table) => {
    table.dropColumn('buy_rate');
    table.dropColumn('sell_rate');
    table.dropColumn('last_price_sync');
  });

  console.log('✅ Rollback completed: Removed live rate columns from exchange_rates table');
}

