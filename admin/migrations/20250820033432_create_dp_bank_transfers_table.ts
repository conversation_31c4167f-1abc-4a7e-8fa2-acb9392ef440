import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('dp_bank_transfers');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('dp_bank_transfers', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('bank_name', 255).notNullable();
      table.string('account_name', 255).notNullable();
      table.string('account_number', 50).notNullable().unique();
      table.string('swift_code', 20).nullable();
      table.string('country', 30).notNullable();
      table.string('currency', 3).notNullable();
      table.string('reference_code', 50).notNullable().unique();
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.index(['bank_name']);
      table.index(['account_name']);
      table.index(['country']);
      table.index(['currency']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created dp_bank_transfers table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table dp_bank_transfers exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM dp_bank_transfers');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('bank_name')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.string('bank_name', 255).notNullable();
    });
    console.log('✅ Added bank_name field');
  }
  
  if (!existingColumns.includes('account_name')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.string('account_name', 255).notNullable();
    });
    console.log('✅ Added account_name field');
  }
  
  if (!existingColumns.includes('account_number')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.string('account_number', 50).notNullable().unique();
    });
    console.log('✅ Added account_number field');
  }
  
  if (!existingColumns.includes('swift_code')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.string('swift_code', 20).nullable();
    });
    console.log('✅ Added swift_code field');
  }
  
  if (!existingColumns.includes('country')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.string('country', 30).notNullable();
    });
    console.log('✅ Added country field');
  }
  
  if (!existingColumns.includes('currency')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.string('currency', 3).notNullable();
    });
    console.log('✅ Added currency field');
  }
  
  if (!existingColumns.includes('reference_code')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.string('reference_code', 50).notNullable().unique();
    });
    console.log('✅ Added reference_code field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM dp_bank_transfers');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('dp_bank_transfers_bank_name_index')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.index(['bank_name']);
    });
    console.log('✅ Added bank_name index');
  }
  
  if (!existingIndexes.includes('dp_bank_transfers_account_name_index')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.index(['account_name']);
    });
    console.log('✅ Added account_name index');
  }
  
  if (!existingIndexes.includes('dp_bank_transfers_country_index')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.index(['country']);
    });
    console.log('✅ Added country index');
  }
  
  if (!existingIndexes.includes('dp_bank_transfers_currency_index')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.index(['currency']);
    });
    console.log('✅ Added currency index');
  }
  
  if (!existingIndexes.includes('dp_bank_transfers_created_at_index')) {
    await knex.schema.alterTable('dp_bank_transfers', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  // Check for unique constraints
  if (!existingIndexes.includes('account_number')) {
    try {
      await knex.raw('ALTER TABLE dp_bank_transfers ADD UNIQUE KEY account_number (account_number)');
      console.log('✅ Added unique constraint on account_number');
    } catch (error) {
      console.log('⚠️  Unique constraint on account_number already exists or could not be added');
    }
  }
  
  if (!existingIndexes.includes('reference_code')) {
    try {
      await knex.raw('ALTER TABLE dp_bank_transfers ADD UNIQUE KEY reference_code (reference_code)');
      console.log('✅ Added unique constraint on reference_code');
    } catch (error) {
      console.log('⚠️  Unique constraint on reference_code already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for dp_bank_transfers table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 