import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('system_users');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('system_users', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('first_name', 40).nullable();
      table.string('last_name', 50).nullable();
      table.string('email', 255).notNullable().unique();
      table.string('password', 255).notNullable();
      table.string('role', 60).defaultTo('admin');
      table.enum('default_password', ['true', 'false']).nullable();
      table.timestamp('default_password_expiry').nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.enum('status', ['active', 'inactive']).defaultTo('inactive');
      table.timestamp('deleted_at').nullable();
      table.timestamp('updated_at').nullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('system_users', (table) => {
      table.index(['role']);
      table.index(['status']);
      table.index(['default_password']);
      table.index(['created_at']);
      table.index(['updated_at']);
      table.index(['deleted_at']);
    });
    
    console.log('✅ Created system_users table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table system_users exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM system_users');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('first_name')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.string('first_name', 40).nullable();
    });
    console.log('✅ Added first_name field');
  }
  
  if (!existingColumns.includes('last_name')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.string('last_name', 50).nullable();
    });
    console.log('✅ Added last_name field');
  }
  
  if (!existingColumns.includes('email')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.string('email', 255).notNullable().unique();
    });
    console.log('✅ Added email field');
  }
  
  if (!existingColumns.includes('password')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.string('password', 255).notNullable();
    });
    console.log('✅ Added password field');
  }
  
  if (!existingColumns.includes('role')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.string('role', 60).defaultTo('admin');
    });
    console.log('✅ Added role field');
  }
  
  if (!existingColumns.includes('default_password')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.enum('default_password', ['true', 'false']).nullable();
    });
    console.log('✅ Added default_password field');
  }
  
  if (!existingColumns.includes('default_password_expiry')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.timestamp('default_password_expiry').nullable();
    });
    console.log('✅ Added default_password_expiry field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.enum('status', ['active', 'inactive']).defaultTo('inactive');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('deleted_at')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.timestamp('deleted_at').nullable();
    });
    console.log('✅ Added deleted_at field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.timestamp('updated_at').nullable();
    });
    console.log('✅ Added updated_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM system_users');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('system_users_role_index')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.index(['role']);
    });
    console.log('✅ Added role index');
  }
  
  if (!existingIndexes.includes('system_users_status_index')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('system_users_default_password_index')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.index(['default_password']);
    });
    console.log('✅ Added default_password index');
  }
  
  if (!existingIndexes.includes('system_users_created_at_index')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  if (!existingIndexes.includes('system_users_updated_at_index')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.index(['updated_at']);
    });
    console.log('✅ Added updated_at index');
  }
  
  if (!existingIndexes.includes('system_users_deleted_at_index')) {
    await knex.schema.alterTable('system_users', (table) => {
      table.index(['deleted_at']);
    });
    console.log('✅ Added deleted_at index');
  }
  
  // Check for unique constraint on email
  if (!existingIndexes.includes('email')) {
    try {
      await knex.raw('ALTER TABLE system_users ADD UNIQUE KEY email (email)');
      console.log('✅ Added unique constraint on email');
    } catch (error) {
      console.log('⚠️  Unique constraint on email already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for system_users table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 