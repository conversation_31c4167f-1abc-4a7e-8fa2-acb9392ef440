import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('client_wallets');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('client_wallets', (table) => {
      table.increments('id').primary(); // Auto-incrementing int primary key
      table.string('client_id', 50).notNullable().unique();
      table.string('public_key', 70).notNullable().unique();
      table.string('secret_key', 250).notNullable();
      table.timestamp('created_on').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('client_wallets', (table) => {
      table.index(['created_on']);
    });
    
    console.log('✅ Created client_wallets table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table client_wallets exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM client_wallets');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('client_wallets', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('client_wallets', (table) => {
      table.string('client_id', 50).notNullable().unique();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('public_key')) {
    await knex.schema.alterTable('client_wallets', (table) => {
      table.string('public_key', 70).notNullable().unique();
    });
    console.log('✅ Added public_key field');
  }
  
  if (!existingColumns.includes('secret_key')) {
    await knex.schema.alterTable('client_wallets', (table) => {
      table.string('secret_key', 250).notNullable();
    });
    console.log('✅ Added secret_key field');
  }
  
  if (!existingColumns.includes('created_on')) {
    await knex.schema.alterTable('client_wallets', (table) => {
      table.timestamp('created_on').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_on field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM client_wallets');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('client_wallets_created_on_index')) {
    await knex.schema.alterTable('client_wallets', (table) => {
      table.index(['created_on']);
    });
    console.log('✅ Added created_on index');
  }
  
  // Check for unique constraints
  if (!existingIndexes.includes('client_id')) {
    try {
      await knex.raw('ALTER TABLE client_wallets ADD UNIQUE KEY client_id (client_id)');
      console.log('✅ Added unique constraint on client_id');
    } catch (error) {
      console.log('⚠️  Unique constraint on client_id already exists or could not be added');
    }
  }
  
  if (!existingIndexes.includes('public_key')) {
    try {
      await knex.raw('ALTER TABLE client_wallets ADD UNIQUE KEY public_key (public_key)');
      console.log('✅ Added unique constraint on public_key');
    } catch (error) {
      console.log('⚠️  Unique constraint on public_key already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for client_wallets table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 