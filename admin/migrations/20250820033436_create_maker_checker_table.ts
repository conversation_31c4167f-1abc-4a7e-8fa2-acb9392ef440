import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('maker_checker');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('maker_checker', (table) => {
      table.string('id', 36).primary(); // char(36) for UUID
      table.string('maker_id', 36).notNullable(); // char(36) for UUID
      table.string('checker_id', 36).nullable(); // char(36) for UUID
      table.enum('entry_type', [
        'add_payment_methods',
        'edit_payment_methods',
        'withdraw',
        'deposit',
        'add_exchange_rate',
        'edit_exchange_rate',
        'add_user',
        'add_business',
        'add_admin',
        'edit_admin',
        'debit',
        'credit',
        'edit_product',
        'edit_role',
        'add_role',
        'add_business_fees',
        'edit_business_fees'
      ]).notNullable();
      table.enum('status', ['pending', 'approved', 'rejected', 'processing']).defaultTo('pending');
      table.text('reason').nullable();
      table.text('data_content').nullable();
      table.datetime('approved_at').nullable();
      table.datetime('created_at').defaultTo(knex.fn.now());
      table.datetime('updated_at').defaultTo(knex.fn.now());
      table.datetime('deleted_at').nullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('maker_checker', (table) => {
      table.index(['maker_id']);
      table.index(['checker_id']);
      table.index(['entry_type']);
      table.index(['status']);
      table.index(['approved_at']);
      table.index(['created_at']);
      table.index(['updated_at']);
      table.index(['deleted_at']);
    });
    
    console.log('✅ Created maker_checker table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table maker_checker exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM maker_checker');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.string('id', 36).primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('maker_id')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.string('maker_id', 36).notNullable();
    });
    console.log('✅ Added maker_id field');
  }
  
  if (!existingColumns.includes('checker_id')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.string('checker_id', 36).nullable();
    });
    console.log('✅ Added checker_id field');
  }
  
  if (!existingColumns.includes('entry_type')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.enum('entry_type', [
        'add_payment_methods',
        'edit_payment_methods',
        'withdraw',
        'deposit',
        'add_exchange_rate',
        'edit_exchange_rate',
        'add_user',
        'add_business',
        'add_admin',
        'edit_admin',
        'debit',
        'credit',
        'edit_product',
        'edit_role',
        'add_role',
        'add_business_fees',
        'edit_business_fees'
      ]).notNullable();
    });
    console.log('✅ Added entry_type field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.enum('status', ['pending', 'approved', 'rejected', 'processing']).defaultTo('pending');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('reason')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.text('reason').nullable();
    });
    console.log('✅ Added reason field');
  }
  
  if (!existingColumns.includes('data_content')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.text('data_content').nullable();
    });
    console.log('✅ Added data_content field');
  }
  
  if (!existingColumns.includes('approved_at')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.datetime('approved_at').nullable();
    });
    console.log('✅ Added approved_at field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.datetime('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.datetime('updated_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added updated_at field');
  }
  
  if (!existingColumns.includes('deleted_at')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.datetime('deleted_at').nullable();
    });
    console.log('✅ Added deleted_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM maker_checker');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('maker_checker_maker_id_index')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.index(['maker_id']);
    });
    console.log('✅ Added maker_id index');
  }
  
  if (!existingIndexes.includes('maker_checker_checker_id_index')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.index(['checker_id']);
    });
    console.log('✅ Added checker_id index');
  }
  
  if (!existingIndexes.includes('maker_checker_entry_type_index')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.index(['entry_type']);
    });
    console.log('✅ Added entry_type index');
  }
  
  if (!existingIndexes.includes('maker_checker_status_index')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('maker_checker_approved_at_index')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.index(['approved_at']);
    });
    console.log('✅ Added approved_at index');
  }
  
  if (!existingIndexes.includes('maker_checker_created_at_index')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  if (!existingIndexes.includes('maker_checker_updated_at_index')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.index(['updated_at']);
    });
    console.log('✅ Added updated_at index');
  }
  
  if (!existingIndexes.includes('maker_checker_deleted_at_index')) {
    await knex.schema.alterTable('maker_checker', (table) => {
      table.index(['deleted_at']);
    });
    console.log('✅ Added deleted_at index');
  }
  
  console.log('✅ Field check complete for maker_checker table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
}