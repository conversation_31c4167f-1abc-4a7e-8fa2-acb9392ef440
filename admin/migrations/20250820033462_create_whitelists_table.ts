import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('whitelists');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('whitelists', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('client_id', 20).notNullable();
      table.string('allowed_ip', 30).notNullable().unique();
      table.string('created_by', 20).notNullable();
      table.enum('status', ['active', 'inactive']).notNullable();
      table.timestamp('created_on').notNullable().defaultTo(knex.fn.now());
      table.timestamp('deactivated_on').notNullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('whitelists', (table) => {
      table.index(['client_id']);
      table.index(['created_by']);
      table.index(['status']);
      table.index(['created_on']);
      table.index(['deactivated_on']);
    });
    
    console.log('✅ Created whitelists table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table whitelists exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM whitelists');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('whitelists', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('whitelists', (table) => {
      table.string('client_id', 20).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('allowed_ip')) {
    await knex.schema.alterTable('whitelists', (table) => {
      table.string('allowed_ip', 30).notNullable().unique();
    });
    console.log('✅ Added allowed_ip field');
  }
  
  if (!existingColumns.includes('created_by')) {
    await knex.schema.alterTable('whitelists', (table) => {
      table.string('created_by', 20).notNullable();
    });
    console.log('✅ Added created_by field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('whitelists', (table) => {
      table.enum('status', ['active', 'inactive']).notNullable();
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('created_on')) {
    await knex.schema.alterTable('whitelists', (table) => {
      table.timestamp('created_on').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_on field');
  }
  
  if (!existingColumns.includes('deactivated_on')) {
    await knex.schema.alterTable('whitelists', (table) => {
      table.timestamp('deactivated_on').notNullable();
    });
    console.log('✅ Added deactivated_on field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM whitelists');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('whitelists_client_id_index')) {
    await knex.schema.alterTable('whitelists', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('whitelists_created_by_index')) {
    await knex.schema.alterTable('whitelists', (table) => {
      table.index(['created_by']);
    });
    console.log('✅ Added created_by index');
  }
  
  if (!existingIndexes.includes('whitelists_status_index')) {
    await knex.schema.alterTable('whitelists', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('whitelists_created_on_index')) {
    await knex.schema.alterTable('whitelists', (table) => {
      table.index(['created_on']);
    });
    console.log('✅ Added created_on index');
  }
  
  if (!existingIndexes.includes('whitelists_deactivated_on_index')) {
    await knex.schema.alterTable('whitelists', (table) => {
      table.index(['deactivated_on']);
    });
    console.log('✅ Added deactivated_on index');
  }
  
  // Check for unique constraint on allowed_ip
  if (!existingIndexes.includes('allowed_ip')) {
    try {
      await knex.raw('ALTER TABLE whitelists ADD UNIQUE KEY allowed_ip (allowed_ip)');
      console.log('✅ Added unique constraint on allowed_ip');
    } catch (error) {
      console.log('⚠️  Unique constraint on allowed_ip already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for whitelists table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 