import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('products');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('products', (table) => {
      table.increments('product_id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('product_name', 255).notNullable();
      table.string('product_code', 30).notNullable();
      table.enum('transaction_type', ['PUSH', 'PULL']).notNullable();
      table.enum('status', ['active', 'inactive']).defaultTo('active');
      table.string('currency', 30).notNullable();
      table.decimal('provider_fee', 10, 2).notNullable();
      table.enum('fee_type', ['FLAT', 'PERCENTAGE', 'rTIER']).notNullable();
      table.decimal('fee_amount', 10, 1).notNullable();
      table.decimal('max_fee_amount', 150, 2).notNullable();
      table.integer('requires_extra_data').notNullable().defaultTo(0);
      table.enum('has_c_account', ['yes', 'no']).notNullable().defaultTo('no');
      table.integer('min_amount').notNullable();
      table.integer('max_amount').notNullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    
    
    // Add indexes for better performance
    await knex.schema.alterTable('products', (table) => {
      table.index(['product_name']);
      table.index(['product_code']);
      table.index(['transaction_type']);
      table.index(['status']);
      table.index(['currency']);
      table.index(['fee_type']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created products table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table products exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM products');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('product_id')) {
    await knex.schema.alterTable('products', (table) => {
      table.increments('product_id').primary();
    });
    console.log('✅ Added product_id field');
  }
  
  if (!existingColumns.includes('product_name')) {
    await knex.schema.alterTable('products', (table) => {
      table.string('product_name', 255).notNullable();
    });
    console.log('✅ Added product_name field');
  }
  
  if (!existingColumns.includes('product_code')) {
    await knex.schema.alterTable('products', (table) => {
      table.string('product_code', 30).notNullable();
    });
    console.log('✅ Added product_code field');
  }
  
  if (!existingColumns.includes('transaction_type')) {
    await knex.schema.alterTable('products', (table) => {
      table.enum('transaction_type', ['PUSH', 'PULL']).notNullable();
    });
    console.log('✅ Added transaction_type field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('products', (table) => {
      table.enum('status', ['active', 'inactive']).defaultTo('active');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('currency')) {
    await knex.schema.alterTable('products', (table) => {
      table.string('currency', 30).notNullable();
    });
    console.log('✅ Added currency field');
  }
  
  if (!existingColumns.includes('provider_fee')) {
    await knex.schema.alterTable('products', (table) => {
      table.decimal('provider_fee', 10, 2).notNullable();
    });
    console.log('✅ Added provider_fee field');
  }
  
  if (!existingColumns.includes('fee_type')) {
    await knex.schema.alterTable('products', (table) => {
      table.enum('fee_type', ['FLAT', 'PERCENTAGE']).notNullable();
    });
    console.log('✅ Added fee_type field');
  }
  
  if (!existingColumns.includes('fee_amount')) {
    await knex.schema.alterTable('products', (table) => {
      table.decimal('fee_amount', 10, 1).notNullable();
    });
    console.log('✅ Added fee_amount field');
  }
  
  if (!existingColumns.includes('requires_extra_data')) {
    await knex.schema.alterTable('products', (table) => {
      table.integer('requires_extra_data').notNullable().defaultTo(0);
    });
    console.log('✅ Added requires_extra_data field');
  }
  
  if (!existingColumns.includes('has_c_account')) {
    await knex.schema.alterTable('products', (table) => {
      table.enum('has_c_account', ['yes', 'no']).notNullable().defaultTo('no');
    });
    console.log('✅ Added has_c_account field');
  }
  
  if (!existingColumns.includes('min_amount')) {
    await knex.schema.alterTable('products', (table) => {
      table.integer('min_amount').notNullable();
    });
    console.log('✅ Added min_amount field');
  }
  
  if (!existingColumns.includes('max_amount')) {
    await knex.schema.alterTable('products', (table) => {
      table.integer('max_amount').notNullable();
    });
    console.log('✅ Added max_amount field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('products', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM products');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('products_product_name_index')) {
    await knex.schema.alterTable('products', (table) => {
      table.index(['product_name']);
    });
    console.log('✅ Added product_name index');
  }
  
  if (!existingIndexes.includes('products_product_code_index')) {
    await knex.schema.alterTable('products', (table) => {
      table.index(['product_code']);
    });
    console.log('✅ Added product_code index');
  }
  
  if (!existingIndexes.includes('products_transaction_type_index')) {
    await knex.schema.alterTable('products', (table) => {
      table.index(['transaction_type']);
    });
    console.log('✅ Added transaction_type index');
  }
  
  if (!existingIndexes.includes('products_status_index')) {
    await knex.schema.alterTable('products', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('products_currency_index')) {
    await knex.schema.alterTable('products', (table) => {
      table.index(['currency']);
    });
    console.log('✅ Added currency index');
  }
  
  if (!existingIndexes.includes('products_fee_type_index')) {
    await knex.schema.alterTable('products', (table) => {
      table.index(['fee_type']);
    });
    console.log('✅ Added fee_type index');
  }
  
  if (!existingIndexes.includes('products_created_at_index')) {
    await knex.schema.alterTable('products', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for products table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 