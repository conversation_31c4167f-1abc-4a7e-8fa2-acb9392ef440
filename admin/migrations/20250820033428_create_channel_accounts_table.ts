import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('channel_accounts');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('channel_accounts', (table) => {
      table.increments('id').primary(); // Auto-incrementing int primary key
      table.string('private_key', 70).notNullable().unique();
      table.string('public_key', 100).notNullable();
      table.enum('status', ['free', 'inUse', 'Inactive']).notNullable().defaultTo('free');
      table.timestamp('updated_at').notNullable().defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('channel_accounts', (table) => {
      table.index(['public_key']);
      table.index(['status']);
      table.index(['updated_at']);
    });
    
    console.log('✅ Created channel_accounts table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table channel_accounts exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM channel_accounts');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('channel_accounts', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('private_key')) {
    await knex.schema.alterTable('channel_accounts', (table) => {
      table.string('private_key', 70).notNullable().unique();
    });
    console.log('✅ Added private_key field');
  }
  
  if (!existingColumns.includes('public_key')) {
    await knex.schema.alterTable('channel_accounts', (table) => {
      table.string('public_key', 100).notNullable();
    });
    console.log('✅ Added public_key field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('channel_accounts', (table) => {
      table.enum('status', ['free', 'inUse', 'Inactive']).notNullable().defaultTo('free');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('channel_accounts', (table) => {
      table.timestamp('updated_at').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added updated_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM channel_accounts');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('channel_accounts_public_key_index')) {
    await knex.schema.alterTable('channel_accounts', (table) => {
      table.index(['public_key']);
    });
    console.log('✅ Added public_key index');
  }
  
  if (!existingIndexes.includes('channel_accounts_status_index')) {
    await knex.schema.alterTable('channel_accounts', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('channel_accounts_updated_at_index')) {
    await knex.schema.alterTable('channel_accounts', (table) => {
      table.index(['updated_at']);
    });
    console.log('✅ Added updated_at index');
  }
  
  // Check for unique constraint on private_key
  if (!existingIndexes.includes('private_key')) {
    try {
      await knex.raw('ALTER TABLE channel_accounts ADD UNIQUE KEY private_key (private_key)');
      console.log('✅ Added unique constraint on private_key');
    } catch (error) {
      console.log('⚠️  Unique constraint on private_key already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for channel_accounts table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 