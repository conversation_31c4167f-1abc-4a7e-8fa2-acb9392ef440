import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('role_access_rights');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('role_access_rights', (table) => {
      table.string('id', 60).primary(); // char(60) NOT NULL
      table.string('role_id', 60).nullable();
      table.string('access_right_id', 60).nullable();
      table.enum('status', ['active', 'inactive']).defaultTo('active');
      table.timestamp('deleted_at').nullable();
      table.timestamp('updated_at').nullable();
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.index(['role_id']);
      table.index(['access_right_id']);
      table.index(['status']);
      table.index(['deleted_at']);
      table.index(['updated_at']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created role_access_rights table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table role_access_rights exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM role_access_rights');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.string('id', 60).primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('role_id')) {
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.string('role_id', 60).nullable();
    });
    console.log('✅ Added role_id field');
  }
  
  if (!existingColumns.includes('access_right_id')) {
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.string('access_right_id', 60).nullable();
    });
    console.log('✅ Added access_right_id field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.enum('status', ['active', 'inactive']).defaultTo('active');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('deleted_at')) {
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.timestamp('deleted_at').nullable();
    });
    console.log('✅ Added deleted_at field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.timestamp('updated_at').nullable();
    });
    console.log('✅ Added updated_at field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM role_access_rights');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('role_access_rights_role_id_index')) {
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.index(['role_id']);
    });
    console.log('✅ Added role_id index');
  }
  
  if (!existingIndexes.includes('role_access_rights_access_right_id_index')) {
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.index(['access_right_id']);
    });
    console.log('✅ Added access_right_id index');
  }
  
  if (!existingIndexes.includes('role_access_rights_status_index')) {
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('role_access_rights_deleted_at_index')) {
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.index(['deleted_at']);
    });
    console.log('✅ Added deleted_at index');
  }
  
  if (!existingIndexes.includes('role_access_rights_updated_at_index')) {
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.index(['updated_at']);
    });
    console.log('✅ Added updated_at index');
  }
  
  if (!existingIndexes.includes('role_access_rights_created_at_index')) {
    await knex.schema.alterTable('role_access_rights', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for role_access_rights table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 