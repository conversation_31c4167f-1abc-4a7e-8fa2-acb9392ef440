import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('clients');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('clients', (table) => {
      table.increments('id').primary();
      table.string('client_id', 50).notNullable().unique();
      table.string('contact_email', 40).notNullable().unique();
      table.string('business_name', 255).notNullable();
      table.string('phone_number', 20).notNullable();
      table.string('country', 100).nullable();
      table.string('city', 100).nullable();
      table.text('address').nullable();
      table.string('industry', 100).nullable();
      table.enum('kyc_status', ['unverified', 'verified', 'inReview']).notNullable().defaultTo('unverified');
      table.enum('status', ['active', 'inactive', 'pending', 'rejected']).notNullable().defaultTo('active');
      table.string('registration_number', 50).nullable();
      table.string('contact_person_name', 255).notNullable();
      table.string('contact_phone', 20).notNullable();
      table.enum('default_password', ['true', 'false']).nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').defaultTo(knex.fn.now());
      table.enum('environment', ['test', 'live']).notNullable().defaultTo('test');
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('clients', (table) => {
      table.index(['client_id']);
      table.index(['contact_email']);
      table.index(['business_name']);
      table.index(['status']);
      table.index(['kyc_status']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created clients table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table clients exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM clients');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('clients', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('clients', (table) => {
      table.string('client_id', 50).notNullable().unique();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('contact_email')) {
    await knex.schema.alterTable('clients', (table) => {
      table.string('contact_email', 40).notNullable().unique();
    });
    console.log('✅ Added contact_email field');
  }
  
  if (!existingColumns.includes('business_name')) {
    await knex.schema.alterTable('clients', (table) => {
      table.string('business_name', 255).notNullable();
    });
    console.log('✅ Added business_name field');
  }
  
  if (!existingColumns.includes('phone_number')) {
    await knex.schema.alterTable('clients', (table) => {
      table.string('phone_number', 20).notNullable();
    });
    console.log('✅ Added phone_number field');
  }
  
  if (!existingColumns.includes('country')) {
    await knex.schema.alterTable('clients', (table) => {
      table.string('country', 100).nullable();
    });
    console.log('✅ Added country field');
  }
  
  if (!existingColumns.includes('city')) {
    await knex.schema.alterTable('clients', (table) => {
      table.string('city', 100).nullable();
    });
    console.log('✅ Added city field');
  }
  
  if (!existingColumns.includes('address')) {
    await knex.schema.alterTable('clients', (table) => {
      table.text('address').nullable();
    });
    console.log('✅ Added address field');
  }
  
  if (!existingColumns.includes('industry')) {
    await knex.schema.alterTable('clients', (table) => {
      table.string('industry', 100).nullable();
    });
    console.log('✅ Added industry field');
  }
  
  if (!existingColumns.includes('kyc_status')) {
    await knex.schema.alterTable('clients', (table) => {
      table.enum('kyc_status', ['unverified', 'verified', 'inReview']).notNullable().defaultTo('unverified');
    });
    console.log('✅ Added kyc_status field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('clients', (table) => {
      table.enum('status', ['active', 'inactive', 'pending', 'rejected']).notNullable().defaultTo('active');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('registration_number')) {
    await knex.schema.alterTable('clients', (table) => {
      table.string('registration_number', 50).nullable();
    });
    console.log('✅ Added registration_number field');
  }
  
  if (!existingColumns.includes('contact_person_name')) {
    await knex.schema.alterTable('clients', (table) => {
      table.string('contact_person_name', 255).notNullable();
    });
    console.log('✅ Added contact_person_name field');
  }
  
  if (!existingColumns.includes('contact_phone')) {
    await knex.schema.alterTable('clients', (table) => {
      table.string('contact_phone', 20).notNullable();
    });
    console.log('✅ Added contact_phone field');
  }
  
  if (!existingColumns.includes('default_password')) {
    await knex.schema.alterTable('clients', (table) => {
      table.enum('default_password', ['true', 'false']).nullable();
    });
    console.log('✅ Added default_password field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('clients', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('clients', (table) => {
      table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added updated_at field');
  }
  
  if (!existingColumns.includes('environment')) {
    await knex.schema.alterTable('clients', (table) => {
      table.enum('environment', ['test', 'live']).notNullable().defaultTo('test');
    });
    console.log('✅ Added environment field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM clients');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('clients_client_id_index')) {
    await knex.schema.alterTable('clients', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('clients_contact_email_index')) {
    await knex.schema.alterTable('clients', (table) => {
      table.index(['contact_email']);
    });
    console.log('✅ Added contact_email index');
  }
  
  if (!existingIndexes.includes('clients_business_name_index')) {
    await knex.schema.alterTable('clients', (table) => {
      table.index(['business_name']);
    });
    console.log('✅ Added business_name index');
  }
  
  if (!existingIndexes.includes('clients_status_index')) {
    await knex.schema.alterTable('clients', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('clients_kyc_status_index')) {
    await knex.schema.alterTable('clients', (table) => {
      table.index(['kyc_status']);
    });
    console.log('✅ Added kyc_status index');
  }
  
  if (!existingIndexes.includes('clients_created_at_index')) {
    await knex.schema.alterTable('clients', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for clients table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
}

