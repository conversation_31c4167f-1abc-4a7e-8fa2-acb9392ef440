import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('transactions_log');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('transactions_log', (table) => {
      table.bigIncrements('id').unsigned().primary(); // bigint UNSIGNED NOT NULL AUTO_INCREMENT
      table.string('trans_id', 255).notNullable();
      table.string('status', 100).nullable();
      table.string('step', 40).nullable();
      table.string('response_code', 10).nullable();
      table.text('description').nullable();
      table.text('data').nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('transactions_log', (table) => {
      table.index(['trans_id']);
      table.index(['status']);
      table.index(['step']);
      table.index(['response_code']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created transactions_log table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table transactions_log exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM transactions_log');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('transactions_log', (table) => {
      table.bigIncrements('id').unsigned().primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('trans_id')) {
    await knex.schema.alterTable('transactions_log', (table) => {
      table.string('trans_id', 255).notNullable();
    });
    console.log('✅ Added trans_id field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('transactions_log', (table) => {
      table.string('status', 100).nullable();
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('step')) {
    await knex.schema.alterTable('transactions_log', (table) => {
      table.string('step', 40).nullable();
    });
    console.log('✅ Added step field');
  }
  
  if (!existingColumns.includes('response_code')) {
    await knex.schema.alterTable('transactions_log', (table) => {
      table.string('response_code', 10).nullable();
    });
    console.log('✅ Added response_code field');
  }
  
      if (!existingColumns.includes('description')) {
      await knex.schema.alterTable('transactions_log', (table) => {
        table.text('description').nullable();
      });
      console.log('✅ Added description field');
    }
  
  if (!existingColumns.includes('data')) {
    await knex.schema.alterTable('transactions_log', (table) => {
      table.text('data').nullable();
    });
    console.log('✅ Added data field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('transactions_log', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM transactions_log');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('transactions_log_trans_id_index')) {
    await knex.schema.alterTable('transactions_log', (table) => {
      table.index(['trans_id']);
    });
    console.log('✅ Added trans_id index');
  }
  
  if (!existingIndexes.includes('transactions_log_status_index')) {
    await knex.schema.alterTable('transactions_log', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('transactions_log_step_index')) {
    await knex.schema.alterTable('transactions_log', (table) => {
      table.index(['step']);
    });
    console.log('✅ Added step index');
  }
  
  if (!existingIndexes.includes('transactions_log_response_code_index')) {
    await knex.schema.alterTable('transactions_log', (table) => {
      table.index(['response_code']);
    });
    console.log('✅ Added response_code index');
  }
  
  if (!existingIndexes.includes('transactions_log_created_at_index')) {
    await knex.schema.alterTable('transactions_log', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for transactions_log table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 