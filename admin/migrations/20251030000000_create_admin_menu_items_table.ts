import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('app_menu_items');

  if (!tableExists) {
    await knex.schema.createTable('app_menu_items', (table) => {
      table.string('id', 36).primary(); // UUID (char(36)) primary key
      table.string('name', 100).notNullable();
      table.string('icon', 100).nullable();
      table.string('path', 255).nullable();
      table.enum('menu_type', ['ADMIN', 'CLIENT']).notNullable().defaultTo('ADMIN');
      table
        .string('parent_id', 36)
        .nullable()
        .references('id')
        .inTable('app_menu_items')
        .onDelete('CASCADE');
      table.json('permissions').nullable();
      table.integer('sort_order').notNullable().defaultTo(0);
      table.boolean('is_active').notNullable().defaultTo(true);
    });

    // Helpful indexes
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.index(['name']);
      table.index(['path']);
      table.index(['menu_type']);
      table.index(['parent_id']);
      table.index(['is_active']);
      table.index(['sort_order']);
    });

    console.log('✅ Created app_menu_items table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table app_menu_items exists, checking for missing fields...');

  const columns = await knex.raw('SHOW COLUMNS FROM app_menu_items');
  const existingColumns = columns[0].map((col: any) => col.Field);

  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.string('id', 36).primary();
    });
    console.log('✅ Added id field');
  }

  if (!existingColumns.includes('name')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.string('name', 100).notNullable();
    });
    console.log('✅ Added name field');
  }

  if (!existingColumns.includes('icon')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.string('icon', 100).nullable();
    });
    console.log('✅ Added icon field');
  }

  if (!existingColumns.includes('path')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.string('path', 255).nullable();
    });
    console.log('✅ Added path field');
  }

  if (!existingColumns.includes('menu_type')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.enum('menu_type', ['ADMIN', 'CLIENT']).notNullable().defaultTo('ADMIN');
    });
    console.log('✅ Added menu_type field');
  }

  if (!existingColumns.includes('parent_id')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.string('parent_id', 36).nullable();
    });
    console.log('✅ Added parent_id field');

    // Try to add the foreign key constraint
    try {
      await knex.raw(
        'ALTER TABLE app_menu_items ADD CONSTRAINT fk_parent_menu FOREIGN KEY (parent_id) REFERENCES app_menu_items(id) ON DELETE CASCADE'
      );
      console.log('✅ Added foreign key fk_parent_menu on parent_id');
    } catch (error) {
      console.log('⚠️  Foreign key fk_parent_menu may already exist or could not be added');
    }
  }

  if (!existingColumns.includes('permissions')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.json('permissions').nullable();
    });
    console.log('✅ Added permissions field');
  }

  if (!existingColumns.includes('sort_order')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.integer('sort_order').notNullable().defaultTo(0);
    });
    console.log('✅ Added sort_order field');
  }

  if (!existingColumns.includes('is_active')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.boolean('is_active').notNullable().defaultTo(true);
    });
    console.log('✅ Added is_active field');
  }

  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM app_menu_items');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);

  if (!existingIndexes.includes('app_menu_items_name_index')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.index(['name']);
    });
    console.log('✅ Added name index');
  }

  if (!existingIndexes.includes('app_menu_items_path_index')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.index(['path']);
    });
    console.log('✅ Added path index');
  }

  if (!existingIndexes.includes('app_menu_items_menu_type_index')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.index(['menu_type']);
    });
    console.log('✅ Added menu_type index');
  }

  if (!existingIndexes.includes('app_menu_items_parent_id_index')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.index(['parent_id']);
    });
    console.log('✅ Added parent_id index');
  }

  if (!existingIndexes.includes('app_menu_items_is_active_index')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.index(['is_active']);
    });
    console.log('✅ Added is_active index');
  }

  if (!existingIndexes.includes('app_menu_items_sort_order_index')) {
    await knex.schema.alterTable('app_menu_items', (table) => {
      table.index(['sort_order']);
    });
    console.log('✅ Added sort_order index');
  }

  console.log('✅ Field check complete for app_menu_items table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
}


