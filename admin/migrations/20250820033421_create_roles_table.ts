import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('roles');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('roles', (table) => {
      table.string('id', 36).primary(); // char(36) for UUID
      table.string('name', 50).notNullable();
      table.text('details').nullable();
      table.enum('status', ['active', 'inactive']).notNullable().defaultTo('active');
      table.string('client_id', 100).nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').nullable();
      table.timestamp('deleted_at').nullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('roles', (table) => {
      table.index(['name']);
      table.index(['status']);
      table.index(['client_id']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created roles table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table roles exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM roles');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('roles', (table) => {
      table.string('id', 36).primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('name')) {
    await knex.schema.alterTable('roles', (table) => {
      table.string('name', 50).notNullable();
    });
    console.log('✅ Added name field');
  }
  
  if (!existingColumns.includes('details')) {
    await knex.schema.alterTable('roles', (table) => {
      table.text('details').nullable();
    });
    console.log('✅ Added details field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('roles', (table) => {
      table.enum('status', ['active', 'inactive']).notNullable().defaultTo('active');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('roles', (table) => {
      table.string('client_id', 100).nullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('roles', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('roles', (table) => {
      table.timestamp('updated_at').nullable();
    });
    console.log('✅ Added updated_at field');
  }
  
  if (!existingColumns.includes('deleted_at')) {
    await knex.schema.alterTable('roles', (table) => {
      table.timestamp('deleted_at').nullable();
    });
    console.log('✅ Added deleted_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM roles');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('roles_name_index')) {
    await knex.schema.alterTable('roles', (table) => {
      table.index(['name']);
    });
    console.log('✅ Added name index');
  }
  
  if (!existingIndexes.includes('roles_status_index')) {
    await knex.schema.alterTable('roles', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('roles_client_id_index')) {
    await knex.schema.alterTable('roles', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('roles_created_at_index')) {
    await knex.schema.alterTable('roles', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for roles table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
}

