import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('network_codes');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('network_codes', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('network', 50).notNullable();
      table.integer('code').notNullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('network_codes', (table) => {
      table.index(['network']);
      table.index(['code']);
    });
    
    console.log('✅ Created network_codes table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table network_codes exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM network_codes');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('network_codes', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('network')) {
    await knex.schema.alterTable('network_codes', (table) => {
      table.string('network', 50).notNullable();
    });
    console.log('✅ Added network field');
  }
  
  if (!existingColumns.includes('code')) {
    await knex.schema.alterTable('network_codes', (table) => {
      table.integer('code').notNullable();
    });
    console.log('✅ Added code field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM network_codes');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('network_codes_network_index')) {
    await knex.schema.alterTable('network_codes', (table) => {
      table.index(['network']);
    });
    console.log('✅ Added network index');
  }
  
  if (!existingIndexes.includes('network_codes_code_index')) {
    await knex.schema.alterTable('network_codes', (table) => {
      table.index(['code']);
    });
    console.log('✅ Added code index');
  }
  
  console.log('✅ Field check complete for network_codes table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
}