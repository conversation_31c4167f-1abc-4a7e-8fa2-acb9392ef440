import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('dp_crypto_deposits');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('dp_crypto_deposits', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('network', 50).notNullable();
      table.string('deposit_address', 255).notNullable().unique();
      table.string('client_id', 20).notNullable();
      table.string('tag', 30).notNullable();
      table.string('vault_id', 30).notNullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('dp_crypto_deposits', (table) => {
      table.index(['network']);
      table.index(['client_id']);
      table.index(['tag']);
      table.index(['vault_id']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created dp_crypto_deposits table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table dp_crypto_deposits exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM dp_crypto_deposits');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('dp_crypto_deposits', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('network')) {
    await knex.schema.alterTable('dp_crypto_deposits', (table) => {
      table.string('network', 50).notNullable();
    });
    console.log('✅ Added network field');
  }
  
  if (!existingColumns.includes('deposit_address')) {
    await knex.schema.alterTable('dp_crypto_deposits', (table) => {
      table.string('deposit_address', 255).notNullable().unique();
    });
    console.log('✅ Added deposit_address field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('dp_crypto_deposits', (table) => {
      table.string('client_id', 20).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('tag')) {
    await knex.schema.alterTable('dp_crypto_deposits', (table) => {
      table.string('tag', 30).notNullable();
    });
    console.log('✅ Added tag field');
  }
  
  if (!existingColumns.includes('vault_id')) {
    await knex.schema.alterTable('dp_crypto_deposits', (table) => {
      table.string('vault_id', 30).notNullable();
    });
    console.log('✅ Added vault_id field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('dp_crypto_deposits', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM dp_crypto_deposits');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('dp_crypto_deposits_network_index')) {
    await knex.schema.alterTable('dp_crypto_deposits', (table) => {
      table.index(['network']);
    });
    console.log('✅ Added network index');
  }
  
  if (!existingIndexes.includes('dp_crypto_deposits_client_id_index')) {
    await knex.schema.alterTable('dp_crypto_deposits', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('dp_crypto_deposits_tag_index')) {
    await knex.schema.alterTable('dp_crypto_deposits', (table) => {
      table.index(['tag']);
    });
    console.log('✅ Added tag index');
  }
  
  if (!existingIndexes.includes('dp_crypto_deposits_vault_id_index')) {
    await knex.schema.alterTable('dp_crypto_deposits', (table) => {
      table.index(['vault_id']);
    });
    console.log('✅ Added vault_id index');
  }
  
  if (!existingIndexes.includes('dp_crypto_deposits_created_at_index')) {
    await knex.schema.alterTable('dp_crypto_deposits', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  // Check for unique constraint on deposit_address
  if (!existingIndexes.includes('deposit_address')) {
    try {
      await knex.raw('ALTER TABLE dp_crypto_deposits ADD UNIQUE KEY deposit_address (deposit_address)');
      console.log('✅ Added unique constraint on deposit_address');
    } catch (error) {
      console.log('⚠️  Unique constraint on deposit_address already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for dp_crypto_deposits table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 