import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('ip_whitelist');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('ip_whitelist', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('client_id', 50).notNullable();
      table.string('ip_address', 50).notNullable();
      table.string('created_by', 50).notNullable();
      table.timestamp('created_on').notNullable().defaultTo(knex.fn.now());
      table.enum('status', ['active', 'inactive']).notNullable().defaultTo('active');
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('ip_whitelist', (table) => {
      table.index(['client_id']);
      table.index(['ip_address']);
      table.index(['created_by']);
      table.index(['status']);
      table.index(['created_on']);
    });
    
    console.log('✅ Created ip_whitelist table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table ip_whitelist exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM ip_whitelist');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('ip_whitelist', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('ip_whitelist', (table) => {
      table.string('client_id', 50).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('ip_address')) {
    await knex.schema.alterTable('ip_whitelist', (table) => {
      table.string('ip_address', 50).notNullable();
    });
    console.log('✅ Added ip_address field');
  }
  
  if (!existingColumns.includes('created_by')) {
    await knex.schema.alterTable('ip_whitelist', (table) => {
      table.string('created_by', 50).notNullable();
    });
    console.log('✅ Added created_by field');
  }
  
  if (!existingColumns.includes('created_on')) {
    await knex.schema.alterTable('ip_whitelist', (table) => {
      table.timestamp('created_on').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_on field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('ip_whitelist', (table) => {
      table.enum('status', ['active', 'inactive']).notNullable().defaultTo('active');
    });
    console.log('✅ Added status field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM ip_whitelist');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('ip_whitelist_client_id_index')) {
    await knex.schema.alterTable('ip_whitelist', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('ip_whitelist_ip_address_index')) {
    await knex.schema.alterTable('ip_whitelist', (table) => {
      table.index(['ip_address']);
    });
    console.log('✅ Added ip_address index');
  }
  
  if (!existingIndexes.includes('ip_whitelist_created_by_index')) {
    await knex.schema.alterTable('ip_whitelist', (table) => {
      table.index(['created_by']);
    });
    console.log('✅ Added created_by index');
  }
  
  if (!existingIndexes.includes('ip_whitelist_status_index')) {
    await knex.schema.alterTable('ip_whitelist', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('ip_whitelist_created_on_index')) {
    await knex.schema.alterTable('ip_whitelist', (table) => {
      table.index(['created_on']);
    });
    console.log('✅ Added created_on index');
  }
  
  console.log('✅ Field check complete for ip_whitelist table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 