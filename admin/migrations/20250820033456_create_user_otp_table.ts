import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('user_otp');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('user_otp', (table) => {
      table.string('user_id', 40).notNullable().unique(); // varchar(40) NOT NULL UNIQUE
      table.string('email', 60).notNullable();
      table.string('otp', 60).notNullable();
      table.string('uuid_otp', 90).nullable();
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('user_otp', (table) => {
      table.index(['email']);
      table.index(['otp']);
      table.index(['uuid_otp']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created user_otp table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table user_otp exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM user_otp');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('user_id')) {
    await knex.schema.alterTable('user_otp', (table) => {
      table.string('user_id', 40).notNullable().unique();
    });
    console.log('✅ Added user_id field');
  }
  
  if (!existingColumns.includes('email')) {
    await knex.schema.alterTable('user_otp', (table) => {
      table.string('email', 60).notNullable();
    });
    console.log('✅ Added email field');
  }
  
  if (!existingColumns.includes('otp')) {
    await knex.schema.alterTable('user_otp', (table) => {
      table.string('otp', 60).notNullable();
    });
    console.log('✅ Added otp field');
  }
  
  if (!existingColumns.includes('uuid_otp')) {
    await knex.schema.alterTable('user_otp', (table) => {
      table.string('uuid_otp', 90).nullable();
    });
    console.log('✅ Added uuid_otp field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('user_otp', (table) => {
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM user_otp');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('user_otp_email_index')) {
    await knex.schema.alterTable('user_otp', (table) => {
      table.index(['email']);
    });
    console.log('✅ Added email index');
  }
  
  if (!existingIndexes.includes('user_otp_otp_index')) {
    await knex.schema.alterTable('user_otp', (table) => {
      table.index(['otp']);
    });
    console.log('✅ Added otp index');
  }
  
  if (!existingIndexes.includes('user_otp_uuid_otp_index')) {
    await knex.schema.alterTable('user_otp', (table) => {
      table.index(['uuid_otp']);
    });
    console.log('✅ Added uuid_otp index');
  }
  
  if (!existingIndexes.includes('user_otp_created_at_index')) {
    await knex.schema.alterTable('user_otp', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  // Check for unique constraint on user_id
  if (!existingIndexes.includes('user_id')) {
    try {
      await knex.raw('ALTER TABLE user_otp ADD UNIQUE KEY user_id (user_id)');
      console.log('✅ Added unique constraint on user_id');
    } catch (error) {
      console.log('⚠️  Unique constraint on user_id already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for user_otp table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 