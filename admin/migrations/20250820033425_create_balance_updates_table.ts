import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('balance_updates');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('balance_updates', (table) => {
      table.string('trans_id', 36).primary(); // char(36) for UUID
      table.string('client_id', 50).notNullable();
      table.string('asset_code', 10).notNullable();
      table.enum('status', ['PENDING', 'SUCCESS', 'FAILED']).defaultTo('PENDING');
      table.decimal('balance', 20, 7).notNullable(); // decimal(20,7) for precise balance
      table.enum('type', ['START', 'END', 'ADJUST']).notNullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').defaultTo(knex.fn.now());
      table.timestamp('deleted_at').nullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('balance_updates', (table) => {
      table.index(['client_id']);
      table.index(['asset_code']);
      table.index(['status']);
      table.index(['type']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created balance_updates table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table balance_updates exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM balance_updates');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('trans_id')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.string('trans_id', 36).primary();
    });
    console.log('✅ Added trans_id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.string('client_id', 50).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('asset_code')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.string('asset_code', 10).notNullable();
    });
    console.log('✅ Added asset_code field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.enum('status', ['PENDING', 'SUCCESS', 'FAILED']).defaultTo('PENDING');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('balance')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.decimal('balance', 20, 7).notNullable();
    });
    console.log('✅ Added balance field');
  }
  
  if (!existingColumns.includes('type')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.enum('type', ['START', 'END', 'ADJUST']).notNullable();
    });
    console.log('✅ Added type field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added updated_at field');
  }
  
  if (!existingColumns.includes('deleted_at')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.timestamp('deleted_at').nullable();
    });
    console.log('✅ Added deleted_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM balance_updates');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('balance_updates_client_id_index')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('balance_updates_asset_code_index')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.index(['asset_code']);
    });
    console.log('✅ Added asset_code index');
  }
  
  if (!existingIndexes.includes('balance_updates_status_index')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('balance_updates_type_index')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.index(['type']);
    });
    console.log('✅ Added type index');
  }
  
  if (!existingIndexes.includes('balance_updates_created_at_index')) {
    await knex.schema.alterTable('balance_updates', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for balance_updates table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 