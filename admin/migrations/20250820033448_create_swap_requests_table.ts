import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('swap_requests');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('swap_requests', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('client_id', 50).notNullable();
      table.string('validation_id', 50).notNullable().unique();
      table.string('product_id', 30).notNullable();
      table.enum('trans_type', ['PUSH', 'PULL', 'SWAP', 'BANK_DEPOSIT']).nullable();
      table.string('trans_id', 255).notNullable().unique();
      table.string('reference_id', 50).notNullable().unique();
      table.string('stellar_tx_id', 255).nullable().unique();
      table.decimal('amount', 18, 2).notNullable();
      table.string('asset_code', 50).defaultTo('UGX');
      table.string('currency', 3).notNullable();
      table.string('sender_account', 255).notNullable();
      table.string('receiver_account', 255).notNullable();
      table.text('memo').nullable();
      table.enum('status', ['PENDING', 'MINT_FAILED', 'failed', 'MINT_INITIATED', 'SUCCESS', 'INITIATED', 'RECEIVED']).defaultTo('PENDING');
      table.decimal('fee', 10, 2).notNullable();
      table.string('service_name', 40).nullable();
      table.string('SessionId', 50).nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('swap_requests', (table) => {
      table.index(['client_id']);
      table.index(['product_id']);
      table.index(['trans_type']);
      table.index(['asset_code']);
      table.index(['currency']);
      table.index(['sender_account']);
      table.index(['receiver_account']);
      table.index(['status']);
      table.index(['service_name']);
      table.index(['SessionId']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created swap_requests table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table swap_requests exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM swap_requests');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.string('client_id', 50).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('validation_id')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.string('validation_id', 50).notNullable().unique();
    });
    console.log('✅ Added validation_id field');
  }
  
  if (!existingColumns.includes('product_id')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.string('product_id', 30).notNullable();
    });
    console.log('✅ Added product_id field');
  }
  
  if (!existingColumns.includes('trans_type')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.enum('trans_type', ['PUSH', 'PULL', 'SWAP', 'BANK_DEPOSIT']).nullable();
    });
    console.log('✅ Added trans_type field');
  }
  
  if (!existingColumns.includes('trans_id')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.string('trans_id', 255).notNullable().unique();
    });
    console.log('✅ Added trans_id field');
  }
  
  if (!existingColumns.includes('reference_id')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.string('reference_id', 50).notNullable().unique();
    });
    console.log('✅ Added reference_id field');
  }
  
  if (!existingColumns.includes('stellar_tx_id')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.string('stellar_tx_id', 255).nullable().unique();
    });
    console.log('✅ Added stellar_tx_id field');
  }
  
  if (!existingColumns.includes('amount')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.decimal('amount', 18, 2).notNullable();
    });
    console.log('✅ Added amount field');
  }
  
  if (!existingColumns.includes('asset_code')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.string('asset_code', 50).defaultTo('UGX');
    });
    console.log('✅ Added asset_code field');
  }
  
  if (!existingColumns.includes('currency')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.string('currency', 3).notNullable();
    });
    console.log('✅ Added currency field');
  }
  
  if (!existingColumns.includes('sender_account')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.string('sender_account', 255).notNullable();
    });
    console.log('✅ Added sender_account field');
  }
  
  if (!existingColumns.includes('receiver_account')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.string('receiver_account', 255).notNullable();
    });
    console.log('✅ Added receiver_account field');
  }
  
  if (!existingColumns.includes('memo')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.text('memo').nullable();
    });
    console.log('✅ Added memo field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.enum('status', ['PENDING', 'MINT_FAILED', 'failed', 'MINT_INITIATED', 'SUCCESS', 'INITIATED', 'RECEIVED']).defaultTo('PENDING');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('fee')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.decimal('fee', 10, 2).notNullable();
    });
    console.log('✅ Added fee field');
  }
  
  if (!existingColumns.includes('service_name')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.string('service_name', 40).nullable();
    });
    console.log('✅ Added service_name field');
  }
  
  if (!existingColumns.includes('SessionId')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.string('SessionId', 50).nullable();
    });
    console.log('✅ Added SessionId field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM swap_requests');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('swap_requests_client_id_index')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('swap_requests_product_id_index')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.index(['product_id']);
    });
    console.log('✅ Added product_id index');
  }
  
  if (!existingIndexes.includes('swap_requests_trans_type_index')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.index(['trans_type']);
    });
    console.log('✅ Added trans_type index');
  }
  
  if (!existingIndexes.includes('swap_requests_asset_code_index')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.index(['asset_code']);
    });
    console.log('✅ Added asset_code index');
  }
  
  if (!existingIndexes.includes('swap_requests_currency_index')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.index(['currency']);
    });
    console.log('✅ Added currency index');
  }
  
  if (!existingIndexes.includes('swap_requests_sender_account_index')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.index(['sender_account']);
    });
    console.log('✅ Added sender_account index');
  }
  
  if (!existingIndexes.includes('swap_requests_receiver_account_index')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.index(['receiver_account']);
    });
    console.log('✅ Added receiver_account index');
  }
  
  if (!existingIndexes.includes('swap_requests_status_index')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('swap_requests_service_name_index')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.index(['service_name']);
    });
    console.log('✅ Added service_name index');
  }
  
  if (!existingIndexes.includes('swap_requests_sessionid_index')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.index(['SessionId']);
    });
    console.log('✅ Added SessionId index');
  }
  
  if (!existingIndexes.includes('swap_requests_created_at_index')) {
    await knex.schema.alterTable('swap_requests', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  // Check for unique constraints
  if (!existingIndexes.includes('validate_request_id')) {
    try {
      await knex.raw('ALTER TABLE swap_requests ADD UNIQUE KEY validate_request_id (trans_id)');
      console.log('✅ Added unique constraint on trans_id');
    } catch (error) {
      console.log('⚠️  Unique constraint on trans_id already exists or could not be added');
    }
  }
  
  if (!existingIndexes.includes('validation_id')) {
    try {
      await knex.raw('ALTER TABLE swap_requests ADD UNIQUE KEY validation_id (validation_id)');
      console.log('✅ Added unique constraint on validation_id');
    } catch (error) {
      console.log('⚠️  Unique constraint on validation_id already exists or could not be added');
    }
  }
  
  if (!existingIndexes.includes('reference_id')) {
    try {
      await knex.raw('ALTER TABLE swap_requests ADD UNIQUE KEY reference_id (reference_id)');
      console.log('✅ Added unique constraint on reference_id');
    } catch (error) {
      console.log('⚠️  Unique constraint on reference_id already exists or could not be added');
    }
  }
  
  if (!existingIndexes.includes('stellar_tx_id')) {
    try {
      await knex.raw('ALTER TABLE swap_requests ADD UNIQUE KEY stellar_tx_id (stellar_tx_id)');
      console.log('✅ Added unique constraint on stellar_tx_id');
    } catch (error) {
      console.log('⚠️  Unique constraint on stellar_tx_id already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for swap_requests table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 