import type { K<PERSON> } from "knex";

// This migration was superseded by 20251030000000_create_admin_menu_items_table.ts (now targeting app_menu_items).
// It remains as a no-op to satisfy Knex's migration validation.

export async function up(_knex: Knex): Promise<void> {
  console.log('ℹ️  Skipping deprecated migration: 20251030000000_create_menu_items_table.ts');
}

export async function down(_knex: Knex): Promise<void> {
  console.log('ℹ️  No rollback for deprecated migration: 20251030000000_create_menu_items_table.ts');
}


