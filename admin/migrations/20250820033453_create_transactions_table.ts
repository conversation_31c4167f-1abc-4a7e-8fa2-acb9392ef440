import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('transactions');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('transactions', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('client_id', 50).notNullable();
      table.string('product_id', 30).notNullable();
      table.enum('trans_type', ['PUSH', 'PULL', 'SWAP', 'BANK_DEPOSIT']).nullable();
      table.string('trans_id', 255).notNullable().unique();
      table.string('reference_id', 80).nullable();
      table.string('stellar_tx_id', 255).nullable().unique();
      table.decimal('req_amount', 10, 2).notNullable();
      table.decimal('amount', 18, 2).notNullable();
      table.string('asset_code', 50).defaultTo('UGX');
      table.string('currency', 10).notNullable();
      table.string('sender_account', 255).nullable();
      table.string('receiver_account', 255).notNullable();
      table.text('memo').nullable();
      table.enum('status', ['PENDING', 'PENDING_APPROVAL', 'MINT_FAILED', 'FAILED', 'SUCCESS', 'INITIATED', 'RECEIVED', 'ONHOLD', 'EXPIRED', 'REVERSED', 'PENDING_REVERSAL']).defaultTo('PENDING');
      table.enum('system_status', ['PENDING', 'MINT_FAILED', 'failed', 'MINT_INITIATED', 'SUCCESS', 'INITIATED', 'RECEIVED', 'ONHOLD', 'PAYOUT_FAILED', 'EXPIRED', 'REVERSED', 'PENDING_REVERSAL']).nullable();
      table.decimal('fee', 10, 2).notNullable();
      table.decimal('provider_fees', 10, 2).nullable();
      table.string('service_name', 40).nullable();
      table.decimal('running_balance', 10, 2).notNullable().defaultTo('0.00');
      table.string('SessionId', 50).nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.string('ext_reference', 60).nullable();
      table.string('payment_method_id', 50).nullable();
      table.string('validation_id', 50).nullable();
      table.string('provider_fee', 50).nullable();
      table.string('receive_currency', 30).nullable();
      table.string('approved_by', 40).nullable();
      table.string('message', 255).nullable();
      table.string('receiver_account_name', 90).nullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['client_id']);
      table.index(['product_id']);
      table.index(['trans_type']);
      table.index(['status']);
      table.index(['system_status']);
      table.index(['asset_code']);
      table.index(['currency']);
      table.index(['sender_account']);
      table.index(['receiver_account']);
      table.index(['service_name']);
      table.index(['SessionId']);
      table.index(['created_at']);
      table.index(['ext_reference']);
      table.index(['payment_method_id']);
      table.index(['validation_id']);
      table.index(['approved_by']);
    });
    
    console.log('✅ Created transactions table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table transactions exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM transactions');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('client_id', 50).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('product_id')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('product_id', 30).notNullable();
    });
    console.log('✅ Added product_id field');
  }
  
  if (!existingColumns.includes('trans_type')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.enum('trans_type', ['PUSH', 'PULL', 'SWAP', 'BANK_DEPOSIT']).nullable();
    });
    console.log('✅ Added trans_type field');
  }
  
  if (!existingColumns.includes('trans_id')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('trans_id', 255).notNullable().unique();
    });
    console.log('✅ Added trans_id field');
  }
  
  if (!existingColumns.includes('reference_id')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('reference_id', 80).nullable();
    });
    console.log('✅ Added reference_id field');
  }
  
  if (!existingColumns.includes('stellar_tx_id')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('stellar_tx_id', 255).nullable().unique();
    });
    console.log('✅ Added stellar_tx_id field');
  }
  
  if (!existingColumns.includes('req_amount')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.decimal('req_amount', 10, 2).notNullable();
    });
    console.log('✅ Added req_amount field');
  }
  
  if (!existingColumns.includes('amount')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.decimal('amount', 18, 2).notNullable();
    });
    console.log('✅ Added amount field');
  }
  
  if (!existingColumns.includes('asset_code')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('asset_code', 50).defaultTo('UGX');
    });
    console.log('✅ Added asset_code field');
  }
  
  if (!existingColumns.includes('currency')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('currency', 10).notNullable();
    });
    console.log('✅ Added currency field');
  }
  
  if (!existingColumns.includes('sender_account')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('sender_account', 255).nullable();
    });
    console.log('✅ Added sender_account field');
  }
  
  if (!existingColumns.includes('receiver_account')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('receiver_account', 255).notNullable();
    });
    console.log('✅ Added receiver_account field');
  }
  
  if (!existingColumns.includes('memo')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.text('memo').nullable();
    });
    console.log('✅ Added memo field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.enum('status', ['PENDING', 'PENDING_APPROVAL', 'MINT_FAILED', 'FAILED', 'SUCCESS', 'INITIATED', 'RECEIVED', 'ONHOLD', 'EXPIRED', 'REVERSED', 'PENDING_REVERSAL']).defaultTo('PENDING');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('system_status')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.enum('system_status', ['PENDING', 'MINT_FAILED', 'failed', 'MINT_INITIATED', 'SUCCESS', 'INITIATED', 'RECEIVED', 'ONHOLD', 'PAYOUT_FAILED', 'EXPIRED', 'REVERSED', 'PENDING_REVERSAL']).nullable();
    });
    console.log('✅ Added system_status field');
  }
  
  if (!existingColumns.includes('fee')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.decimal('fee', 10, 2).notNullable();
    });
    console.log('✅ Added fee field');
  }
  
  if (!existingColumns.includes('provider_fees')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.decimal('provider_fees', 10, 2).nullable();
    });
    console.log('✅ Added provider_fees field');
  }
  
  if (!existingColumns.includes('service_name')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('service_name', 40).nullable();
    });
    console.log('✅ Added service_name field');
  }
  
  if (!existingColumns.includes('running_balance')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.decimal('running_balance', 10, 2).notNullable().defaultTo('0.00');
    });
    console.log('✅ Added running_balance field');
  }
  
  if (!existingColumns.includes('SessionId')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('SessionId', 50).nullable();
    });
    console.log('✅ Added SessionId field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('ext_reference')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('ext_reference', 60).nullable();
    });
    console.log('✅ Added ext_reference field');
  }
  
  if (!existingColumns.includes('payment_method_id')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('payment_method_id', 50).nullable();
    });
    console.log('✅ Added payment_method_id field');
  }
  
  if (!existingColumns.includes('validation_id')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('validation_id', 50).nullable();
    });
    console.log('✅ Added validation_id field');
  }
  
  if (!existingColumns.includes('provider_fee')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('provider_fee', 50).nullable();
    });
    console.log('✅ Added provider_fee field');
  }
  
  if (!existingColumns.includes('receive_currency')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('receive_currency', 30).nullable();
    });
    console.log('✅ Added receive_currency field');
  }
  
  if (!existingColumns.includes('approved_by')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('approved_by', 40).nullable();
    });
    console.log('✅ Added approved_by field');
  }
  
  if (!existingColumns.includes('message')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('message', 255).nullable();
    });
    console.log('✅ Added message field');
  }
  
  if (!existingColumns.includes('receiver_account_name')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.string('receiver_account_name', 90).nullable();
    });
    console.log('✅ Added receiver_account_name field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM transactions');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('transactions_client_id_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('transactions_product_id_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['product_id']);
    });
    console.log('✅ Added product_id index');
  }
  
  if (!existingIndexes.includes('transactions_trans_type_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['trans_type']);
    });
    console.log('✅ Added trans_type index');
  }
  
  if (!existingIndexes.includes('transactions_status_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('transactions_system_status_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['system_status']);
    });
    console.log('✅ Added system_status index');
  }
  
  if (!existingIndexes.includes('transactions_asset_code_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['asset_code']);
    });
    console.log('✅ Added asset_code index');
  }
  
  if (!existingIndexes.includes('transactions_currency_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['currency']);
    });
    console.log('✅ Added currency index');
  }
  
  if (!existingIndexes.includes('transactions_sender_account_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['sender_account']);
    });
    console.log('✅ Added sender_account index');
  }
  
  if (!existingIndexes.includes('transactions_receiver_account_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['receiver_account']);
    });
    console.log('✅ Added receiver_account index');
  }
  
  if (!existingIndexes.includes('transactions_service_name_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['service_name']);
    });
    console.log('✅ Added service_name index');
  }
  
  if (!existingIndexes.includes('transactions_sessionid_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['SessionId']);
    });
    console.log('✅ Added SessionId index');
  }
  
  if (!existingIndexes.includes('transactions_created_at_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  if (!existingIndexes.includes('transactions_ext_reference_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['ext_reference']);
    });
    console.log('✅ Added ext_reference index');
  }
  
  if (!existingIndexes.includes('transactions_payment_method_id_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['payment_method_id']);
    });
    console.log('✅ Added payment_method_id index');
  }
  
  if (!existingIndexes.includes('transactions_validation_id_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['validation_id']);
    });
    console.log('✅ Added validation_id index');
  }
  
  if (!existingIndexes.includes('transactions_approved_by_index')) {
    await knex.schema.alterTable('transactions', (table) => {
      table.index(['approved_by']);
    });
    console.log('✅ Added approved_by index');
  }
  
  // Check for unique constraints
  if (!existingIndexes.includes('validate_request_id')) {
    try {
      await knex.raw('ALTER TABLE transactions ADD UNIQUE KEY validate_request_id (trans_id)');
      console.log('✅ Added unique constraint on trans_id');
    } catch (error) {
      console.log('⚠️  Unique constraint on trans_id already exists or could not be added');
    }
  }
  
  if (!existingIndexes.includes('stellar_tx_id')) {
    try {
      await knex.raw('ALTER TABLE transactions ADD UNIQUE KEY stellar_tx_id (stellar_tx_id)');
      console.log('✅ Added unique constraint on stellar_tx_id');
    } catch (error) {
      console.log('⚠️  Unique constraint on stellar_tx_id already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for transactions table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 