import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('payout_requests');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('payout_requests', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.integer('transaction_id').notNullable();
      table.integer('client_id').notNullable();
      table.string('phone_number', 20).notNullable();
      table.decimal('amount', 18, 7).notNullable();
      table.enum('status', ['pending', 'processed', 'failed']).defaultTo('pending');
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('processed_at').nullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('payout_requests', (table) => {
      table.index(['transaction_id']);
      table.index(['client_id']);
      table.index(['phone_number']);
      table.index(['status']);
      table.index(['created_at']);
      table.index(['processed_at']);
    });
    
    console.log('✅ Created payout_requests table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table payout_requests exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM payout_requests');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('transaction_id')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.integer('transaction_id').notNullable();
    });
    console.log('✅ Added transaction_id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.integer('client_id').notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('phone_number')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.string('phone_number', 20).notNullable();
    });
    console.log('✅ Added phone_number field');
  }
  
  if (!existingColumns.includes('amount')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.decimal('amount', 18, 7).notNullable();
    });
    console.log('✅ Added amount field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.enum('status', ['pending', 'processed', 'failed']).defaultTo('pending');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('processed_at')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.timestamp('processed_at').nullable();
    });
    console.log('✅ Added processed_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM payout_requests');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('payout_requests_transaction_id_index')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.index(['transaction_id']);
    });
    console.log('✅ Added transaction_id index');
  }
  
  if (!existingIndexes.includes('payout_requests_client_id_index')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('payout_requests_phone_number_index')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.index(['phone_number']);
    });
    console.log('✅ Added phone_number index');
  }
  
  if (!existingIndexes.includes('payout_requests_status_index')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('payout_requests_created_at_index')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  if (!existingIndexes.includes('payout_requests_processed_at_index')) {
    await knex.schema.alterTable('payout_requests', (table) => {
      table.index(['processed_at']);
    });
    console.log('✅ Added processed_at index');
  }
  
  // Check for foreign key constraints
  try {
    const foreignKeys = await knex.raw(`
      SELECT CONSTRAINT_NAME 
      FROM information_schema.KEY_COLUMN_USAGE 
      WHERE TABLE_NAME = 'payout_requests' 
      AND REFERENCED_TABLE_NAME = 'transactions'
      AND REFERENCED_COLUMN_NAME = 'id'
    `);
    
    if (foreignKeys[0].length === 0) {
      await knex.raw(`
        ALTER TABLE payout_requests 
        ADD CONSTRAINT payout_requests_ibfk_1 
        FOREIGN KEY (transaction_id) 
        REFERENCES transactions(id) 
        ON DELETE CASCADE
      `);
      console.log('✅ Added foreign key constraint on transaction_id');
    } else {
      console.log('⚠️  Foreign key constraint on transaction_id already exists');
    }
  } catch (error) {
    console.log('⚠️  Foreign key constraint on transaction_id could not be added or already exists');
  }
  
  try {
    const foreignKeys = await knex.raw(`
      SELECT CONSTRAINT_NAME 
      FROM information_schema.KEY_COLUMN_USAGE 
      WHERE TABLE_NAME = 'payout_requests' 
      AND REFERENCED_TABLE_NAME = 'clients'
      AND REFERENCED_COLUMN_NAME = 'id'
    `);
    
    if (foreignKeys[0].length === 0) {
      await knex.raw(`
        ALTER TABLE payout_requests 
        ADD CONSTRAINT payout_requests_ibfk_2 
        FOREIGN KEY (client_id) 
        REFERENCES clients(id) 
        ON DELETE CASCADE
      `);
      console.log('✅ Added foreign key constraint on client_id');
    } else {
      console.log('⚠️  Foreign key constraint on client_id already exists');
    }
  } catch (error) {
    console.log('⚠️  Foreign key constraint on client_id could not be added or already exists');
  }
  
  console.log('✅ Field check complete for payout_requests table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 