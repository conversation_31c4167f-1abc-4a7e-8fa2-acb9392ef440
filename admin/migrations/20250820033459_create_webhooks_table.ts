import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('webhooks');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('webhooks', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('client_id', 250).notNullable();
      table.string('callback_url', 250).notNullable();
      table.timestamp('date').notNullable().defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('webhooks', (table) => {
      table.index(['client_id']);
      table.index(['callback_url']);
      table.index(['date']);
    });
    
    console.log('✅ Created webhooks table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table webhooks exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM webhooks');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('webhooks', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('webhooks', (table) => {
      table.string('client_id', 250).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('callback_url')) {
    await knex.schema.alterTable('webhooks', (table) => {
      table.string('callback_url', 250).notNullable();
    });
    console.log('✅ Added callback_url field');
  }
  
  if (!existingColumns.includes('date')) {
    await knex.schema.alterTable('webhooks', (table) => {
      table.timestamp('date').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added date field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM webhooks');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('webhooks_client_id_index')) {
    await knex.schema.alterTable('webhooks', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('webhooks_callback_url_index')) {
    await knex.schema.alterTable('webhooks', (table) => {
      table.index(['callback_url']);
    });
    console.log('✅ Added callback_url index');
  }
  
  if (!existingIndexes.includes('webhooks_date_index')) {
    await knex.schema.alterTable('webhooks', (table) => {
      table.index(['date']);
    });
    console.log('✅ Added date index');
  }
  
  console.log('✅ Field check complete for webhooks table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 