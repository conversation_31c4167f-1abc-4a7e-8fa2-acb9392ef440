import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('utilia_assets');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('utilia_assets', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('network', 30).notNullable();
      table.string('chain', 30).notNullable();
      table.string('currency', 30).notNullable();
      table.string('asset_code', 5).notNullable();
      table.string('asset', 250).notNullable();
      table.integer('is_muda_supported').notNullable();
      table.string('explorer_url', 255).nullable();
      table.timestamp('created_on').notNullable().defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.index(['network']);
      table.index(['chain']);
      table.index(['currency']);
      table.index(['asset_code']);
      table.index(['asset']);
      table.index(['is_muda_supported']);
      table.index(['created_on']);
    });
    
    console.log('✅ Created utilia_assets table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table utilia_assets exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM utilia_assets');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('network')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.string('network', 30).notNullable();
    });
    console.log('✅ Added network field');
  }
  
  if (!existingColumns.includes('chain')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.string('chain', 30).notNullable();
    });
    console.log('✅ Added chain field');
  }
  
  if (!existingColumns.includes('currency')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.string('currency', 30).notNullable();
    });
    console.log('✅ Added currency field');
  }
  
  if (!existingColumns.includes('asset_code')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.string('asset_code', 5).notNullable();
    });
    console.log('✅ Added asset_code field');
  }
  
  if (!existingColumns.includes('asset')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.string('asset', 250).notNullable();
    });
    console.log('✅ Added asset field');
  }
  
  if (!existingColumns.includes('is_muda_supported')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.integer('is_muda_supported').notNullable();
    });
    console.log('✅ Added is_muda_supported field');
  }
  
  if (!existingColumns.includes('explorer_url')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.string('explorer_url', 255).nullable();
    });
    console.log('✅ Added explorer_url field');
  }
  
  if (!existingColumns.includes('created_on')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.timestamp('created_on').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_on field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM utilia_assets');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('utilia_assets_network_index')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.index(['network']);
    });
    console.log('✅ Added network index');
  }
  
  if (!existingIndexes.includes('utilia_assets_chain_index')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.index(['chain']);
    });
    console.log('✅ Added chain index');
  }
  
  if (!existingIndexes.includes('utilia_assets_currency_index')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.index(['currency']);
    });
    console.log('✅ Added currency index');
  }
  
  if (!existingIndexes.includes('utilia_assets_asset_code_index')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.index(['asset_code']);
    });
    console.log('✅ Added asset_code index');
  }
  
  if (!existingIndexes.includes('utilia_assets_asset_index')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.index(['asset']);
    });
    console.log('✅ Added asset index');
  }
  
  if (!existingIndexes.includes('utilia_assets_is_muda_supported_index')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.index(['is_muda_supported']);
    });
    console.log('✅ Added is_muda_supported index');
  }
  
  if (!existingIndexes.includes('utilia_assets_created_on_index')) {
    await knex.schema.alterTable('utilia_assets', (table) => {
      table.index(['created_on']);
    });
    console.log('✅ Added created_on index');
  }
  
  console.log('✅ Field check complete for utilia_assets table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 