import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('export_files');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('export_files', (table) => {
      table.string('id', 36).primary(); // UUID varchar(36)
      table.enum('report_type', ['ADMIN', 'CLIENT']).notNullable().defaultTo('ADMIN'); // User type
      table.string('label', 255).nullable(); // Optional label for the export
      table.string('file_name', 255).notNullable();
      table.string('file_key', 500).notNullable(); // S3 key
      table.string('file_url', 1000).notNullable(); // S3 URL
      table.string('file_type', 50).notNullable(); // excel or pdf
      table.string('mime_type', 100).notNullable();
      table.string('client_id', 50).nullable();
      table.string('export_type', 100).nullable(); // transactions, reconciliation, etc.
      table.string('created_by', 100).nullable(); // User who created the export
      table.integer('total_records').defaultTo(0);
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('expires_at').notNullable(); // 6 hours from creation
      table.boolean('deleted').defaultTo(false);
      table.enum('status', ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED']).defaultTo('PENDING'); // Job status
      table.text('error_message').nullable(); // Error message if failed
      table.json('filters').nullable(); // Store export filters/parameters
      table.timestamp('completed_at').nullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('export_files', (table) => {
      table.index(['report_type']);
      table.index(['client_id']);
      table.index(['export_type']);
      table.index(['created_by']);
      table.index(['created_at']);
      table.index(['expires_at']);
      table.index(['deleted']);
      table.index(['file_key']);
      table.index(['status']);
    });
    
    console.log('✅ Created export_files table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table export_files exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM export_files');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.string('id', 36).primary();
    });
    console.log('✅ Added id field (UUID)');
  }
  
  // Check if old 'type' column exists and needs to be renamed to 'report_type'
  if (existingColumns.includes('type') && !existingColumns.includes('report_type')) {
    // Use raw query to rename column (renameColumn may not work for ENUM)
    await knex.raw(`ALTER TABLE export_files CHANGE COLUMN \`type\` \`report_type\` ENUM('ADMIN', 'CLIENT') NOT NULL DEFAULT 'ADMIN'`);
    console.log('✅ Renamed type column to report_type');
  }
  
  if (!existingColumns.includes('report_type')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.enum('report_type', ['ADMIN', 'CLIENT']).notNullable().defaultTo('ADMIN');
    });
    console.log('✅ Added report_type field');
  }
  
  if (!existingColumns.includes('label')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.string('label', 255).nullable();
    });
    console.log('✅ Added label field');
  }
  
  if (!existingColumns.includes('file_name')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.string('file_name', 255).notNullable();
    });
    console.log('✅ Added file_name field');
  }
  
  if (!existingColumns.includes('file_key')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.string('file_key', 500).notNullable();
    });
    console.log('✅ Added file_key field');
  }
  
  if (!existingColumns.includes('file_url')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.string('file_url', 1000).notNullable();
    });
    console.log('✅ Added file_url field');
  }
  
  if (!existingColumns.includes('file_type')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.string('file_type', 50).notNullable();
    });
    console.log('✅ Added file_type field');
  }
  
  if (!existingColumns.includes('mime_type')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.string('mime_type', 100).notNullable();
    });
    console.log('✅ Added mime_type field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.string('client_id', 50).nullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('export_type')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.string('export_type', 100).nullable();
    });
    console.log('✅ Added export_type field');
  }
  
  if (!existingColumns.includes('created_by')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.string('created_by', 100).nullable();
    });
    console.log('✅ Added created_by field');
  }
  
  if (!existingColumns.includes('total_records')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.integer('total_records').defaultTo(0);
    });
    console.log('✅ Added total_records field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('expires_at')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.timestamp('expires_at').notNullable();
    });
    console.log('✅ Added expires_at field');
  }
  
  if (!existingColumns.includes('deleted')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.boolean('deleted').defaultTo(false);
    });
    console.log('✅ Added deleted field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.enum('status', ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED']).defaultTo('PENDING');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('error_message')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.text('error_message').nullable();
    });
    console.log('✅ Added error_message field');
  }
  
  if (!existingColumns.includes('filters')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.json('filters').nullable();
    });
    console.log('✅ Added filters field');
  }
  
  if (!existingColumns.includes('completed_at')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.timestamp('completed_at').nullable();
    });
    console.log('✅ Added completed_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM export_files');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('export_files_report_type_index')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.index(['report_type']);
    });
    console.log('✅ Added report_type index');
  }
  
  if (!existingIndexes.includes('export_files_client_id_index')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('export_files_export_type_index')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.index(['export_type']);
    });
    console.log('✅ Added export_type index');
  }
  
  if (!existingIndexes.includes('export_files_created_by_index')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.index(['created_by']);
    });
    console.log('✅ Added created_by index');
  }
  
  if (!existingIndexes.includes('export_files_created_at_index')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  if (!existingIndexes.includes('export_files_expires_at_index')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.index(['expires_at']);
    });
    console.log('✅ Added expires_at index');
  }
  
  if (!existingIndexes.includes('export_files_deleted_index')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.index(['deleted']);
    });
    console.log('✅ Added deleted index');
  }
  
  if (!existingIndexes.includes('export_files_file_key_index')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.index(['file_key']);
    });
    console.log('✅ Added file_key index');
  }
  
  if (!existingIndexes.includes('export_files_status_index')) {
    await knex.schema.alterTable('export_files', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  console.log('✅ Field check complete for export_files table');
}

export async function down(knex: Knex): Promise<void> {
  // Drop the table if it exists
  const tableExists = await knex.schema.hasTable('export_files');
  
  if (tableExists) {
    await knex.schema.dropTable('export_files');
    console.log('✅ Dropped export_files table');
  } else {
    console.log('⚠️  export_files table does not exist, nothing to drop');
  }
}

