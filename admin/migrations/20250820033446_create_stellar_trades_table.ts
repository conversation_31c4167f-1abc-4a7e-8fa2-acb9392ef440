import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('stellar_trades');
  
  if (!tableExists) {
    // Create the table with fields inferred from ALTER statements
    await knex.schema.createTable('stellar_trades', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('trade_id', 255).notNullable().unique();
      table.string('maker_id', 255).notNullable(); // Maker of the trade
      table.string('taker_id', 255).notNullable(); // Taker of the trade
      table.string('order_id', 255).notNullable(); // Reference to stellar_orders
      table.timestamp('created_at').defaultTo(knex.fn.now());
      
      // Add foreign key constraint
      table.foreign('order_id').references('order_id').inTable('stellar_orders');
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('stellar_trades', (table) => {
      table.index(['maker_id'], 'idx_maker_trades');
      table.index(['taker_id'], 'idx_taker_trades');
      table.index(['order_id'], 'idx_order_trades');
      table.index(['created_at'], 'idx_created_at');
    });
    
    console.log('✅ Created stellar_trades table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table stellar_trades exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM stellar_trades');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('stellar_trades', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('trade_id')) {
    await knex.schema.alterTable('stellar_trades', (table) => {
      table.string('trade_id', 255).notNullable().unique();
    });
    console.log('✅ Added trade_id field');
  }
  
  if (!existingColumns.includes('maker_id')) {
    await knex.schema.alterTable('stellar_trades', (table) => {
      table.string('maker_id', 255).notNullable();
    });
    console.log('✅ Added maker_id field');
  }
  
  if (!existingColumns.includes('taker_id')) {
    await knex.schema.alterTable('stellar_trades', (table) => {
      table.string('taker_id', 255).notNullable();
    });
    console.log('✅ Added taker_id field');
  }
  
  if (!existingColumns.includes('order_id')) {
    await knex.schema.alterTable('stellar_trades', (table) => {
      table.string('order_id', 255).notNullable();
    });
    console.log('✅ Added order_id field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('stellar_trades', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM stellar_trades');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name.toLowerCase());
  
  if (!existingIndexes.includes('idx_maker_trades')) {
    await knex.schema.alterTable('stellar_trades', (table) => {
      table.index(['maker_id'], 'idx_maker_trades');
    });
    console.log('✅ Added idx_maker_trades index');
  }
  
  if (!existingIndexes.includes('idx_taker_trades')) {
    await knex.schema.alterTable('stellar_trades', (table) => {
      table.index(['taker_id'], 'idx_taker_trades');
    });
    console.log('✅ Added idx_taker_trades index');
  }
  
  if (!existingIndexes.includes('idx_order_trades')) {
    await knex.schema.alterTable('stellar_trades', (table) => {
      table.index(['order_id'], 'idx_order_trades');
    });
    console.log('✅ Added idx_order_trades index');
  }
  
  if (!existingIndexes.includes('idx_created_at')) {
    await knex.schema.alterTable('stellar_trades', (table) => {
      table.index(['created_at'], 'idx_created_at');
    });
    console.log('✅ Added idx_created_at index');
  }
  
  // Check for unique constraint on trade_id
  if (!existingIndexes.includes('trade_id')) {
    try {
      await knex.raw('ALTER TABLE stellar_trades ADD UNIQUE KEY trade_id (trade_id)');
      console.log('✅ Added unique constraint on trade_id');
    } catch (error) {
      console.log('⚠️  Unique constraint on trade_id already exists or could not be added');
    }
  }
  
  // Check for foreign key constraint on order_id
  const foreignKeys = await knex.raw(`
    SELECT CONSTRAINT_NAME 
    FROM information_schema.KEY_COLUMN_USAGE 
    WHERE TABLE_NAME = 'stellar_trades' 
    AND REFERENCED_TABLE_NAME = 'stellar_orders'
    AND REFERENCED_COLUMN_NAME = 'order_id'
  `);
  
  if (foreignKeys[0].length === 0) {
    try {
      await knex.raw('ALTER TABLE stellar_trades ADD CONSTRAINT stellar_trades_ibfk_1 FOREIGN KEY (order_id) REFERENCES stellar_orders (order_id)');
      console.log('✅ Added foreign key constraint on order_id');
    } catch (error) {
      console.log('⚠️  Foreign key constraint on order_id already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for stellar_trades table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 