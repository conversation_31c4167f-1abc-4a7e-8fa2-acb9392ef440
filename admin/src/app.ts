import express, { Request, Response } from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';
import expressFileUpload from 'express-fileupload';
import account from './controllers/accounts'; 
import admin from './controllers/admin'; 
import media from './controllers/media';
import CronService from './helpers/cron';
dotenv.config();

// Initialize cron service
const cronService = new CronService();
console.log('Cron service initialized');



// Ensure exports directory exists with write permissions
const exportsDir = path.join(process.cwd(), 'exports');
if (!fs.existsSync(exportsDir)) {
  fs.mkdirSync(exportsDir, { recursive: true, mode: 0o755 });
  console.log(`Created exports directory: ${exportsDir}`);
} else {
  // Ensure write permissions even if directory already exists
  try {
    fs.chmodSync(exportsDir, 0o755);
    console.log(`Exports directory exists with proper permissions: ${exportsDir}`);
  } catch (error) {
    console.warn(`Could not set permissions on exports directory: ${error}`);
  }
}

const app = express();
const PORT = process.env.PORT || 3005; // Default to 3000 if PORT is not in environment

app.use(cors());
app.use(expressFileUpload({}) as unknown as express.RequestHandler); // Initialize express-fileupload with proper type casting
app.use(bodyParser.json());
app.use(express.json({ limit: '50mb' })); // Increase JSON payload limit to 50 MB
app.use(express.urlencoded({ limit: '50mb', extended: true })); // Increase URL-encoded payload limit
app.set('trust proxy', true);

// Using the routes
app.use('/clients', account);
app.use('/admin', admin);
app.use('/media', media);

app.use('/health', (req: Request, res: Response) => {
  res.status(200).json({ status: 'ok', service: 'client-admin' });
});

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
