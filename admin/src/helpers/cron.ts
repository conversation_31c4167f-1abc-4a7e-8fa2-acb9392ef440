import * as cron from 'node-cron';

import Model from './model';
import WalletService from '../services/wallet.service';
import ExportHelper from "../helpers/export.helper";
import path from 'path';

class CronService {
    constructor() {
        console.log("Admin Cron Service initiated.");
        this.scheduleEverySixHours();
        this.scheduleEveryTwentyMins();
    }

    private scheduleEverySixHours() {
        cron.schedule('0 */6 * * *', async () => {
            console.log('Running every six hours admin task...');
            try {
                console.log('Every six hours admin task completed.');
            } catch (error) {
                console.error('Error running every six hours admin task:', error);
            }
        });
    }


    // run evety 20 mins
    private scheduleEveryTwentyMins() {
        // run after 30 mins of every hour
        // 20 * * * *
        cron.schedule('20 * * * *', async () => {
            // folder export is in root folder  
            const exportsDir = path.join(process.cwd(), 'exports');
            ExportHelper.autoDeleteOldExports(exportsDir);
            console.log('Running every twenty mins admin task...');
        });
    }
}