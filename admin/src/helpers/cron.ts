import * as cron from 'node-cron';

import Model from './model';
import WalletService from '../services/wallet.service';
import ExportHelper from "../helpers/export.helper";
import ExportJobService from '../services/exportJob.service';
import path from 'path';

class CronService {
    private exportJobService: ExportJobService;
    
    constructor() {
        console.log("Admin Cron Service initiated.");
        this.exportJobService = new ExportJobService();
        this.scheduleEverySixHours();
        this.scheduleEveryTwentyMins();
        this.scheduleS3Cleanup();
        this.scheduleExportJobs();
        this.scheduleResetStuckJobs();
    }

    private scheduleEverySixHours() {
        cron.schedule('0 */6 * * *', async () => {
            console.log('Running every six hours admin task...');
            try {
                console.log('Every six hours admin task completed.');
            } catch (error) {
                console.error('Error running every six hours admin task:', error);
            }
        });
    }


    // run evety 20 mins
    private scheduleEveryTwentyMins() {
        // run after 30 mins of every hour
        // 20 * * * *
        cron.schedule('20 * * * *', async () => {
            // folder export is in root folder  
            const exportsDir = path.join(process.cwd(), 'exports');
            ExportHelper.autoDeleteOldExports(exportsDir);
            console.log('Running every twenty mins admin task...');
        });
    }

    // Schedule S3 cleanup every hour
    private scheduleS3Cleanup() {
        cron.schedule('0 * * * *', async () => {
            console.log('Running S3 export files cleanup task...');
            try {
                const model = new Model();
                await ExportHelper.deleteExpiredS3Exports(model);
                console.log('S3 export files cleanup completed.');
            } catch (error) {
                console.error('Error running S3 export files cleanup:', error);
            }
        });
    }

    // Process export jobs every 2 minutes
    private scheduleExportJobs() {
        cron.schedule('*/2 * * * *', async () => {
            console.log('Processing export jobs...');
            try {
                const pendingJobs = await this.exportJobService.getPendingJobs(5);
                
                if (pendingJobs.length === 0) {
                    console.log('No pending export jobs found.');
                    return;
                }

                console.log(`Found ${pendingJobs.length} pending export job(s).`);

                // Process jobs in parallel
                const processPromises = pendingJobs.map(async (job: any) => {
                    try {
                        // Parse filters if it's a string
                        const filters = typeof job.filters === 'string' 
                            ? JSON.parse(job.filters) 
                            : job.filters;

                        await this.exportJobService.processExportJob({
                            id: job.id,
                            file_name: job.file_name,
                            file_type: job.file_type,
                            export_type: job.export_type,
                            filters: filters,
                            total_records: 0,
                            client_id: job.client_id,
                            report_type: job.report_type,
                            created_by: job.created_by
                        });
                    } catch (error: any) {
                        console.error(`Failed to process export job #${job.id}:`, error.message);
                    }
                });

                await Promise.all(processPromises);
                console.log('Export jobs processing completed.');
            } catch (error) {
                console.error('Error running export jobs processing:', error);
            }
        });
    }

    // Reset stuck jobs every hour
    private scheduleResetStuckJobs() {
        cron.schedule('5 * * * *', async () => {
            console.log('Checking for stuck export jobs...');
            try {
                await this.exportJobService.resetStuckJobs();
                console.log('Stuck jobs reset completed.');
            } catch (error) {
                console.error('Error resetting stuck jobs:', error);
            }
        });
    }
}

export default CronService;