import ExcelJS from 'exceljs';
import PDFDocument from 'pdfkit';
import { Buffer } from 'buffer';
import fs from 'fs';
import path from 'path';

export default class ExportHelper {
  
  /**
   * Export data to Excel file
   */
  static async exportToExcel(data: any[], filename: string, sheetName: string = 'Data'): Promise<{ buffer: Buffer; filename: string; mime_type: string }> {
    try {
      console.log('Starting Excel generation for:', filename);
      console.log('Data length:', data.length);
      
      // Validate input data
      if (!this.validateExportData(data)) {
        throw new Error('Invalid export data provided');
      }
      
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(sheetName);

      // If no data, return empty workbook with headers
      if (!data || data.length === 0) {
        console.log('No data provided, creating empty workbook with sample headers');
        
        // Add sample headers to show structure
        const sampleHeaders = ['No Data Available', 'Generated On', 'Total Records'];
        const headerRow = worksheet.addRow(sampleHeaders);
        headerRow.font = { bold: true, color: { argb: 'FF000000' } }; // Black text
        headerRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE8F4FD' } // Light blue background
        };
        headerRow.alignment = { vertical: 'middle', horizontal: 'center' };
        
        // Add info row with black text
        const infoRow = worksheet.addRow(['No transactions found', new Date().toLocaleString(), '0']);
        infoRow.font = { color: { argb: 'FF000000' } }; // Black text
        
        const buffer = await workbook.xlsx.writeBuffer();
        return {
          buffer: Buffer.from(buffer),
          filename: `${filename}.xlsx`,
          mime_type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        };
      }

      // Get headers from first object
      const headers = Object.keys(data[0]);
      console.log('Excel headers:', headers);
      
      if (headers.length === 0) {
        console.error('No headers found in data');
        throw new Error('No data structure found');
      }
      
      // Add headers with improved styling
      const headerRow = worksheet.addRow(headers);
      headerRow.font = { bold: true, color: { argb: 'FF000000' } }; // Black text
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE8F4FD' } // Light blue background
      };
      headerRow.alignment = { vertical: 'middle', horizontal: 'center' };

      // Add data rows with black text
      data.forEach((row, index) => {
        const values = headers.map(header => {
          const value = row[header];
          // Handle null/undefined values
          if (value === null || value === undefined) return '';
          // Convert dates to readable format
          if (value instanceof Date) return value.toLocaleDateString();
          // Convert numbers to strings
          return String(value);
        });
        const dataRow = worksheet.addRow(values);
        // Set black text color for all data cells
        dataRow.eachCell((cell) => {
          cell.font = { color: { argb: 'FF000000' } }; // Black text
        });
      });

      // Auto-fit columns with minimum width
      worksheet.columns.forEach(column => {
        if (column.header) {
          const maxLength = Math.max(
            column.header.length,
            ...(column.values?.slice(1)?.map(v => String(v || '').length) || [])
          );
          column.width = Math.max(maxLength + 2, 15); // Minimum width of 15
        }
      });

      // Add borders to all cells
      worksheet.eachRow((row, rowNumber) => {
        row.eachCell((cell, colNumber) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      });

      console.log('Generating Excel buffer...');
      const buffer = await workbook.xlsx.writeBuffer();
      console.log('Excel buffer generated, size:', buffer.byteLength);
      
      // Validate buffer
      if (!buffer || buffer.byteLength === 0) {
        throw new Error('Generated Excel buffer is empty or invalid');
      }
      
      // Ensure we have a proper Buffer
      let finalBuffer;
      if (Buffer.isBuffer(buffer)) {
        finalBuffer = buffer;
      } else if (buffer instanceof Uint8Array) {
        finalBuffer = Buffer.from(buffer);
      } else {
        finalBuffer = Buffer.from(buffer);
      }
      
      console.log('Final buffer size:', finalBuffer.length);
      console.log('Buffer type:', typeof finalBuffer);
      console.log('Is Buffer:', Buffer.isBuffer(finalBuffer));
      
      return {
        buffer: finalBuffer,
        filename: `${filename}.xlsx`,
        mime_type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      };
    } catch (error: any) {
      console.error('Excel generation error:', error);
      throw new Error(`Failed to generate Excel file: ${error?.message || 'Unknown error'}`);
    }
  }

  /**
   * Export data to PDF file - FIXED VERSION
   */
  static async exportToPDF(data: any[], filename: string, title: string = 'Export Data'): Promise<{ buffer: Buffer; filename: string; mime_type: string }> {
    return new Promise((resolve, reject) => {
      try {
        console.log('Starting PDF generation for:', filename);
        console.log('Data length:', data.length);
        console.log('Sample data:', data.slice(0, 2));
        
        // Validate input data
        if (!this.validateExportData(data)) {
          reject(new Error('Invalid export data provided'));
          return;
        }
        
        const doc = new PDFDocument({ 
          margin: 50,
          size: 'A4',
          layout: 'portrait'
        });

        const chunks: Buffer[] = [];

        doc.on('data', (chunk) => {
          console.log('PDF chunk received, size:', chunk.length);
          chunks.push(Buffer.from(chunk));
        });
        
        doc.on('end', () => {
          try {
            const buffer = Buffer.concat(chunks);
            console.log('PDF generation completed, buffer size:', buffer.length);
            
            // Validate buffer
            if (buffer.length === 0) {
              reject(new Error('Generated PDF buffer is empty'));
              return;
            }
            
            resolve({
              buffer,
              filename: `${filename}.pdf`,
              mime_type: 'application/pdf'
            });
          } catch (error: any) {
            reject(new Error(`Failed to create PDF buffer: ${error?.message || 'Unknown error'}`));
          }
        });

        doc.on('error', (error: any) => {
          console.error('PDF generation error event:', error);
          reject(new Error(`PDF generation failed: ${error?.message || 'Unknown error'}`));
        });

        // Set document properties
        doc.info.Title = title;
        doc.info.CreationDate = new Date();

        // Add beautiful title with styling
        doc.fontSize(28).font('Helvetica-Bold').fillColor('#2E86AB').text(title, { align: 'center' });
        doc.moveDown(0.5);
        
        // Add decorative line under title
        doc.strokeColor('#2E86AB').lineWidth(2);
        doc.moveTo(100, doc.y).lineTo(495, doc.y).stroke();
        doc.moveDown(1);

        // Add export info with better styling
        doc.fontSize(12).font('Helvetica').fillColor('#000000').text(`Generated on: ${new Date().toLocaleString()}`, { align: 'left' });
        doc.text(`Total Records: ${data.length}`, { align: 'left' });
        doc.moveDown(2);

        if (!data || data.length === 0) {
          doc.fontSize(16).fillColor('#000000').text('No Data Available for Export', { align: 'center' });
          doc.moveDown(1);
          doc.fontSize(12).fillColor('#000000').text(`Generated on: ${new Date().toLocaleString()}`, { align: 'center' });
          doc.text('Total Records: 0', { align: 'center' });
          doc.moveDown(2);
          doc.fontSize(14).fillColor('#000000').text('No transactions or sessions found matching the specified criteria.', { align: 'center' });
          doc.end();
          return;
        }

        // Get headers from first object
        const headers = Object.keys(data[0]);
        console.log('PDF headers:', headers);
        console.log('First row data:', data[0]);
        
        if (headers.length === 0) {
          console.error('No headers found in data');
          doc.fontSize(14).fillColor('#000000').text('Error: No data structure found.', { align: 'center' });
          doc.end();
          return;
        }
        
        // Calculate table dimensions - ensure columns fit on page
        const pageWidth = 495; // A4 width minus margins (595 - 100)
        const columnWidth = Math.min(pageWidth / headers.length, 80); // Max column width
        const rowHeight = 25;
        const startX = 50;
        let currentY = doc.y + 20;

        console.log('Table dimensions:', { pageWidth, columnWidth, rowHeight, startX, currentY });

        // Add table headers with improved styling
        doc.fontSize(10).font('Helvetica-Bold').fillColor('#000000');
        headers.forEach((header, index) => {
          const x = startX + (index * columnWidth);
          
          // Draw header cell with better background color
          doc.rect(x, currentY, columnWidth, rowHeight).fillAndStroke('#E8F4FD', '#2E86AB');
          
          // Add header text in black
          doc.text(header, x + 5, currentY + 8, { 
            width: columnWidth - 10,
            align: 'center'
          });
        });

        currentY += rowHeight;

        // Add data rows
        data.forEach((row, rowIndex) => {
          console.log(`Processing row ${rowIndex}:`, row);
          
          // Check if we need a new page
          if (currentY > 700) {
            console.log('Adding new page at Y:', currentY);
            doc.addPage();
            currentY = 50;
          }

          doc.fontSize(9).font('Helvetica').fillColor('#000000');
          headers.forEach((header, colIndex) => {
            const x = startX + (colIndex * columnWidth);
            const value = String(row[header] || '');
            
            // Draw cell border
            doc.rect(x, currentY, columnWidth, rowHeight).stroke('#2E86AB');
            
            // Add text with word wrapping in black
            doc.text(value, x + 5, currentY + 8, { 
              width: columnWidth - 10,
              height: rowHeight - 10
            });
          });

          currentY += rowHeight;
        });

        console.log('Finalizing PDF at Y position:', currentY);
        doc.end();
      } catch (error: any) {
        console.error('PDF generation error:', error);
        reject(new Error(`PDF generation failed: ${error?.message || 'Unknown error'}`));
      }
    });
  }

  /**
   * Format transaction data for export (all columns)
   */
  static formatTransactionData(transactions: any[]): any[] {
    if (!Array.isArray(transactions)) {
      console.warn('formatTransactionData: transactions is not an array:', transactions);
      return [];
    }

    return transactions.map((t, index) => {
      try {
        // Helper function to format date
        const formatDate = (dateValue: any): string => {
          if (!dateValue) return '';
          try {
            const date = new Date(dateValue);
            if (!isNaN(date.getTime())) {
              return date.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              });
            }
          } catch (e) {
            return String(dateValue);
          }
          return '';
        };

        // Helper function to safely get value
        const getValue = (value: any): string => {
          if (value === null || value === undefined) return '';
          return String(value);
        };

        return {
          'Trans ID': getValue(t.trans_id),
          'Client ID': getValue(t.client_id),
          'Product ID': getValue(t.product_id),
          'Trans Type': getValue(t.trans_type),
          'Reference ID': getValue(t.reference_id),
          'Stellar TX ID': getValue(t.stellar_tx_id),
          'Req Amount': getValue(t.req_amount),
          'Amount': getValue(t.amount),
          'Asset Code': getValue(t.asset_code),
          'Currency': getValue(t.currency),
          'Sender Account': getValue(t.sender_account),
          'Receiver Account': getValue(t.receiver_account),
          'Memo': getValue(t.memo),
          'Status': getValue(t.status),
          'System Status': getValue(t.system_status),
          'Fee': getValue(t.fee),
          'Provider Fees': getValue(t.provider_fees),
          'Service Name': getValue(t.service_name),
          'Running Balance': getValue(t.running_balance),
          'Session ID': getValue(t.SessionId),
          'Created At': formatDate(t.created_at),
          'Ext Reference': getValue(t.ext_reference),
          'Payment Method ID': getValue(t.payment_method_id),
          'Validation ID': getValue(t.validation_id),
          'Provider Fee': getValue(t.provider_fee),
          'Receive Currency': getValue(t.receive_currency),
          'Approved By': getValue(t.approved_by),
          'Message': getValue(t.message),
          'Receiver Account Name': getValue(t.receiver_account_name)
        };
      } catch (error) {
        console.error(`Error formatting transaction ${index}:`, error);
        // Return error row with all columns
        const errorRow: any = {};
        const columns = [
          'Client ID', 'Product ID', 'Trans Type', 'Trans ID',
          'Reference ID', 'Stellar TX ID', 'Req Amount', 'Amount', 'Asset Code',
          'Currency', 'Sender Account', 'Receiver Account', 'Memo', 'Status',
          'System Status', 'Fee', 'Provider Fees', 'Service Name', 'Running Balance',
          'Session ID', 'Created At', 'Ext Reference', 'Payment Method ID',
          'Validation ID', 'Provider Fee', 'Receive Currency', 'Approved By',
          'Message', 'Receiver Account Name'
        ];
        columns.forEach(col => errorRow[col] = 'Error');
        return errorRow;
      }
    });
  }


  /**
   * Validate export data
   */
  static validateExportData(data: any[]): boolean {
    if (!Array.isArray(data)) {
      console.error('Export data is not an array');
      return false;
    }
    
    if (data.length === 0) {
      console.warn('Export data is empty');
      return true; // Empty data is valid
    }
    
    if (typeof data[0] !== 'object' || data[0] === null) {
      console.error('Export data items are not objects');
      return false;
    }
    
    return true;
  }

  /**
   * Generate safe filename
   */
  static generateSafeFilename(baseName: string, extension: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const safeBaseName = baseName.replace(/[^a-zA-Z0-9_-]/g, '_');
    return `${safeBaseName}_${timestamp}.${extension}`;
  }

  /**
   * Auto delete old export files (PDF and Excel) that are older than specified minutes
   * @param exportsDir - Path to exports directory (defaults to './exports')
   * @param maxAgeMinutes - Maximum age in minutes (defaults to 15)
   */
  static async autoDeleteOldExports(exportsDir: string = './exports', maxAgeMinutes: number = 15): Promise<{ deleted: number; errors: number; files: string[] }> {
    try {
      console.log(`Starting cleanup of exports folder: ${exportsDir}`);
      
      // Check if directory exists
      if (!fs.existsSync(exportsDir)) {
        console.warn(`Exports directory does not exist: ${exportsDir}`);
        return { deleted: 0, errors: 0, files: [] };
      }

      // Get all files in the exports directory
      const files = fs.readdirSync(exportsDir);
      
      const now = Date.now();
      const maxAgeMs = maxAgeMinutes * 60 * 1000; // Convert minutes to milliseconds
      
      let deletedCount = 0;
      let errorCount = 0;
      const deletedFiles: string[] = [];

      for (const file of files) {
        // Only process PDF and Excel files
        if (!file.endsWith('.pdf') && !file.endsWith('.xlsx') && !file.endsWith('.xls')) {
          continue;
        }

        const filePath = path.join(exportsDir, file);

        try {
          // Get file stats
          const stats = fs.statSync(filePath);
          const fileAge = now - stats.mtimeMs; // Time since last modification

          // Check if file is older than maxAge
          if (fileAge > maxAgeMs) {
            // Delete the file
            fs.unlinkSync(filePath);
            deletedCount++;
            deletedFiles.push(file);
            console.log(`Deleted old export file: ${file} (age: ${Math.round(fileAge / 60000)} minutes)`);
          }
        } catch (error: any) {
          errorCount++;
          console.error(`Error processing file ${file}:`, error?.message || error);
        }
      }

      console.log(`Cleanup completed. Deleted: ${deletedCount}, Errors: ${errorCount}`);
      
      return {
        deleted: deletedCount,
        errors: errorCount,
        files: deletedFiles
      };
    } catch (error: any) {
      console.error('Error during exports cleanup:', error?.message || error);
      return { deleted: 0, errors: 1, files: [] };
    }
  }

  
}
