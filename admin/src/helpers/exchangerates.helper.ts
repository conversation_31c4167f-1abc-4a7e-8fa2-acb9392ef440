import axios from 'axios';
import * as dotenv from "dotenv";
dotenv.config();
export default class ExchangeRates {

    public async getRates(data: any) {
        try {
        
        let theRateValue: number | string = "";    
        let coinMarketCupRates = await this.getCoinMarketCupRates(data);
        
        theRateValue = coinMarketCupRates?.price;
        console.log("coinMarketCupRates 111", theRateValue);

        if( coinMarketCupRates?.price === undefined || coinMarketCupRates?.price === 0) {
          theRateValue = await this.getFiatRates(data);
          theRateValue = (Number(theRateValue) >= 0) ? theRateValue : "";
          console.log("coinMarketCupRates 11VVV", theRateValue);
        }

        return {price: theRateValue, status: true};

        } catch (error: any) {
            console.error("Token Error 1003", error);
            console.log("error sending email", error.message)
            return {price: 0, status: false, "message": error.message};
        }
    }


    
    public async getCoinMarketCupRates(data: any) {
        try {
        const url = `${process.env.COINMARKETCAP_URL}symbol=${data?.base}&convert=${data?.quote}`;
        const headers = { 
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'X-CMC_PRO_API_KEY': `${process.env.COINMARKETCAP_API_KEY}`
        }; 
        const response = await axios.get(url, { headers });
        return response?.data?.data?.[data?.base]?.quote?.[data?.quote];

        } catch (error: any) {
            console.error("Token Error 1003", error);
            console.log("error sending email", error.message)
            return {status: false,  "message": error.message}
        }
    }


    public async getExchangeRates(url: string, theheader: any) {
        try {
            const response = await axios.get(url, {
            headers: theheader
            });
            return response.data;
        } catch (error: any) {
            console.error("Error fetching exchange rates:", error);
            throw error;
        }
    }

    public async updateExchangeRates(url: string, data: any, theheader: any) {
        try {
            const response = await axios.post(url, data, {
            headers: theheader
            });
            return response.data;
        } catch (error: any) {
            console.error("Error updating exchange rates:", error);
            throw error;
        }
    }

    public async getFiatRates(data: any) {
        try {

            const url = `${process.env.FOREX_URL}${data?.quote_currency?.toLowerCase()}.json`;
            const headers = { 
                'Content-Type': 'application/json',
                'Accept': '*/*'
            }; 

            const response = await axios.get(url, { headers });
            let rateValue = response?.data?.[data?.quote_currency?.toLowerCase()]?.[data?.base_currency?.toLowerCase()];
            rateValue = (rateValue !== undefined && rateValue !== "") ? 1/Number(rateValue) : "";
            return rateValue;
        
        } catch (error: any) {
            console.error("Token Error 1003", error);
            console.log("error sending email", error.message);
            return { "message": error.message };
        }
    }
}  