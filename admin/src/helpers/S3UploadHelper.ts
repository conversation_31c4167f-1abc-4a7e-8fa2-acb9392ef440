import AWS from 'aws-sdk';

// Configure AWS S3 using environment variables
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
});

const s3 = new AWS.S3();

interface UploadResponse {
  url: string;
  key: string;
  thumbnail?: string;
  originalUrl?: string;
}


/**
 * Uploads a file to S3.
 * @param file - The file buffer and metadata to be uploaded.
 * @param folderName - The folder name in the S3 bucket.
 * @returns The S3 URL and key of the uploaded file.
 */
export const uploadToS3 = async (file: any, folderName: string, fileTitle:string=''): Promise<UploadResponse> => {
  try {
    console.log('FILE_INFO',file)
    // Generate a unique file name for the S3 bucket
    const fileName = file.name || ""
    const fileExtension = fileName.split('.').pop(); // Get file extension

    const randomFileName = fileTitle === '' 
    ? `${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExtension}` 
    : `${fileTitle}.${fileExtension}`;
  
    const params = {
      Bucket: process.env.AWS_S3_BUCKET_NAME as string,
      Key: `${folderName}/${randomFileName}`,
      Body: file.data,
      ContentType: file.mimetype
    };

    // Upload the file to S3
    const data = await s3.upload(params).promise();

    return { url: data.Location, key: data.Key };
  } catch (error) {
    console.error('Error uploading to S3:', error);
    throw new Error('Error uploading file to S3');
  }
};
