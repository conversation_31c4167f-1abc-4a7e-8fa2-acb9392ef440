/**
 * Date Helper Utility Functions
 * Provides consistent date handling across the application
 * 
 * Usage Examples:
 * 
 * // Get current date
 * const today = CURRENT_DATE(); // Returns "2024-01-15"
 * 
 * // Get current date and time
 * const now = CURRENT_DATETIME(); // Returns "2024-01-15T10:30:45.123Z"
 * 
 * // Get MySQL formatted date
 * const mysqlDate = getCurrentDateMySQL(); // Returns "2024-01-15"
 * 
 * // Get MySQL formatted datetime
 * const mysqlDateTime = getCurrentDateTimeMySQL(); // Returns "2024-01-15 10:30:45"
 * 
 * // Check if date is today
 * const isToday = isToday('2024-01-15'); // Returns true/false
 * 
 * // Get first day of last 6 months (updated function)
 * const sixMonthsAgo = getFirstDayOfCurrentMonth(); // Returns "2023-07-01" (if today is 2024-01-15)
 * 
 * // Get first day of current month
 * const thisMonth = getFirstDayOfThisMonth(); // Returns "2024-01-01"
 * 
 * // Get first day of specific months ago
 * const threeMonthsAgo = getFirstDayOfMonthsAgo(3); // Returns "2023-10-01"
 * 
 * // Get current month range
 * const monthRange = getCurrentMonthRange(); // Returns {start: "2024-01-01", end: "2024-02-01"}
 */

/**
 * Get current date in YYYY-MM-DD format
 */
export const CURRENT_DATE = (): string => {
  return new Date().toISOString().split('T')[0];
};

/**
 * Get current date and time in ISO format
 */
export const CURRENT_DATETIME = (): string => {
  return new Date().toISOString();
};

/**
 * Get current date in MySQL format (YYYY-MM-DD)
 */
export const getCurrentDateMySQL = (): string => {
  return new Date().toISOString().split('T')[0];
};

/**
 * Get current date and time in MySQL format (YYYY-MM-DD HH:MM:SS)
 */
export const getCurrentDateTimeMySQL = (): string => {
  const now = new Date();
  return now.toISOString().slice(0, 19).replace('T', ' ');
};

/**
 * Format date for MySQL queries (date only)
 * @param date - Date object or date string
 * @returns Formatted date string (YYYY-MM-DD)
 */
export const formatDateForMySQL = (date: Date | string): string => {
  const d = new Date(date);
  return d.toISOString().split('T')[0];
};

/**
 * Format datetime for MySQL queries
 * @param date - Date object or date string
 * @returns Formatted datetime string (YYYY-MM-DD HH:MM:SS)
 */
export const formatDateTimeForMySQL = (date: Date | string): string => {
  const d = new Date(date);
  return d.toISOString().slice(0, 19).replace('T', ' ');
};

/**
 * Get first day of the last 6 months in YYYY-MM-DD format
 */
export const getFirstDayOfCurrentMonth = (): string => {
  const now = new Date();
  // Subtract 6 months from current date
  const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 6, 1);
  return sixMonthsAgo.toISOString().split('T')[0];
};

/**
 * Get first day of current month in YYYY-MM-DD format
 */
export const getFirstDayOfThisMonth = (): string => {
  const now = new Date();
  return new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
};

/**
 * Get first day of N months ago in YYYY-MM-DD format
 * @param monthsAgo - Number of months to go back (default: 6)
 */
export const getFirstDayOfMonthsAgo = (monthsAgo: number = 6): string => {
  const now = new Date();
  const targetDate = new Date(now.getFullYear(), now.getMonth() - monthsAgo, 1);
  return targetDate.toISOString().split('T')[0];
};

/**
 * Get first day of next month in YYYY-MM-DD format
 */
export const getFirstDayOfNextMonth = (): string => {
  const now = new Date();
  return new Date(now.getFullYear(), now.getMonth() + 1, 1).toISOString().split('T')[0];
};

/**
 * Check if a date is today
 * @param date - Date to check
 * @returns boolean
 */
export const isToday = (date: Date | string): boolean => {
  const today = new Date().toISOString().split('T')[0];
  const checkDate = new Date(date).toISOString().split('T')[0];
  return today === checkDate;
};

/**
 * Get date range for current month
 * @returns Object with start and end dates
 */
export const getCurrentMonthRange = () => {
  return {
    start: getFirstDayOfCurrentMonth(),
    end: getFirstDayOfNextMonth()
  };
};

/**
 * Validate if date range is within the maximum allowed duration
 * @param startDate - Start date string
 * @param endDate - End date string
 * @param maxMonths - Maximum number of months allowed (default: 2)
 * @returns Object with isValid boolean and error message if invalid
 */
export const validateDateRange = (startDate?: string | null, endDate?: string | null, maxMonths: number = 2): { isValid: boolean; error?: string; start?: Date; end?: Date } => {
  if (!startDate || !endDate) {
    return { isValid: true }; // Allow if dates are not provided
  }

  const start = new Date(startDate);
  const end = new Date(endDate);

  // Check if dates are valid
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return {
      isValid: false,
      error: 'Invalid date format. Expected format: YYYY-MM-DD'
    };
  }

  // Check if start date is before end date
  if (start > end) {
    return {
      isValid: false,
      error: 'Start date must be before or equal to end date'
    };
  }

  // Calculate the difference in months
  const startYear = start.getFullYear();
  const startMonth = start.getMonth();
  const endYear = end.getFullYear();
  const endMonth = end.getMonth();
  
  // Calculate the number of months between dates
  // We count the number of month boundaries crossed + 1 to include both months
  // Example: Jan 1 to Mar 1 = (2024*12 + 2) - (2024*12 + 0) = 2 months (Jan + Feb + Mar, so 3 months inclusive)
  // But we want: Jan 1 to Mar 1 to count as crossing 2 boundaries (Jan-Feb, Feb-Mar), so 3 months total
  const monthsDifference = (endYear - startYear) * 12 + (endMonth - startMonth);
  const totalMonths = monthsDifference + 1; // +1 to include both boundary months
  
  if (totalMonths > maxMonths) {
    return {
      isValid: false,
      error: `Date range exceeds the maximum allowed duration of ${maxMonths} months. Please select a shorter range.`
    };
  }

  return {
    isValid: true,
    start,
    end
  };
};

/**
 * Calculate the maximum allowed date range (2 months from today)
 * @returns Object with minDate and maxDate
 */
export const getMaxExportDateRange = () => {
  const today = new Date();
  
  const twoMonthsAgo = new Date(today);
  twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 2);
  twoMonthsAgo.setDate(twoMonthsAgo.getDate() + 1); // Add 1 day to avoid 3-month boundary
  
  return {
    minDate: twoMonthsAgo.toISOString().split('T')[0],
    maxDate: today.toISOString().split('T')[0]
  };
}; 