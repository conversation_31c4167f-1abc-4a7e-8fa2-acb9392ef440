import Model from "../helpers/model";
import TwoF<PERSON><PERSON><PERSON><PERSON>elper from "../helpers/2fa.helper";
import EmailSender from "../helpers/email.helper";
import { v4 as uuidv4 } from "uuid";
import jwt from "jsonwebtoken";
import CryptoJS, { algo } from "crypto-js";
import { z } from "zod";
import dotenv from 'dotenv';
import StellarService from "../helpers/StellarService";  // Moved to wallet service
import { LiquidityRailService } from "../services/liquidityrail.service";
import WalletService from "../services/wallet.service";
import ExportHelper from "../helpers/export.helper";
import RatesService from "../helpers/exchangerates.helper";
import { formatDateForMySQL, getCurrentDateMySQL, getFirstDayOfCurrentMonth, isToday, getFirstDayOfNextMonth } from '../helpers/dateHelper';
import { object } from "zod/v4";

dotenv.config();
// import { createAddress, createWallet, listWalletsForVault, createWalletAddress } from "../intergrations/Utilia";  // Moved to wallet service
// Define CURRENT_DATE constant for consistent date handling
const CURRENT_DATE = new Date().toISOString().split('T')[0]; // Returns YYYY-MM-DD format

const SECRET_KEY = "";
const SECRET_KEY_CLIENT = process.env.SECRET_KEY || "";

const sendmail = new EmailSender()
const ratesService = new RatesService();
const liquidityRailService = new LiquidityRailService();

type User_ = {
  email: string;
  password: string;
  role: string;
  first_name: string;
  last_name: string;
};

type RoleUpdateData = {
  name: string;
  details?: string;
  status: 'active' | 'inactive';
  updated_at: string;
};

type UserData = {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  role: string;
  role_details: {
    id: string;
    name: string;
    details: string;
    status: string;
    access_rights: any[];
  };
  status: string;
  updated_at: string;
  created_at: string;
  menu_list: any[];
};
interface balanceInterface {
  balance: string;
  asset: string;
}

export default class Admin extends Model {

  async getTransactionLogs(trans_id: any) {
    const transaction: any = await this.callQuerySafe(`SELECT * FROM transactions WHERE trans_id = '${trans_id}' `);
    if (transaction.length == 0) {
      return this.makeResponse(404, "Transaction not found");
    }
    const logs = await this.callQuerySafe(`SELECT * FROM transactions_log WHERE trans_id = '${trans_id}' `);
    const webhookLogs = await this.callQuerySafe(`SELECT * FROM webhook_logs WHERE trans_id = '${trans_id}' `);
    return this.makeResponse(200, "retrieved successfully", { transaction: transaction[0], transactionLogs: logs, webhookLogs: webhookLogs });
  }

  async thirdpartyBalances() {
    const dummyBalances: balanceInterface[] = [
      {
        balance: "100",
        asset: "USD"
      },
      {
        balance: "100",
        asset: "UGX"
      },

    ]

    const accounts = ["quidax", "utilia", "muda", "pegasus_out", "pegasus_in"]
    const getQuidaxBalances = dummyBalances
    const getUtiliaBalances = dummyBalances
    const getMudaBalances = dummyBalances
    const getPegasusOutBalances = dummyBalances
    const getPegasusInBalances = dummyBalances
    return this.makeResponse(200, "Balances fetched successfully", {
      quidax: getQuidaxBalances,
      utilia: getUtiliaBalances,
      muda: getMudaBalances,
      pegasus_out: getPegasusOutBalances,
      pegasus_in: getPegasusInBalances
    });
  }

  async saveNetworks(chain: { name: any; displayName: any; caipDetails: any; testnet: any; nativeAsset: any; custom: any; }) {


    const network: any = await this.callQuerySafe(`select * from utilia_networks where name = '${chain.name}'`);
    if (network.length > 0) {
      return this.makeResponse(200, "Network already exists", network);
    }
    const response = await this.insertData("utilia_networks", chain);
    return this.makeResponse(200, "Network saved successfully", response);
  }
  private readonly TRANSACTION_FEES_TABLE = 'custome_fees';

  constructor() {
    super();
  }

  async issueTokens(data: any) {
    return this.makeResponse(401, "SUCCESS", data);
  }



  private getMySQLDateTime() {
    return new Date().toISOString().slice(0, 19).replace('T', ' ');
  }

  async getAssetHolders(data: any) {

    const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "assetHoldersView");
    if (!accessRight) {
      // return this.makeResponse(405, "You are not authorized to view asset holders", []);
    }

    // Get all active holders from Stellar
    const hodlers = await new StellarService().getAssetHolders(data?.currency.toUpperCase());
    const users: any = [];

    // Get all client wallets from database
    const allClientWallets: any = await this.callQuerySafe(`
      SELECT 
        w.public_key, 
        c.business_name, 
        c.client_id 
      FROM client_wallets w 
      INNER JOIN clients c ON w.client_id = c.client_id
    `);

    // Create map of existing funded holders
    const existingHolders = new Set(hodlers.map(h => h.accountId));

    // Process active holders from Stellar
    for (const holder of hodlers) {
      const publicKey = holder.accountId;
      const clientInfo = allClientWallets.find((w: any) => w.public_key === publicKey);
      users.push({
        wallet: holder,
        clientId: clientInfo?.client_id || null,
        businessName: clientInfo?.business_name || null
      });
    }

    // Add unfunded wallets that aren't in Stellar response
    for (const wallet of allClientWallets) {
      if (!existingHolders.has(wallet.public_key)) {
        users.push({
          wallet: {
            accountId: wallet.public_key,
            balance: "0.0000000", // Unfunded wallet
            cBalance: "0"
          },
          clientId: wallet.client_id,
          businessName: wallet.business_name
        });
      }
    }

    return this.makeResponse(200, "Holders fetched successfully", users);


  }

  async getAccessClients(id: any, data?: any) {
    try {
      const { page, size = 10, search = '' } = data || {};

      // Build search condition
      let searchCondition = '';
      if (search && search.trim() !== '') {
        const searchStr = search.trim().replace(/'/g, "''");
        searchCondition = `AND (
          cl.email LIKE '%${searchStr}%' OR 
          cl.first_name LIKE '%${searchStr}%' OR 
          cl.last_name LIKE '%${searchStr}%' OR
          cl.role LIKE '%${searchStr}%' OR
          rl.name LIKE '%${searchStr}%'
        )`;
      }

      // If page not provided, fetch ALL
      if (page === undefined || page === null) {
        const query = `
          SELECT cl.*, rl.name as role_name, rl.details as role_details 
          FROM client_logins cl
          LEFT JOIN roles rl ON cl.role = rl.id
          WHERE cl.client_id = '${id}' 
          ${searchCondition}
          ORDER BY cl.created_at DESC
        `;
        const clientLogins: any = await this.callQuerySafe(query);

        const defaultRole: any = await this.defaultRole();
        clientLogins.forEach((client: any) => {
          if (client.role === defaultRole?.id) {
            client.role_name = defaultRole.name;
            client.role_details = defaultRole.details;
          }
        });

        return this.makeResponse(200, "Access clients fetched successfully", clientLogins);
      }

      // Else: Paginated fetch
      const limit = parseInt(size) > 0 ? parseInt(size) : 10;
      const offset = (page - 1) * limit;

      const countQuery = `
        SELECT COUNT(*) as total 
        FROM client_logins cl 
        LEFT JOIN roles rl ON cl.role = rl.id 
        WHERE cl.client_id = '${id}' 
        ${searchCondition}
      `;
      const countResult: any = await this.callQuerySafe(countQuery);
      const totalItems = countResult[0]?.total || 0;

      const query = `
        SELECT cl.*, rl.name as role_name, rl.details as role_details 
        FROM client_logins cl
        LEFT JOIN roles rl ON cl.role = rl.id
        WHERE cl.client_id = '${id}' 
        ${searchCondition}
        ORDER BY cl.created_at DESC 
        LIMIT ${limit} OFFSET ${offset}
      `;
      const clientLogins: any = await this.callQuerySafe(query);

      const defaultRole: any = await this.defaultRole();
      clientLogins.forEach((client: any) => {
        if (client.role === defaultRole?.id) {
          client.role_name = defaultRole.name;
          client.role_details = defaultRole.details;
        }
      });

      const totalPages = Math.ceil(totalItems / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      const paginationInfo = {
        current_page: page,
        total_pages: totalPages,
        total_items: totalItems,
        items_per_page: limit,
        hasNextPage,
        hasPrevPage,
        next_page: hasNextPage ? Number(page) + 1 : null,
        previous_page: hasPrevPage ? page - 1 : null
      };

      return this.makeResponse(200, "Access clients fetched successfully", {
        items: clientLogins,
        pagination: paginationInfo
      });

    } catch (error: any) {
      console.error("Error fetching access clients:", error);
      return this.makeResponse(500, "Error fetching access clients");
    }
  }



  async getPendingWithdrawals(data: any) {

    try {
      // 1. Check access rights
      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "depositsView");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to view pending deposits");
      }

      const pendingDeposits1 = await this.callQuerySafe(` SELECT transactions.*, clients.business_name
                                                          FROM transactions
                                                          LEFT JOIN clients ON transactions.client_id = clients.client_id
                                                          WHERE transactions.system_status = 'PENDING_ADMIN_APPROVAL'
                                                          AND transactions.trans_type = 'PUSH' and transactions.status='ONHOLD'
                                                          ORDER BY transactions.created_at DESC`);
      return this.makeResponse(200, "Pending withdraw fetched successfully", pendingDeposits1);


    } catch (error: any) {
      console.error("Error fetching pending withdraws", error);
      return this.makeResponse(500, "Error fetching pending withdraws");
    }
  }


  async getPendingDeposits(data: any) {

    try {
      // 1. Check access rights
      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "depositsView");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to view pending deposits");
      }

      // if page is undefined, return all pending deposits  
      if (data?.page === undefined) {
        const pendingDeposits1 = await this.callQuerySafe(` SELECT transactions.*, clients.business_name
                                                          FROM transactions
                                                          LEFT JOIN clients ON transactions.client_id = clients.client_id
                                                          WHERE (transactions.status = 'PENDING' OR transactions.status = 'PENDING_APPROVAL')
                                                          AND (transactions.service_name = 'BANK_DEPOSIT' OR transactions.service_name = 'SWAP')
                                                          ORDER BY transactions.created_at DESC`);
        return this.makeResponse(200, "Pending deposits fetched successfully", pendingDeposits1);
      }

      // 2. Pagination values
      const limit = Number(data?.size) || 10;
      const currentPage = Number(data?.page) || 1;
      const offset = (currentPage - 1) * limit;

      // 3. Filters
      const searchTerm = data?.search?.trim();
      const startDate = data?.start_date?.trim();
      const endDate = data?.end_date?.trim();

      // Build WHERE conditions dynamically
      const conditions: string[] = [
        `(transactions.status = 'PENDING' OR transactions.status = 'PENDING_APPROVAL')`,
        `(transactions.service_name = 'BANK_DEPOSIT' OR transactions.service_name = 'SWAP' OR transactions.service_name = 'BANK_TRANSFER')`
      ];

      if (searchTerm) {
        conditions.push(`(transactions.id LIKE '%${searchTerm}%' OR transactions.client_id LIKE '%${searchTerm}%' OR clients.business_name LIKE '%${searchTerm}%' OR transactions.trans_id LIKE '%${searchTerm}%' OR transactions.memo LIKE '%${searchTerm}%' OR transactions.ext_reference LIKE '%${searchTerm}%' OR transactions.currency LIKE '%${searchTerm}%')`);
      }
      if (startDate && endDate) {
        conditions.push(`transactions.created_at BETWEEN '${startDate} 00:00:00' AND '${endDate} 23:59:59'`);
      } else if (startDate) {
        conditions.push(`transactions.created_at >= '${startDate} 00:00:00'`);
      } else if (endDate) {
        conditions.push(`transactions.created_at <= '${endDate} 23:59:59'`);
      }

      const whereClause = `WHERE ${conditions.join(" AND ")}`;

      // 4. Queries
      const totalResult: any = await this.callQuerySafe(`
        SELECT COUNT(*) AS total
        FROM transactions
        LEFT JOIN clients ON transactions.client_id = clients.client_id
        ${whereClause}
      `) as any[];
      const totalItems = (totalResult as any[])[0]?.total || 0;
      const totalPages = Math.ceil(totalItems / limit);

      const users = await this.callQuerySafe(`
        SELECT transactions.*, clients.business_name
        FROM transactions
        LEFT JOIN clients ON transactions.client_id = clients.client_id
        ${whereClause}
        ORDER BY transactions.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `);

      const total_unique_businesses = await this.callQuerySafe(`
        SELECT COUNT(DISTINCT transactions.client_id) AS total
        FROM transactions
        LEFT JOIN clients ON transactions.client_id = clients.client_id
        ${whereClause}
      `);

      const total_transactions_pending = await this.callQuerySafe(`
        SELECT COUNT(*) AS total
        FROM transactions
        LEFT JOIN clients ON transactions.client_id = clients.client_id
        ${whereClause}
      `);

      const total_pending_amounts = await this.callQuerySafe(`
        SELECT 
          currency, 
          COALESCE(SUM(amount), 0) AS total
        FROM transactions
        LEFT JOIN clients ON transactions.client_id = clients.client_id
        ${whereClause}
        GROUP BY currency
        ORDER BY currency
      `);

      // 5. Response
      return this.makeResponse(200, "clients", {
        items: users,
        pagination: {
          current_page: currentPage,
          next_page: currentPage + 1 <= totalPages ? currentPage + 1 : null,
          previous_page: currentPage > 1 ? currentPage - 1 : null,
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: limit
        },
        totals: {
          total_unique_businesses: (total_unique_businesses as any)[0]?.total || 0,
          total_transactions_pending: (total_transactions_pending as any)[0]?.total || 0,
          total_pending_amounts: total_pending_amounts
        }
      });

    } catch (error: any) {
      console.error("Error fetching pending deposits:", error);
      return this.makeResponse(500, "Error fetching pending deposits");
    }
  }




  async approveSwap(data: any) {
    try {
      const { status, clientId } = data;

      const transactionData: any = await this.callQuerySafe(`SELECT * FROM swap_requests WHERE trans_id = '${data.id}'`);
      if (transactionData.length === 0) {
        return this.makeResponse(400, "Swap request not found");
      }


      const swapRequest = transactionData[0];
      const id = swapRequest.trans_id;

      if (status === "approved") {
        const walletService = new WalletService();
        const swapResponse = await walletService.swapTokens(swapRequest.trans_id)
        return swapResponse
      }

      if (status === "rejected") {
        await this.updateData("swap_requests", `trans_id = '${id}'`, {
          status: "FAILED",
          memo: "Admin Rejected"
        });
      }


    } catch (error: any) {
      console.error("Error approving swap:", error);
      return this.makeResponse(500, "Error approving swap");
    }
  }



  async addApprovalWithdrawals(id: string, data: any) {
    let trans_id = id
    try {
      this.saveOperationLog("APPROVE_WITHDRAWAL", data?.clientId, data?.userId, "admin", "transactions", id, data, "1");
      const { status, reason, clientId } = data;
      // 1. Check access rights
      if (!await this.getLoggedUserAccessRights(clientId, "depositsApprove")) {
        return this.makeResponse(405, "You are not authorized to approve deposits");
      }

      // 2. Verify 2FA (skip for rejections if intended)
      const twoFa = await this.confirmUser2Fa(data);
      if (!twoFa?.status && status !== "rejected") {
        return this.makeResponse(400, twoFa?.message);
      }

      // 3. Get pending withdrawal
      const withdrawal: any = await this.callQuerySafe(
        `SELECT * FROM transactions 
         WHERE  id = '${id}' AND trans_type = 'PUSH' AND system_status = 'PENDING_ADMIN_APPROVAL'`
      );

      if (withdrawal.length === 0) {
        return this.makeResponse(400, "Pending withdraw transaction not found");
      }

      const withdraw = withdrawal[0];
      trans_id = withdraw.trans_id;
      if (status === "approved") {

        await this.updateData("transactions", `trans_id = '${trans_id}'`, {
          approved_by: clientId,
          system_status: "SUCCESS",
          status: "SUCCESS",
          message: "TRANSACTION APPROVED"
        });
        this.saveTransactionLog(trans_id, "SUCCESS", "ADMIN_APPROVAL", 200, "ACCEPTED WITHDRAWAL", data);
        return this.makeResponse(200, "Pending withdrawal approved successfully");
      } else {
        this.saveTransactionLog(trans_id, "REJECTED", "ADMIN_APPROVAL", 400, "ADMIN REJECTED WITHDRAWAL", data);
        await this.updateData("transactions", `trans_id = '${trans_id}'`, {
          approved_by: clientId,
          status: "PENDING_REVERSAL",
          message: reason || "Pending withdrawal was rejected by admin",
          system_status: "REJECTED"
        });
        return this.makeResponse(200, "Pending withdrawal was rejected");
      }

    } catch (error: any) {
      this.saveTransactionLog(trans_id, "ERROR", "ADMIN_APPROVAL", 500, "ERROR APPROVING WITHDRAWAL", data);
      console.error("Error approving withdrawals:", error);
      return this.makeResponse(500, "Error approving withdrawals");
    }
  }





  async addApprovalDeposits(id: string, data: any) {
    try {
      const { status, clientId } = data;

      // 1. Check access rights
      if (!await this.getLoggedUserAccessRights(clientId, "depositsApprove")) {
        return this.makeResponse(405, "You are not authorized to approve deposits");
      }

      // 2. Verify 2FA (skip for rejections if intended)
      const twoFa = await this.confirmUser2Fa(data);
      if (!twoFa?.status && status !== "rejected") {
        return this.makeResponse(400, twoFa?.message);
      }

   

      // 3. Get pending deposit
      const deposits: any = await this.callQuerySafe(
        `SELECT * FROM transactions 
         WHERE trans_id = '${id}' AND (status = 'PENDING' AND service_name = 'BANK_DEPOSIT') OR (status = 'PENDING_APPROVAL')`
      );
      if (!deposits.length) {
        return this.makeResponse(400, "Pending deposit not found");
      }
      const deposit = deposits[0];
      const transType = deposit.trans_type;

      if (transType === "SWAP") {
        const swapData = {
          status: status,
          clientId: clientId,
          id: id
        }
        return this.approveSwap(swapData);
      }
      // 4. Get maker-checker record
      const makerChecker = await this.selectDataQuerySafe("maker_checker", { id: deposit.validation_id });
      if (makerChecker.length > 0) {
        // return this.makeResponse(400, "Approval record not found");
        const record = makerChecker[0];

        // 5. Maker cannot approve own request
        if (record?.maker_id && String(record?.maker_id) === String(clientId)) {
          return this.makeResponse(206, "A maker cannot approve their own request");
        }
        // Prevent double approval
        if (record?.status !== "pending") {
          return this.makeResponse(400, "Deposit already reviewed");
        }

        // 6. Update maker_checker if still pending
        if (record?.status === "PENDING") {
          await this.updateData("maker_checker", `id = '${deposit.validation_id}'`, {
            approved_at: this.getMySQLDateTime(),
            checker_id: clientId,
            reason: "complete deposit transaction",
            status
          });
        }
      }

      const txType = deposits[0].trans_type;
      const serviceName = deposits[0].service_name;

      if (status === "approved") {
        const walletService = new WalletService();

        if (txType === "PULL" && serviceName === "BANK_DEPOSIT") {
          const issued = await walletService.issueTokens(id);
          return this.makeResponse(200, "Pending deposit approved successfully", issued);
        } else if (txType === "PUSH" && serviceName === "BANK_TRANSFER") {
          await this.updateData("transactions", `trans_id = '${id}'`, {
            status: " SUCCESS",
            memo: "TRANSACTION APPROVED"
          });
        }
      }

      if (status === "rejected") {
        await this.updateData("transactions", `trans_id = '${id}'`, {
          status: "FAILED",
          memo: "Admin Rejected"
        });
      }

      return this.makeResponse(200, `Pending deposit was ${status} successfully`, deposits);

    } catch (error: any) {

      console.error("Error approving deposit:", error);
      return this.makeResponse(500, "Error approving deposit");
    }
  }



  async transactionReport(data: any) {
    try {
      const { start_date, end_date, currency } = data;
      const transactions = await this.callQuerySafe(`select * FROM transactions  ORDER BY created_at DESC LIMIT 1000`);
      return this.makeResponse(200, "Transactions fetched successfully", transactions);
    } catch (error: any) {
      console.error("Error fetching transactions:", error);
      return this.makeResponse(500, "Error fetching transactions");
    }
  }








  async getTransactions(data: any) {
    try {
      // 1️⃣ If no pagination requested → fetch last 200
      if (data?.page === undefined || data?.page === null) {
        const reports: any = await this.callQuerySafe(`
          SELECT * 
          FROM transactions 
          WHERE NOT (trans_type = 'BANK_DEPOSIT' AND status = 'PENDING') 
          ORDER BY created_at DESC 
          LIMIT 1000
        `);
        return this.makeResponse(200, "Wallet reconciliation reports fetched successfully", reports);
      }

      // 2️⃣ Pagination setup
      const limit = Math.max(Number(data?.size) || 10, 1);
      const currentPage = Math.max(Number(data?.page) || 1, 1);
      const offset = (currentPage - 1) * limit;

      // 3️⃣ Build filters dynamically & use params for safety
      const filters: string[] = [];
      const params: any[] = [];

      if (data?.search?.trim()) {
        filters.push(`(reference_id LIKE ? OR service_name LIKE ? OR receiver_account LIKE ?)`);
        params.push(`%${data.search.trim()}%`, `%${data.search.trim()}%`, `%${data.search.trim()}%`);
      }
      if (data?.currency?.trim()) {
        filters.push(`currency = ?`);
        params.push(data.currency.trim().toUpperCase());
      }
      if (data?.transaction_type?.trim()) {
        filters.push(`trans_type = ?`);
        params.push(data.transaction_type.trim());
      }
      if (data?.status?.trim()) {
        filters.push(`status = ?`);
        params.push(data.status.trim().toUpperCase());
      }
      if (data?.message?.trim()) {
        filters.push(`message LIKE ?`);
        params.push(`%${data.message.trim()}%`);
      }
      if (data?.start_date?.trim() && data?.end_date?.trim()) {
        const startDateFormatted_ = new Date(data.start_date.trim()).toISOString();
        const endDateFormatted_ = new Date(data.end_date.trim()).toISOString();
        filters.push(`created_at >= ? AND created_at <= ?`);
        params.push(startDateFormatted_, endDateFormatted_);
      }

      const whereClause = filters.length ? `WHERE ${filters.join(" AND ")}` : "";

      // 4️⃣ Get total count for pagination
      const totalResult: any = await this.callQuerySafe(
        `SELECT COUNT(*) AS total FROM transactions ${whereClause}`,
        params
      );
      const totalItems: any = (totalResult as any)[0]?.total || 0;
      const totalPages = Math.ceil(totalItems / limit);
      const nextPage = currentPage < totalPages ? currentPage + 1 : null;
      const previousPage = currentPage > 1 ? currentPage - 1 : null;

      // 5️⃣ Fetch paginated transactions
      const transactions = await this.callQuerySafe(
        `SELECT * FROM transactions ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`,
        [...params, limit, offset]
      );

      // 6️⃣ Get totals (with success rate & net change)
      const totalsResult: any = await this.callQuerySafe(
        `
        SELECT 
          COUNT(*) AS total_transactions,
          SUM(amount) AS total_amount,
          SUM(fee) AS total_fees,
          SUM(CASE WHEN trans_type = 'PULL' THEN 1 ELSE 0 END) AS total_payout_transactions,
          SUM(CASE WHEN trans_type = 'PUSH' THEN 1 ELSE 0 END) AS total_collections_transactions,
          SUM(CASE WHEN trans_type = 'PULL' THEN amount ELSE 0 END) AS total_payout_amount,
          SUM(CASE WHEN trans_type = 'PUSH' THEN amount ELSE 0 END) AS total_collections_amount,
          SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) AS total_successful_transactions,
          SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) AS total_failed_transactions,
          SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END) AS total_pending_transactions,
          CASE 
            WHEN COUNT(*) > 0 
            THEN ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) 
            ELSE 0 
          END AS success_rate,
          SUM(CASE WHEN trans_type = 'PUSH' THEN 1 ELSE 0 END) - SUM(CASE WHEN trans_type = 'PULL' THEN 1 ELSE 0 END) AS net_change_transactions_count,
          SUM(CASE WHEN trans_type = 'PUSH' THEN amount ELSE 0 END) - SUM(CASE WHEN trans_type = 'PULL' THEN amount ELSE 0 END) AS net_change_amount
        FROM transactions
        ${whereClause}
        `,
        params
      );

      const totals = (totalsResult as any)[0] || {};

      // 7️⃣ Opening & closing balance (only if currency & date range defined)
      if (data?.currency?.trim() && data?.start_date?.trim() && data?.end_date?.trim()) {
        const currencyParam = data.currency.trim().toUpperCase();
        const startDateFormatted = new Date(data.start_date.trim()).toISOString();
        const endDateFormatted = new Date(data.end_date.trim()).toISOString();

        const openingBalanceRes = await this.callQuerySafe(
          `
          SELECT running_balance AS opening_balance
          FROM transactions
          WHERE currency = ? 
            AND status = 'SUCCESS'
            AND created_at < ?
          ORDER BY balance_updated_at DESC LIMIT 1  
          `,
          [currencyParam, startDateFormatted]
        );

        const closingBalanceRes = await this.callQuerySafe(
          `
          SELECT running_balance AS closing_balance
          FROM transactions
          WHERE currency = ? 
            AND status = 'SUCCESS'
            AND created_at <= ?
          ORDER BY balance_updated_at DESC LIMIT 1
          `,
          [currencyParam, endDateFormatted]
        );

        totals.opening_balance = (openingBalanceRes as any)[0]?.opening_balance || 0;
        totals.closing_balance = (closingBalanceRes as any)[0]?.closing_balance || 0;
      } else {
        totals.opening_balance = null;
        totals.closing_balance = null;
      }

      // 8️⃣ Return response
      return this.makeResponse(200, "Transactions fetched successfully", {
        items: transactions,
        pagination: {
          current_page: currentPage,
          next_page: nextPage,
          previous_page: previousPage,
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: limit,
        },
        totals,
      });

    } catch (error: any) {
      console.error("Error fetching reconciliation stats:", error);
      return this.makeResponse(500, "Error fetching client reconciliation");
    }
  }






  async exportTransactions(data: any) {
    try {
     

      // 2️⃣ Pagination setup
      const export_file = data.export_file || "excel"; // Default to excel
      const limit = Math.max(Number(data?.size) || 10, 1);
      const currentPage = Math.max(Number(data?.page) || 1, 1);
      const offset = (currentPage - 1) * limit;

      // 3️⃣ Build filters dynamically & use params for safety
      const filters: string[] = [];
      const params: any[] = [];

      if (data?.search?.trim()) {
        filters.push(`(reference_id LIKE ? OR service_name LIKE ? OR receiver_account LIKE ?)`);
        params.push(`%${data.search.trim()}%`, `%${data.search.trim()}%`, `%${data.search.trim()}%`);
      }
      if (data?.currency?.trim()) {
        filters.push(`currency = ?`);
        params.push(data.currency.trim().toUpperCase());
      }
      if (data?.transaction_type?.trim()) {
        filters.push(`trans_type = ?`);
        params.push(data.transaction_type.trim());
      }
      if (data?.status?.trim()) {
        filters.push(`status = ?`);
        params.push(data.status.trim().toUpperCase());
      }
      if (data?.message?.trim()) {
        filters.push(`message LIKE ?`);
        params.push(`%${data.message.trim()}%`);
      }
      if (data?.start_date?.trim() && data?.end_date?.trim()) {
        const startDateFormatted_ = new Date(data.start_date.trim()).toISOString();
        const endDateFormatted_ = new Date(data.end_date.trim()).toISOString();
        filters.push(`created_at >= ? AND created_at <= ?`);
        params.push(startDateFormatted_, endDateFormatted_);
      }

      const whereClause = filters.length ? `WHERE ${filters.join(" AND ")}` : "";

      // 4️⃣ Get total count for pagination
      const totalResult: any = await this.callQuerySafe(
        `SELECT COUNT(*) AS total FROM transactions ${whereClause}`,
        params
      );
      const totalItems: any = (totalResult as any)[0]?.total || 0;
      const totalPages = Math.ceil(totalItems / limit);
      const nextPage = currentPage < totalPages ? currentPage + 1 : null;
      const previousPage = currentPage > 1 ? currentPage - 1 : null;

      // 5️⃣ Fetch paginated transactions
      const transactions: any = await this.callQuerySafe(
        `SELECT * FROM transactions ${whereClause} ORDER BY created_at DESC`,
        [...params]
      );

      // Format data for export
      const formattedData = ExportHelper.formatTransactionData(transactions);
      
      // Generate filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `transactions_export_${timestamp}`;
      let exportResult;
      
      if (export_file.toLowerCase() === 'pdf') {
          exportResult = await ExportHelper.exportToPDF(
            formattedData, 
            filename, 
            'Transactions Export Report'
          );
        } else {
          // Default to Excel
          exportResult = await ExportHelper.exportToExcel(
            formattedData, 
            filename, 
            'Transactions'
          );
        }
        
        // Save a copy of the exported file to the exports folder
        try {
          const fs = require('fs');
          const path = require('path');
          const exportsDir = path.join(process.cwd(), 'exports');
          
          // Ensure exports directory exists
          if (!fs.existsSync(exportsDir)) {
            fs.mkdirSync(exportsDir, { recursive: true });
          }
          
          const filePath = path.join(exportsDir, exportResult.filename);
          fs.writeFileSync(filePath, exportResult.buffer);
          console.log(`Export file saved to: ${filePath}`);
        } catch (saveError) {
          console.log("Warning: Could not save export file copy:", saveError);
        }
        
        // Prepare export data
        const exportData = {
          export_type: export_file,
          total_records: transactions.length,
          export_date: new Date().toISOString(),
          filters: { },
          filename: exportResult.filename,
          buffer: exportResult.buffer,
          mime_type: exportResult.mime_type
        };
          
        return this.makeResponse(200, "Export file generated", exportData);


    } catch (error: any) {
      console.error("Error fetching reconciliation stats:", error);
      return this.makeResponse(500, "Error fetching client reconciliation");
    }
  }



  async getTransactionProviderLogs(id: string) {
    try {
      const transactions: any = await this.callQuerySafe(`select * FROM transactions WHERE trans_id = '${id}' LIMIT 1`);
      if (transactions.length === 0) {
        return this.makeResponse(400, "Transaction not found");
      }
      return this.makeResponse(200, "Transaction provider logs fetched successfully", transactions);
    } catch (error: any) {
      console.error("Error fetching transaction provider logs:", error);
      return this.makeResponse(500, "Error fetching transaction provider logs");
    }
  }




  
  async getTransactionsOnHold(data: any) {
    try {
      // 1️⃣ If no pagination requested → fetch last 200
      if (data?.page === undefined || data?.page === null) {
        const reports: any = await this.callQuerySafe(`
          SELECT * 
          FROM transactions 
          WHERE UPPER(status) IN ('ONHOLD', 'PENDING')
          ORDER BY created_at DESC 
          LIMIT 200
        `);
        return this.makeResponse(200, "Wallet reconciliation onhold reports fetched successfully", reports);
      }

      // 2️⃣ Pagination setup
      const limit = Math.max(Number(data?.size) || 10, 1);
      const currentPage = Math.max(Number(data?.page) || 1, 1);
      const offset = (currentPage - 1) * limit;

      // 3️⃣ Build filters dynamically & use params for safety
      const filters: string[] = [];
      const params: any[] = [];

      if (data?.search?.trim()) {
        filters.push(`(reference_id LIKE ? OR service_name LIKE ? OR receiver_account LIKE ?)`);
        params.push(`%${data.search.trim()}%`, `%${data.search.trim()}%`, `%${data.search.trim()}%`);
      }
      if (data?.currency?.trim()) {
        filters.push(`currency = ?`);
        params.push(data.currency.trim().toUpperCase());
      }
      if (data?.transaction_type?.trim()) {
        filters.push(`trans_type = ?`);
        params.push(data.transaction_type.trim());
      }
      if (data?.status?.trim()) {
        filters.push(`status = ?`);
        params.push(data.status.trim().toUpperCase());
      } else {
        filters.push(`UPPER(status) IN ('ONHOLD', 'PENDING')`);
      }

      if (data?.message?.trim()) {
        filters.push(`message LIKE ?`);
        params.push(`%${data.message.trim()}%`);
      }
      if (data?.start_date?.trim() && data?.end_date?.trim()) {
        const startDateFormatted_ = new Date(data.start_date.trim()).toISOString();
        const endDateFormatted_ = new Date(data.end_date.trim()).toISOString();
        filters.push(`created_at BETWEEN ? AND ?`);
        params.push(startDateFormatted_, endDateFormatted_);
      }

      const whereClause = filters.length ? `WHERE ${filters.join(" AND ")}` : "";

      // 4️⃣ Get total count for pagination
      const totalResult: any = await this.callQuerySafe(
        `SELECT COUNT(*) AS total FROM transactions ${whereClause}`,
        params
      );
      const totalItems: any = (totalResult as any)[0]?.total || 0;
      const totalPages = Math.ceil(totalItems / limit);
      const nextPage = currentPage < totalPages ? currentPage + 1 : null;
      const previousPage = currentPage > 1 ? currentPage - 1 : null;

      // 5️⃣ Fetch paginated transactions
      const transactions = await this.callQuerySafe(
        `SELECT * FROM transactions ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`,
        [...params, limit, offset]
      );

      const totals: any = {};
      totals.opening_balance = null;
      totals.closing_balance = null;
      // 8️⃣ Return response
      return this.makeResponse(200, "Transactions fetched successfully", {
        items: transactions,
        pagination: {
          current_page: currentPage,
          next_page: nextPage,
          previous_page: previousPage,
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: limit,
        },
        totals,
      });

    } catch (error: any) {
      console.error("Error fetching reconciliation onhold stats:", error);
      return this.makeResponse(500, "Error fetching client reconciliation");
    }
  }






  async getTransactionDetails(id: string) {
    try {

      const transactions: any = await this.callQuerySafe(`select * FROM transactions WHERE trans_id = '${id}' OR reference_id = '${id}' or receiver_account = '${id}' LIMIT 1`);
      if (transactions.length === 0) {
        return this.makeResponse(400, "Transaction not found");
      }
      const client: any = await this.callQuerySafe(`select * FROM clients WHERE client_id = '${transactions[0].client_id}'`);
      transactions[0].client = client[0];
      const transactionDetails = {
        transaction: transactions[0]
      }
      return this.makeResponse(200, "Transactions fetched successfully", transactions[0]);

    } catch (error: any) {
      console.error("Error fetching transactions:", error);
      return this.makeResponse(500, "Error fetching transactions");
    }
  }



  

  async getStats(data: any) {
    try {

      let { start_date, end_date, trans_type, currency } = data;
      const statsCurrency: any    = currency ? currency : 'UGX';

      let dateFilter = '';
      if (start_date && end_date && start_date !== "" && end_date !== "") {
        const formattedStartDate = await formatDateForMySQL(start_date);
        const formattedEndDate = await formatDateForMySQL(end_date);
        dateFilter = ` AND created_at >= '${formattedStartDate}' AND created_at <= '${formattedEndDate}'`;
      } else {
        const firstDayOfMonth = getFirstDayOfCurrentMonth();
        const firstDayOfNextMonth = getFirstDayOfNextMonth();
        dateFilter = ` AND created_at >= '${firstDayOfMonth}' AND created_at <= '${firstDayOfNextMonth}'`;
      }

      let transTypeFilter = '';
      if (trans_type && trans_type !== "") {
        transTypeFilter = ` AND trans_type = '${trans_type}'`;
      }

      let currencyTypeFilter = '';
      if (statsCurrency && statsCurrency !== "") {
        currencyTypeFilter = ` AND currency = '${statsCurrency}'`;
      }
      const businessCount:         any[] = await this.selectDataQuerySafe("clients", { status: 'active' });
      const businessInactiveCount: any[] = await this.selectDataQuerySafe("clients", { status: 'inactive' });
      const businessApprovedCount: any[] = [] 
      const businessRejectedCount: any[] = [] 
      const businessPendingCount:  any[] = [] 

      const transactions_this_week: any = await this.callQuerySafe(`select count(*) as total FROM transactions WHERE status = 'SUCCESS' ${dateFilter} ${currencyTypeFilter} ${transTypeFilter} `);
      const volumes_this_week: any = await this.callQuerySafe(`select SUM(amount) as total FROM transactions WHERE status = 'SUCCESS' ${currencyTypeFilter} ${dateFilter} ${transTypeFilter} `);
      const registrations_this_week: any = await this.callQuerySafe(`select count(*) as total FROM clients WHERE status = 'active' ${dateFilter} `);
      
      const collections_this_week: any = await this.callQuerySafe(`select SUM(amount) as total FROM transactions WHERE status = 'SUCCESS' AND trans_type='PULL' ${currencyTypeFilter} ${dateFilter}`);
      const payouts_this_week: any = await this.callQuerySafe(`select SUM(amount) as total FROM transactions WHERE status = 'SUCCESS' AND trans_type='PUSH' ${currencyTypeFilter} ${dateFilter}`);
      const revenue_this_week: any = await this.callQuerySafe(`select SUM(fee - provider_fees) AS profit FROM transactions WHERE status = 'SUCCESS' ${currencyTypeFilter} ${dateFilter}`);
      const revenue_earned: any = await this.callQuerySafe(`
                                                              SELECT 
                                                                DATE_FORMAT(MIN(created_at), '%b') AS month,
                                                                SUM(amount) AS total_revenue,
                                                                SUM(fee) AS muda_fees,
                                                                SUM(provider_fees) AS provider_fees,
                                                                SUM(fee - provider_fees) AS profit
                                                              FROM transactions
                                                              WHERE status = 'SUCCESS'
                                                              ${currencyTypeFilter}
                                                              AND created_at >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
                                                              GROUP BY YEAR(created_at), MONTH(created_at)
                                                              ORDER BY YEAR(created_at), MONTH(created_at)
                                                            `);
      const user_growth: any = await this.callQuerySafe(`SELECT 
                                                            DATE_FORMAT(created_at, '%b') AS month,
                                                            COUNT(*) AS total_clients
                                                          FROM clients
                                                          WHERE status = 'active'
                                                          AND created_at >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
                                                          GROUP BY DATE_FORMAT(created_at, '%b'), YEAR(created_at), MONTH(created_at)
                                                          ORDER BY YEAR(created_at), MONTH(created_at)`);

      const hardcodedStats = {
        collections: collections_this_week[0]?.total ?? 0,
        payouts: payouts_this_week[0]?.total ?? 0,
        revenue: revenue_this_week[0]?.profit ?? 0,
        transactions: transactions_this_week[0]?.total ?? 0,
        transactions_this_week: transactions_this_week[0]?.total ?? 0,
        volumes_this_week: (volumes_this_week[0]?.total !== null) ? `${volumes_this_week[0]?.total} ${statsCurrency}` : `0 ${statsCurrency}`,
        registrations_this_week: registrations_this_week[0]?.total ?? 0,

        businesses: businessCount.length ?? 0,
        businessInactiveCount: businessInactiveCount.length ?? 0,
        businessApprovedCount: businessApprovedCount.length ?? 0,
        businessRejectedCount: businessRejectedCount.length ?? 0,
        businessPendingCount: businessPendingCount.length ?? 0,
        revenue_earned: revenue_earned,
        user_growth: user_growth

      };

      return this.makeResponse(200, "Stats fetched successfully", hardcodedStats);
    } catch (error: any) {
      console.error("Error fetching stats:", error);
      return this.makeResponse(500, "Error fetching stats");
    }
  }

  async getStatsVolumesGraphs(data: any) {
    try {
      const { start_date, end_date, currency } = data;
      const statsCurrency: any = currency ? currency : 'UGX';

      const transactions_this_week: any = await this.callQuerySafe(`select count(*) as total FROM transactions WHERE status = 'SUCCESS' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
      const volumes_this_week: any = await this.callQuerySafe(`select SUM(amount) as total FROM transactions WHERE currency = '${statsCurrency}' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
    } catch (error: any) {
      console.error("Error fetching stats graphs:", error);
      return this.makeResponse(500, "Error fetching stats graphs");
    }
  }

  async getStatsRevenueGraphs(data: any) {
    try {
      const { start_date, end_date, currency } = data;
      const statsCurrency: any = currency ? currency : 'UGX';

      const transactions_this_week: any = await this.callQuerySafe(`select count(*) as total FROM transactions WHERE status = 'SUCCESS' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
      const volumes_this_week: any = await this.callQuerySafe(`select SUM(amount) as total FROM transactions WHERE currency = '${statsCurrency}' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
    } catch (error: any) {
      console.error("Error fetching stats graphs:", error);
      return this.makeResponse(500, "Error fetching stats graphs");
    }
  }


  async getStatsUsersGraphs(data: any) {
    try {
      const { start_date, end_date, currency } = data;
      const statsCurrency: any = currency ? currency : 'UGX';

      const transactions_this_week: any = await this.callQuerySafe(`select count(*) as total FROM transactions WHERE status = 'SUCCESS' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
      const volumes_this_week: any = await this.callQuerySafe(`select SUM(amount) as total FROM transactions WHERE currency = '${statsCurrency}' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
    } catch (error: any) {
      console.error("Error fetching stats graphs:", error);
      return this.makeResponse(500, "Error fetching stats graphs");
    }
  }

  async getBusinessWallet(client_id: string, data: any) {
    try {
      const { start_date, end_date, currency } = data;
      const wallets: any = await this.callQuerySafe(`select public_key, created_on FROM client_wallets WHERE client_id='${client_id}'`)
      return this.makeResponse(200, "Client wallets fetched successfully", wallets);
    } catch (error: any) {
      console.error("Error fetching business wallet:", error);
    }
  }

  private validateBusinessData(data: any) {
    const businessSchema = z.object({
      business_name: z.string().min(3),
      phone_number: z.string().min(10).max(15),
      address: z.string().optional(),
      contact_person_name: z.string().min(3),
      contact_email: z.string().email(),
      contact_phone: z.string().min(10).max(15),
    });
    return businessSchema.safeParse(data);
  }

  private validateBankData(data: any) {
    const bankSchema = z.object({
      bank_name: z.string().min(1, "Bank name is required"),
      account_name: z.string().min(1, "Account name is required"),
      account_number: z.string().min(1, "Account number is required"),
      swift_code: z.string().min(1, "SWIFT code is required"),
      country: z.string().min(1, "Country is required"),
      currency: z.string().min(1, "Currency is required"),
      branch_name: z.string().optional(),
      reference_code: z.string().optional(),
    });
    return bankSchema.safeParse(data);
  }

  private validateRolesData(data: any) {
    const roleSchema = z.object({
      name: z.string().min(3),
      status: z.enum(['active', 'inactive']),
      details: z.string().optional(),
      access_rights: z.array(z.string()).default([])
    });
    return roleSchema.safeParse(data);
  }



  private validateProductData(data: any) {
    const productSchema = z.object({
      fee_amount: z.number(),
      status: z.enum(['FLAT', 'PERCENTAGE'])
    });
    return productSchema.safeParse(data);
  }



  async createDashboardLogin(data: any) {
    try {


      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "businessEdit");
      const accessRight2 = await this.getLoggedUserAccessRights(data?.clientId, "clientsEdit");
      if ((!accessRight || !accessRight2) || (!accessRight && !accessRight2)) {
        return this.makeResponse(405, "You are not authorized to create admin user", []);
      }

      const { email, first_name, last_name, client_id } = data;
      const password = this.generateSecurePassword(12, true);
      const encryptedPassword = await this.generatePassword(password);
      const existingAdmin = await this.selectDataQuerySafe("clients", { client_id });
      if (existingAdmin.length == 0) {
        return this.makeResponse(206, "client doesn't exist");
      }

      const admin: any = await this.callQuerySafe("SELECT * FROM client_logins WHERE email = ? AND client_id = ?", [email, client_id]);
      if (admin.length > 0) {
        return this.makeResponse(200, "Email already registered");
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }


      const defaultRole: any = await this.defaultRole();
      const adminData = {
        client_id,
        email,
        password: encryptedPassword,
        default_password: true,
        role: defaultRole?.id,
        status: 'active',
        first_name,
        last_name,
      };
      await this.insertData("client_logins", adminData);
      this.sendEmail("CLIENT_REGISTRATION", email, first_name, password);

      return this.makeResponse(200, "Client access created successfully");
    } catch (error: any) {
      console.error("Error creating admin user:", error);
      return this.makeResponse(500, "email already exists");
    }
  }


  async adminConfirmLogin(email: string, password: string, verificationToken: string) {
    try {

      // 🔎 Check if Admin Exists
      const admin: any[] = await this.selectDataQuerySafe("system_users", { email });
      if (admin.length === 0) {
        return this.makeResponse(200, "Invalid username/password");
      }

      // Check if password is stored as bcrypt hash or old AES encryption
      let isPasswordValid = false;

      // First try bcrypt verification
      try {
        isPasswordValid = await this.verifyPassword(password, admin[0].password);
      } catch (error) {
        // If bcrypt fails, try old AES decryption
        try {
          const bytes = CryptoJS.AES.decrypt(admin[0].password, SECRET_KEY);
          const originalPassword = bytes.toString(CryptoJS.enc.Utf8);
          isPasswordValid = (originalPassword === password);
        } catch (decryptError) {
          console.error("Error decrypting password:", decryptError);
          isPasswordValid = false;
        }
      }

      if (!isPasswordValid) {
        return this.makeResponse(400, "Invalid credentials");
      }

      if (admin[0].default_password === "true") {
        const checkDefaultPasswordExpiry = await this.checkDefaultPasswordExpiry(admin[0].default_password_expiry, 4);
        if (!checkDefaultPasswordExpiry) {
          return this.makeResponse(400, "Your login token has expired. Please reset your password.");
        }
      }

      const data: any = {};
      data.clientId = admin[0].id;
      data.user_type = 'admin';
      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'admin' })
      if (client.length === 0) {
        return this.makeResponse(400, "Unknown 2fa secret");
      }

      const responseData: any = await TwoFactorAuthHelper.verifySecret(client[0].secret, verificationToken)
      if (!responseData?.status) {
        return this.makeResponse(400, "Invalid 2fa token");
      }

      // 🔑 Generate JWT Token
      const token = jwt.sign({ adminId: admin[0].id, email: admin[0].email }, process.env.JWT_SECRET!, {
        expiresIn: "10h",
      });

      return this.makeResponse(200, "Login successful", { token });
    } catch (error: any) {
      console.error("Error in admin login:", error);
      return this.makeResponse(500, "Error logging in");
    }
  }


  async adminLogin(email: string, password: string) {
    try {

      // 🔎 Check if Admin Exists
      const admin: any[] = await this.selectDataQuerySafe("system_users", { email });
      if (admin.length === 0) {
        return this.makeResponse(400, "Invalid username/password  ");
      }

      const hashedPassword = await this.generatePassword(password);
      // console.log(`hash-testing`, hashedPassword) // SECURITY: Removed password logging

      // Check if password is stored as bcrypt hash or old AES encryption
      let isPasswordValid = false;

      // First try bcrypt verification
      try {
        isPasswordValid = await this.verifyPassword(password, admin[0].password);
      } catch (error) {
        // If bcrypt fails, try old AES decryption
        try {
          const bytes = CryptoJS.AES.decrypt(admin[0].password, SECRET_KEY);
          const originalPassword = bytes.toString(CryptoJS.enc.Utf8);
          isPasswordValid = (originalPassword === password);
        } catch (decryptError) {
          console.error("Error decrypting password:", decryptError);
          isPasswordValid = false;
        }
      }

      if (!isPasswordValid) {
        return this.makeResponse(400, "Invalid credentials");
      }

      if (admin[0].default_password === "true") {
        const checkDefaultPasswordExpiry = await this.checkDefaultPasswordExpiry(admin[0].default_password_expiry, 4);
        if (!checkDefaultPasswordExpiry) {
          return this.makeResponse(400, "Your login token has expired. Please reset your password.");
        }
      }
      const data: any = {};
      data.clientId = admin[0].id;
      data.user_type = 'admin';
      const client: any = await this.userFaAuthAccountStatus(data)
      if (client[0]?.status === 'active') {
        return this.makeResponse(200, "Provide a 2fa token to login", { "2fa_status": client[0]?.status });
      }
      const token = jwt.sign({ adminId: admin[0].id, email: admin[0].email }, process.env.JWT_SECRET!, {
        expiresIn: "10h",
      });
      return this.makeResponse(200, "Login successful", { token });
    } catch (error: any) {
      console.error("Error in admin login:", error);
      return this.makeResponse(500, "Error logging in");
    }
  }

  /**
   * ✅ Create Admin User
   */
  async createAdminUser(data: any) {
    try {
      return this.makeResponse(200, "out of service");
    } catch (error: any) {
      console.error("Error creating admin user:", error);
      return this.makeResponse(500, "Error creating admin user");
    }
  }



  async getAssetHoldersOld(currency: string) {
    const supply = await new StellarService().getAssetHolders(currency);
    //   const client = await this.callQuerySafe(`select * from supported_currencies `);
    return this.makeResponse(200, "Error fetching stats", supply);
  }


  async getClientCurrencies(clientId: string) {
    //const client = await this.callQuerySafe(`select * from client_currencies c inner join supported_currencies s  where s.asset_code=c.currency and client_id = '${clientId}'`);
    const client = await this.callQuerySafe(`select *  from supported_currencies`);
    return this.makeResponse(200, "Error fetching stats", client);
  }

  async getclientBalance(clientId: any) {

    // const accessRight = await this.getLoggedUserAccessRights(clientId, "walletView");
    // if(!accessRight) {
    //   // return this.makeResponse(405, "You are not authorized to view client balances", []);
    // }
    const currencyInfo: any = await this.getClientCurrencies(clientId);
    const currencies = currencyInfo.data
    for (const currency of currencies) {
      const supply = await new StellarService().getAssetSupply(currency.asset_code);
      currency.balance = supply;
    }
    return this.makeResponse(200, "Client balances fetched successfully", currencies);
  }


  async getAllSystemUsers(data: any) {
    try {
      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "usersView");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to view system users", []);
      }

      const { page, size = 8, search = '', role = '' } = data || {};
      const limit = parseInt(size) > 0 ? parseInt(size) : 8;
      const offset = page ? (Number(page) - 1) * limit : 0;

      // Build search condition
      let searchCondition = "WHERE u.email != ''";
      if (search && search.trim() !== '') {
        const searchStr = search.trim().replace(/'/g, "''");
        searchCondition += ` AND (u.first_name LIKE '%${searchStr}%' OR u.last_name LIKE '%${searchStr}%' OR u.email LIKE '%${searchStr}%')`;
      }
      if (role && role.trim() !== '') {
        searchCondition += ` AND u.role = '${role}'`;
      }

      // ✅ If page not provided → fetch all users (no pagination)
      if (!page) {
        const usersQuery = `
          SELECT u.*, r.name as role_name, r.id as role_id, r.details as role_details, r.status as role_status
          FROM system_users u
          LEFT JOIN roles r ON u.role = r.id
          ${searchCondition}
          ORDER BY u.id DESC
        `;
        const users = await this.callQuerySafe(usersQuery) as any[];

        if (users.length === 0) {
          return this.makeResponse(400, "No system users found", []);
        }

        const defaultRole: any = await this.defaultAdminRole();
        const usersData: any[] = await Promise.all(
          users.map(async (user: any) => {
            const accessRights = await this.getRoleAccessRights(user.role);
            return {
              id: user.id,
              first_name: user.first_name,
              last_name: user.last_name,
              email: user.email,
              role: user.role,
              role_details: {
                id: user.role_id ?? defaultRole?.id,
                name: user.role_name ?? defaultRole?.name,
                details: user.role_details ?? defaultRole?.details,
                status: user.role_status ?? defaultRole?.status,
                access_rights: accessRights
              },
              status: user.status,
              updated_at: user.updated_at,
              created_at: user.created_at
            };
          })
        );

        return this.makeResponse(200, "System users fetched successfully", usersData);
      }

      // ✅ If page is provided → fetch paginated users
      const countQuery = `SELECT COUNT(*) AS total FROM system_users u ${searchCondition}`;
      const countResult: any = await this.callQuerySafe(countQuery);
      const totalItems = countResult[0]?.total || 0;

      const usersQuery = `
        SELECT u.*, r.name as role_name, r.id as role_id, r.details as role_details, r.status as role_status
        FROM system_users u
        LEFT JOIN roles r ON u.role = r.id
        ${searchCondition}
        ORDER BY u.id DESC
        LIMIT ${limit} OFFSET ${offset}
      `;
      const users = await this.callQuerySafe(usersQuery) as any[];

      if (users.length === 0) {
        return this.makeResponse(400, "No system users found", {
          items: [],
          pagination: {
            current_page: page,
            total_pages: 0,
            total_items: 0,
            items_per_page: limit,
            hasNextPage: false,
            hasPrevPage: false,
            next_page: null,
            previous_page: null
          }
        });
      }

      const defaultRole: any = await this.defaultAdminRole();
      const usersData: any[] = await Promise.all(
        users.map(async (user: any) => {
          const accessRights = await this.getRoleAccessRights(user.role);
          return {
            id: user.id,
            first_name: user.first_name,
            last_name: user.last_name,
            email: user.email,
            role: user.role,
            role_details: {
              id: user.role_id ?? defaultRole?.id,
              name: user.role_name ?? defaultRole?.name,
              details: user.role_details ?? defaultRole?.details,
              status: user.role_status ?? defaultRole?.status,
              access_rights: accessRights
            },
            status: user.status,
            updated_at: user.updated_at,
            created_at: user.created_at
          };
        })
      );

      const totalPages = Math.ceil(totalItems / limit);
      const hasNextPage = Number(page) < totalPages;
      const hasPrevPage = Number(page) > 1;

      return this.makeResponse(200, "System users fetched successfully", {
        items: usersData,
        pagination: {
          current_page: Number(page),
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: limit,
          hasNextPage,
          hasPrevPage,
          next_page: hasNextPage ? Number(page) + 1 : null,
          previous_page: hasPrevPage ? Number(page) - 1 : null
        }
      });

    } catch (error: any) {
      console.error("Error fetching system users:", error);
      return this.makeResponse(500, "Error fetching system users");
    }
  }



  async totalClientsStats(data: any) {
    try {

      let conditions: string[] = [];

      if (data?.client_status_type === 'active') {
        conditions.push(`status = '${data.client_status_type}'`);
      }

      if (data?.client_kyc_status === 'active') {
        conditions.push(`kyc_status = '${data.client_kyc_status}'`);
      }

      const qryCondition = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      const reports: any = await this.callQuerySafe(`
        SELECT COUNT(*) AS total
        FROM clients ${qryCondition}
      `);

      const totalClients = reports?.[0]?.total || 0;

      return this.makeResponse(200, "System total clients fetched successfully", {
        totalClients
      });
    } catch (error: any) {
      console.error("Error fetching system clients:", error);
      return this.makeResponse(500, "Error fetching system clients");
    }
  }


  async totalTransactionsStats() {
    try {

      const reports: any = await this.callQuerySafe(`SELECT COUNT(*) AS total FROM transactions`);
      const totalTransactions = reports?.[0]?.total || 0;

      return this.makeResponse(200, "System total transactions fetched successfully", {
        totalTransactions
      });
    } catch (error: any) {
      console.error("Error fetching system transactions:", error);
      return this.makeResponse(500, "Error fetching system transactions");
    }
  }


  async totalTransactionsFees() {
    try {

      const reports: any = await this.callQuerySafe(`SELECT SUM(fee) AS total
                                                 FROM transactions
                                                 WHERE status = 'SUCCESS'`);
      const totalFee = reports?.[0]?.total || 0;

      return this.makeResponse(200, "System total transaction fees fetched successfully", {
        totalFee
      });

    } catch (error: any) {
      console.error("Error fetching total transaction fees:", error);
      return this.makeResponse(500, "Error fetching total transactionn fees");
    }
  }

  async transactionsFees() {
    try {

      const reports: any = await this.callQuerySafe(`SELECT  id, product_id, trans_type,trans_id,amount,asset_code,currency,
                                                         status,fee,service_name, created_at
                                                           FROM transactions
                                                            WHERE status = 'SUCCESS' ORDER BY created_at DESC`);
      const transactionFee: any = reports;
      return this.makeResponse(200, "System transaction fees fetched successfully", reports);

    } catch (error: any) {
      console.error("Error fetching  transaction fees:", error);
      return this.makeResponse(500, "Error fetching  transactionn fees");
    }
  }


  async reconciliationStats(wallet: string, data: any) {
    try {
      // 1️⃣ Access control
      const canViewBusiness = await this.getLoggedUserAccessRights(data?.clientId, "businessView");
      const canViewClients = await this.getLoggedUserAccessRights(data?.clientId, "clientsView");

      if (!canViewBusiness || !canViewClients) {
        return this.makeResponse(405, "You are not authorized to view clients reconciliation", []);
      }

      const clientId = wallet;


      if (data?.page === undefined || data?.page === null) {
        const reports: any = await this.callQuerySafe(`SELECT * FROM transactions WHERE client_id = '${clientId}' ORDER BY created_at DESC`);
        return this.makeResponse(200, "Wallet reconciliation reports fetched successfully", reports);
      }

      // 2️⃣ Pagination setup
      const limit = Math.max(Number(data?.size) || 10, 1);
      const currentPage = Math.max(Number(data?.page) || 1, 1);
      const offset = (currentPage - 1) * limit;

      // 3️⃣ Build filters dynamically & use parameters for safety
      const filters: string[] = ["client_id = ?"];
      const params: any[] = [clientId];

      if (data?.search?.trim()) {
        filters.push(`(reference_id LIKE ? OR service_name LIKE ? OR receiver_account LIKE ?)`);
        params.push(`%${data.search.trim()}%`, `%${data.search.trim()}%`, `%${data.search.trim()}%`);
      }
      if (data?.currency?.trim()) {
        filters.push(`currency = ?`);
        params.push(data.currency.trim().toUpperCase());
      }
      if (data?.transaction_type?.trim()) {
        filters.push(`trans_type = ?`);
        params.push(data.transaction_type.trim());
      }
      if (data?.status?.trim()) {
        filters.push(`status = ?`);
        params.push(data.status.trim().toUpperCase());
      }
      if (data?.message?.trim()) {
        filters.push(`message LIKE ?`);
        params.push(`%${data.message.trim()}%`);
      }
      if (data?.start_date?.trim() && data?.end_date?.trim()) {
        let startDateFormatted_ = new Date(data.start_date.trim()).toISOString();
        let endDateFormatted_ = new Date(data.end_date.trim()).toISOString();
        filters.push(`created_at >= ? AND created_at <= ?`);
        params.push(startDateFormatted_, endDateFormatted_);
      }

      const whereClause = filters.length ? `WHERE ${filters.join(" AND ")}` : "";
      // 4️⃣ Get total count for pagination
      const totalResult: any = await this.callQuerySafe(
        `SELECT COUNT(*) AS total FROM transactions ${whereClause}`,
        params
      );
      const totalItems = totalResult[0]?.total || 0;
      const totalPages = Math.ceil(totalItems / limit);
      const nextPage = currentPage < totalPages ? currentPage + 1 : null;
      const previousPage = currentPage > 1 ? currentPage - 1 : null;

      // 5️⃣ Fetch paginated transactions
      const transactions = await this.callQuerySafe(
        `SELECT * FROM transactions ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`,
        [...params, limit, offset]
      );

      // 6️⃣ Get all totals in **one** query (plus success rate & net change)
      const totalsResult = await this.callQuerySafe(
        `
          SELECT 
            COUNT(*) AS total_transactions,
            SUM(amount) AS total_amount,
            SUM(fee) AS total_fees,
            SUM(CASE WHEN trans_type = 'PULL' THEN 1 ELSE 0 END) AS total_payout_transactions,
            SUM(CASE WHEN trans_type = 'PUSH' THEN 1 ELSE 0 END) AS total_collections_transactions,
            SUM(CASE WHEN trans_type = 'PULL' THEN amount ELSE 0 END) AS total_payout_amount,
            SUM(CASE WHEN trans_type = 'PUSH' THEN amount ELSE 0 END) AS total_collections_amount,
            SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) AS total_successful_transactions,
            SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) AS total_failed_transactions,
            SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END) AS total_pending_transactions,
            CASE 
              WHEN COUNT(*) > 0 
              THEN ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) 
              ELSE 0 
            END AS success_rate,
            SUM(CASE WHEN trans_type = 'PUSH' THEN 1 ELSE 0 END) - SUM(CASE WHEN trans_type = 'PULL' THEN 1 ELSE 0 END) AS net_change_transactions_count,
            SUM(CASE WHEN trans_type = 'PUSH' THEN amount ELSE 0 END) - SUM(CASE WHEN trans_type = 'PULL' THEN amount ELSE 0 END) AS net_change_amount
          FROM transactions
          ${whereClause}
          `,
        params
      ) as any[];

      const totals: any = (totalsResult as any[])[0] || {};

      // 7️⃣ Opening & closing balance (only if currency & date range defined)
      if (data?.currency?.trim() && data?.start_date?.trim() && data?.end_date?.trim()) {

        const currencyParam = data.currency.trim().toUpperCase();
        let startDateFormatted = new Date(data.start_date.trim()).toISOString();
        let endDateFormatted = new Date(data.end_date.trim()).toISOString();

        // Opening balance: total amount from successful txns <= start_date
        const openingBalanceRes = await this.callQuerySafe(
          `
            SELECT running_balance AS opening_balance
            FROM transactions
            WHERE client_id = ? 
              AND currency = ? 
              AND status = 'SUCCESS'
              AND created_at < ?
            ORDER BY balance_updated_at DESC LIMIT 1  
            `,
          [clientId, currencyParam, startDateFormatted]
        );

        // Closing balance: total amount from successful txns > start_date AND <= end_date
        const closingBalanceRes = await this.callQuerySafe(
          `
            SELECT running_balance AS closing_balance
            FROM transactions
            WHERE client_id = ? 
              AND currency = ? 
              AND status = 'SUCCESS'
              AND created_at >= ? 
              AND created_at <= ?
            ORDER BY balance_updated_at DESC LIMIT 1
            `,
          [clientId, currencyParam, startDateFormatted, endDateFormatted]
        );

        totals.opening_balance = (openingBalanceRes as any)[0]?.opening_balance || 0;
        totals.closing_balance = (closingBalanceRes as any)[0]?.closing_balance || 0;
      } else {
        totals.opening_balance = null;
        totals.closing_balance = null;
      }


      // 8️⃣ Return response
      return this.makeResponse(200, "Clients reconciliation data fetched successfully", {
        items: transactions,
        pagination: {
          current_page: currentPage,
          next_page: nextPage,
          previous_page: previousPage,
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: limit
        },
        totals
      });

    } catch (error: any) {
      console.error("Error fetching reconciliation stats:", error);
      return this.makeResponse(500, "Error fetching client reconciliation");
    }
  }



  async exportReconciliationStats(wallet: string, data: any) {
    try {
     
      // 2️⃣ Pagination setup
      const export_file = data.export_file || "excel"; // Default to excel
      // 2️⃣ Pagination setup
      const limit = Math.max(Number(data?.size) || 10, 1);
      const currentPage = Math.max(Number(data?.page) || 1, 1);
      const offset = (currentPage - 1) * limit;
      const clientId = wallet;

      // 3️⃣ Build filters dynamically & use parameters for safety
      const filters: string[] = ["client_id = ?"];
      const params: any[] = [clientId];

      if (data?.search?.trim()) {
        filters.push(`(reference_id LIKE ? OR service_name LIKE ? OR receiver_account LIKE ?)`);
        params.push(`%${data.search.trim()}%`, `%${data.search.trim()}%`, `%${data.search.trim()}%`);
      }
      if (data?.currency?.trim()) {
        filters.push(`currency = ?`);
        params.push(data.currency.trim().toUpperCase());
      }
      if (data?.transaction_type?.trim()) {
        filters.push(`trans_type = ?`);
        params.push(data.transaction_type.trim());
      }
      if (data?.status?.trim()) {
        filters.push(`status = ?`);
        params.push(data.status.trim().toUpperCase());
      }
      if (data?.message?.trim()) {
        filters.push(`message LIKE ?`);
        params.push(`%${data.message.trim()}%`);
      }
      if (data?.start_date?.trim() && data?.end_date?.trim()) {
        let startDateFormatted_ = new Date(data.start_date.trim()).toISOString();
        let endDateFormatted_ = new Date(data.end_date.trim()).toISOString();
        filters.push(`created_at >= ? AND created_at <= ?`);
        params.push(startDateFormatted_, endDateFormatted_);
      }

      const whereClause = filters.length ? `WHERE ${filters.join(" AND ")}` : "";
      // 4️⃣ Get total count for pagination
      const totalResult: any = await this.callQuerySafe(
        `SELECT COUNT(*) AS total FROM transactions ${whereClause}`,
        params
      );
      const totalItems = totalResult[0]?.total || 0;
      const totalPages = Math.ceil(totalItems / limit);
      const nextPage = currentPage < totalPages ? currentPage + 1 : null;
      const previousPage = currentPage > 1 ? currentPage - 1 : null;

      // 5️⃣ Fetch paginated transactions
      const transactions: any = await this.callQuerySafe(
        `SELECT * FROM transactions ${whereClause} ORDER BY created_at DESC`,
        [...params]
      );
      // Format data for export
      const formattedData = ExportHelper.formatTransactionData(transactions);
    
      // Generate filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `transactions_export_${timestamp}`;
      let exportResult;
      
      if (export_file.toLowerCase() === 'pdf') {
          exportResult = await ExportHelper.exportToPDF(
            formattedData, 
            filename, 
            'Transactions Export Report'
          );
        } else {
          // Default to Excel
          exportResult = await ExportHelper.exportToExcel(
            formattedData, 
            filename, 
            'Transactions'
          );
        }
        
        // Save a copy of the exported file to the exports folder
        try {
          const fs = require('fs');
          const path = require('path');
          const exportsDir = path.join(process.cwd(), 'exports');
          
          // Ensure exports directory exists
          if (!fs.existsSync(exportsDir)) {
            fs.mkdirSync(exportsDir, { recursive: true });
          }
          
          const filePath = path.join(exportsDir, exportResult.filename);
          fs.writeFileSync(filePath, exportResult.buffer);
          console.log(`Export file saved to: ${filePath}`);
        } catch (saveError) {
          console.log("Warning: Could not save export file copy:", saveError);
        }
        
        // Prepare export data
        const exportData = {
          export_type: export_file,
          total_records: transactions.length,
          export_date: new Date().toISOString(),
          filters: { },
          filename: exportResult.filename,
          buffer: exportResult.buffer,
          mime_type: exportResult.mime_type
        };
          
        return this.makeResponse(200, "Export file generated", exportData);


    } catch (error: any) {
      console.error("Error fetching reconciliation stats:", error);
      return this.makeResponse(500, "Error fetching client reconciliation");
    }
  }


  async userBalances(clientId: any) {
    console.log(`getStatement`, clientId)

    const currencyInfo: any = await this.getClientCurrencies(clientId);
    const currencies = currencyInfo.data;

    // Get the balances from StellarService
    const balances: any = await new StellarService().getBalance(clientId) || [];

    // For each currency, find a matching balance; if not found, set it to "0"
    for (const currency of currencies) {
      const matchingBalance = balances.find((b: any) => currency.asset_code === b.code);
      currency.balance = matchingBalance ? matchingBalance.balance : "0";
    }
    return this.makeResponse(200, "Client balances fetched successfully", currencies);
  }

  async usersStats(data: any) {
    try {

      const roles = ['verifier', 'approver', 'admin', 'user'];
      const reports: { role: string; count: number }[] = [];
      for (const role of roles) {

        const users = await this.selectDataQuerySafe("system_users", { role });
        reports.push({ role, count: users?.length || 0 });
      }
      return this.makeResponse(200, "System user reports fetched successfully", reports);

    } catch (error: any) {
      console.error("Error fetching system users:", error);
      return this.makeResponse(500, "Error fetching system users");
    }
  }




  // async volumeStats(data: any) {
  //   try {

  //     let { start_date, end_date } = data;

  //     // Determine date range
  //     let dateFilter = '';
  //     if (start_date && end_date && start_date !== "" && end_date !== "") {
  //       const formattedStartDate = await formatDateForMySQL(start_date);
  //       const formattedEndDate = await formatDateForMySQL(end_date);
  //       dateFilter = ` AND created_at >= '${formattedStartDate}' AND created_at <= '${formattedEndDate}'`;
  //       //dateFilter = ` AND created_at BETWEEN '${formattedStartDate}' AND '${formattedEndDate}'`;
  //     } else {
  //       const firstDayOfMonth = getFirstDayOfCurrentMonth();
  //       const today = getCurrentDateMySQL();
  //       dateFilter = ` AND created_at >= '${firstDayOfMonth}' AND created_at < '${today}'`;
  //     }

  //     // SQL query with parameterized values (consider using prepared statements)
  //     const sql = `
  //           SELECT 
  //               currency,
  //               SUM(CASE WHEN trans_type = 'PUSH' THEN amount ELSE 0 END) AS push_volume,
  //               SUM(CASE WHEN trans_type = 'PULL' THEN amount ELSE 0 END) AS pull_volume,
  //               SUM(CASE WHEN trans_type = 'SWAP' THEN amount ELSE 0 END) AS swap_volume,
  //               SUM(CASE WHEN trans_type = 'BANK_DEPOSIT' THEN amount ELSE 0 END) AS bank_deposit_volume,
  //               SUM(amount) AS net_volume,
  //               SUM(ABS(amount)) AS total_movement,
  //               COUNT(*) AS transaction_count
  //           FROM 
  //               transactions
  //           WHERE 
  //               trans_type IN ('PUSH', 'PULL', 'SWAP', 'BANK_DEPOSIT')
  //               AND status = 'SUCCESS'
  //               ${dateFilter}
  //           GROUP BY 
  //               currency
  //           ORDER BY 
  //               total_movement DESC`;

  //     const reports = await this.callQuerySafe(sql);
  //     return this.makeResponse(200, "System volume reports fetched successfully", {
  //       reports: reports,
  //       datePeriod: {
  //         start: start_date || getFirstDayOfCurrentMonth(),
  //         end: end_date || getCurrentDateMySQL(),
  //         filter: dateFilter ? 'custom' : 'default'
  //       }
  //     });
  //   } catch (error) {
  //     console.error("Error fetching system volume:", error);
  //     return this.makeResponse(500, "Error fetching system volume");
  //   }
  // }

  async volumeStats(data: any) {
    try {
      let { start_date, end_date, page, size } = data;

      // Determine date range
      let dateFilter = '';
      if (start_date && end_date && start_date !== "" && end_date !== "") {
        const formattedStartDate = await formatDateForMySQL(start_date);
        const formattedEndDate = await formatDateForMySQL(end_date);
        dateFilter = ` AND created_at >= '${formattedStartDate}' AND created_at <= '${formattedEndDate}'`;
      } else {
        const firstDayOfMonth = getFirstDayOfCurrentMonth();
        const today = getCurrentDateMySQL();
        dateFilter = ` AND created_at >= '${firstDayOfMonth}' AND created_at < '${today}'`;
      }

      // Base SQL
      const baseSql = `
        FROM transactions
        WHERE 
          trans_type IN ('PUSH', 'PULL', 'SWAP', 'BANK_DEPOSIT')
          AND status = 'SUCCESS'
          ${dateFilter}
        GROUP BY currency
      `;

      // Count total groups
      const countSql = `SELECT COUNT(*) as total ${baseSql}`;
      const countResult: any = await this.callQuerySafe(countSql);
      const totalItems = countResult?.[0]?.total || 0;

      // Pagination defaults
      let limit = size && !isNaN(size) ? Number(size) : 8;
      let offset = 0;
      let totalPages = 1;

      if (page && !isNaN(page)) {
        offset = (Number(page) - 1) * limit;
        totalPages = Math.ceil(totalItems / limit);
      }

      // Main query with pagination if requested
      const sql = `
        SELECT 
            currency,
            SUM(CASE WHEN trans_type = 'PUSH' THEN amount ELSE 0 END) AS push_volume,
            SUM(CASE WHEN trans_type = 'PULL' THEN amount ELSE 0 END) AS pull_volume,
            SUM(CASE WHEN trans_type = 'SWAP' THEN amount ELSE 0 END) AS swap_volume,
            SUM(CASE WHEN trans_type = 'BANK_DEPOSIT' THEN amount ELSE 0 END) AS bank_deposit_volume,
            SUM(amount) AS net_volume,
            SUM(ABS(amount)) AS total_movement,
            COUNT(*) AS transaction_count
        ${baseSql}
        ORDER BY total_movement DESC
        ${page ? `LIMIT ${limit} OFFSET ${offset}` : ''}
      `;

      const reports = await this.callQuerySafe(sql);

      // Response formatting
      if (page) {
        return this.makeResponse(200, "System volume reports fetched successfully", {
          items: reports,
          pagination: {
            current_page: Number(page),
            total_pages: totalPages,
            total_items: totalItems,
            items_per_page: limit,
            hasNextPage: Number(page) < totalPages,
            hasPrevPage: Number(page) > 1,
            next_page: Number(page) < totalPages ? Number(page) + 1 : null,
            previous_page: Number(page) > 1 ? Number(page) - 1 : null
          },
          datePeriod: {
            start: start_date || getFirstDayOfCurrentMonth(),
            end: end_date || getCurrentDateMySQL(),
            filter: start_date && end_date ? 'custom' : 'default'
          }
        });
      }

      // Non-paginated response
      return this.makeResponse(200, "System volume reports fetched successfully", {
        reports: reports,
        datePeriod: {
          start: start_date || getFirstDayOfCurrentMonth(),
          end: end_date || getCurrentDateMySQL(),
          filter: start_date && end_date ? 'custom' : 'default'
        }
      });

    } catch (error) {
      console.error("Error fetching system volume:", error);
      return this.makeResponse(500, "Error fetching system volume");
    }
  }




  async getProfitLiquidityRailNetworkStats() {
    try {

      //const reports = await this.selectDataQuerySafe("transactions", `status = 'SUCCESS'`);
      const reports: any = [{
        created_on: "",
        ex_rate: "",
        transId: "",
        amount: "",
        currency: "",
        spread: "",
        fee: ""
      }]; // await this.callQuerySafe(`SELECT * FROM transactions WHERE status = 'SUCCESS'`);
      return this.makeResponse(200, "System volume reports fetched successfully", reports);
    } catch (error: any) {
      console.error("Error fetching system volume:", error);
      return this.makeResponse(500, "Error fetching system volume");
    }

  }


  async getProfitMudapayStats(data: any) {
    try {
      let { start_date, end_date, trans_type, currency } = data;

      // Determine date range
      let dateFilter = '';
      if (start_date && end_date && start_date !== "" && end_date !== "") {
        const formattedStartDate = await formatDateForMySQL(start_date);
        const formattedEndDate = await formatDateForMySQL(end_date);
        dateFilter = ` AND created_at >= '${formattedStartDate}' AND created_at <= '${formattedEndDate}'`;
        // dateFilter = ` AND created_at BETWEEN '${formattedStartDate}' AND '${formattedEndDate}'`;
      } else {
        const firstDayOfMonth = getFirstDayOfCurrentMonth();
        const firstDayOfNextMonth = getFirstDayOfNextMonth();
        dateFilter = ` AND created_at >= '${firstDayOfMonth}' AND created_at <= '${firstDayOfNextMonth}'`;
      }

      let transTypeFilter = '';
      if (trans_type && trans_type !== "") {
        transTypeFilter = ` AND trans_type = '${trans_type}'`;
      }

      let currencyTypeFilter = '';
      if (currency && currency !== "") {
        currencyTypeFilter = ` AND currency = '${currency}'`;
      }

      // Enhanced query to include fee information and additional fields
      const sql = `
        SELECT 
          trans_type,
          trans_id,
          amount,
          asset_code,
          currency,
          status,
          fee,
          provider_fees,
          created_at
        FROM transactions 
        WHERE status = 'SUCCESS' 
        ${dateFilter} 
        ${transTypeFilter}
        ${currencyTypeFilter}
        ORDER BY created_at DESC
      `;
      
      const rawReports: any = await this.callQuerySafe(sql);
      // Map and calculate additional fee fields
      const mappedReports: any = rawReports.map((transaction: any) => {
        return {
          ...transaction,
          muda_fees: transaction?.fee ?? "",
          provider_fees: transaction?.provider_fees ?? "",
          profit: Number(transaction?.fee) - Number(transaction?.provider_fees)
        };
      });

      return this.makeResponse(200, "Muda pay profit reports fetched successfully", {
        transactions: mappedReports,
        dateRange: {
          start: start_date || getFirstDayOfCurrentMonth(),
          end: end_date || getFirstDayOfNextMonth()
        },
        filters: {
          trans_type: trans_type || 'All'
        }
      });
    } catch (error: any) {
      console.error("Error fetching Muda pay profit reports:", error);
      return this.makeResponse(500, "Error fetching Muda pay profit reports");
    }
  }



  async profitonTradeStats() {
    try {

      const reports: any = await this.callQuerySafe(`SELECT  id, product_id, trans_type,trans_id,amount,asset_code,currency,
                                                         status,fee,service_name, created_at
                                                           FROM transactions ORDER BY created_at DESC`);
      return this.makeResponse(200, "System profit on trade reports fetched successfully", reports);
    } catch (error: any) {
      console.error("Error fetching system profit:", error);
      return this.makeResponse(500, "Error fetching system profit");
    }
  }



  async twoFaAuthAccountStatus(data: any) {
    try {

      if (data?.clientId === undefined) {
        return this.makeResponse(400, "Unknown user account");
      }
      // console.log("data log >>>", data)

      let dataStatus: any = { status: '' }

      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'admin' })
      // console.log("data log client >>>", client)
      if (client[0]?.status) {
        dataStatus = { status: client[0].status }
      }
      return this.makeResponse(200, "user 2fa status successfully", dataStatus);

    } catch (error: any) {

      console.error("Error creating system user:", error);
      return this.makeResponse(500, "Error creating system user");
    }
  }

  async twoFaAuthAccountCode(data: any) {
    try {

      if (data?.clientId === undefined) {
        return this.makeResponse(200, "Unknown user account");
      }

      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'admin' })
      if (client.lenght > 0) {
        const clientData = { status: client[0].secret }
        return this.makeResponse(200, "Unknown user account", clientData);
      }

      if (client[0]?.secret && client[0]?.secret !== "") {
        return this.makeResponse(200, "User 2fa code created successfully",
          { code: client[0]?.secret, url: client[0]?.qr_code });
      }
      const authCode: any = await TwoFactorAuthHelper.generateSecret({ ...data, user_type: 'admin' });
      const userData = {
        id: uuidv4(),
        user_id: data.clientId,
        secret: authCode?.secret,
        user_type: 'admin',
        qr_code: authCode?.qrCode,
        code: authCode?.data?.data?.hex,
        status: 'pending'
      };
      await this.insertData("user_2fa", userData);
      return this.makeResponse(200, "User user 2fa successfully", { code: authCode?.data?.data?.base32, url: authCode?.qrCode });

    } catch (error: any) {

      console.error("Error creating system user:", error);
      return this.makeResponse(500, "Error generating user 2fa code");
    }
  }

  async twoFaAuthVerify(id: string, data: any) {
    try {

      if (data?.clientId === undefined) {
        return this.makeResponse(400, "Unknown user account");
      }

      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'admin' })
      if (client.length === 0) {
        data = { status: client[0]?.secret ?? "" }
        return this.makeResponse(200, "Unknown 2fa secret");
      }

      const responseData: any = await TwoFactorAuthHelper.verifySecret(client[0].secret, data?.token)
      // console.log("responseData  2fa token verify  - ", responseData); // SECURITY: Removed token logging
      if (responseData?.status) {
        const userData = {
          status: 'active'
        };
        await this.updateData("user_2fa", `user_id = '${data?.clientId}'`, userData);
        return this.makeResponse(200, "User 2FA verification approved", responseData);
      }

      return this.makeResponse(200, "User 2FA verification failed", responseData);
    } catch (error: any) {

      console.error("Error verifying 2fa code:", error);
      return this.makeResponse(500, "Error verifying 2fa code");
    }
  }



  async twoFaAuthUpdate(id: string, data: any) {
    try {

      if (data?.clientId === undefined) {
        return this.makeResponse(400, "Unknown user account");
      }

      if (!['active', 'inactive'].includes(data.status)) {
        return this.makeResponse(400, `Status must be either 'active' or 'inactive'`);
      }

      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'admin' })
      if (client.lenght === 0) {
        data = { status: client[0].secret }
        return this.makeResponse(200, "Unknown 2fa secret");
      }
      const theData: any = {
        status: data?.status
      }

      const saved: any = await this.updateData("user_2fa", `user_id = '${data?.clientId}' AND user_type = 'admin' AND deleted_at IS NULL`, theData)
      if (data?.status === 'active') {
        return this.makeResponse(200, "User 2fa  has been activated successfully");
      } else {
        return this.makeResponse(200, "User 2fa has been deactivated successfully");
      }
    } catch (error: any) {

      console.error("Error creating system user:", error);
      return this.makeResponse(500, "Error creating system user");
    }
  }

  async addApproveBusinessKyc(client_id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "businessApprove");
      const accessRight2 = await this.getLoggedUserAccessRights(data?.clientId, "clientsApprove");
      if ((!accessRight || !accessRight2) || (!accessRight && !accessRight2)) {
        return this.makeResponse(405, "You are not authorized to approve business kyc", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair: any = await this.callQuerySafe("SELECT * FROM clients WHERE client_id = ? AND kyc_status = 'verified'", [client_id]);
      if (checkPair.length > 0) {
        return this.makeResponse(400, "Business kyc is already verified");
      }

      await this.updateData("clients", `client_id = '${client_id}'`, { kyc_status: 'verified' });
      return this.makeResponse(200, "Business kyc approved successfully", {});

    } catch (error: any) {
      return this.makeResponse(500, "Error approving client kyc");
    }

  }



  /*****
  * 
  *   add business 
  * 
  * */

  async createBusiness(data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "businessEdit");
      const accessRight2 = await this.getLoggedUserAccessRights(data?.clientId, "clientsCreate");
      if ((!accessRight || !accessRight2) || (!accessRight && !accessRight2)) {
        return this.makeResponse(405, "You are not authorized to create business", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      if (data?.clientId === undefined) {
        return this.makeResponse(400, "Unknown user account");
      }


      const checkEmail: any = await this.selectDataQuerySafe("clients", { contact_email: data.contact_email });
      if (checkEmail.length > 0) {
        return this.makeResponse(400, "Client email already taken");
      }

      const checkPair_: any = await this.callQuerySafe("SELECT * FROM maker_checker WHERE status = 'pending' AND JSON_UNQUOTE(JSON_EXTRACT(data_content, '$.contact_email')) = ?", [data.contact_email]);
      if (checkPair_.length > 0) {
        return this.makeResponse(206, "Business with same email already placed for verification");
      }

      const checkContactEmail: any = await this.selectDataQuerySafe("client_logins", { email: data.contact_email });
      if (checkContactEmail.length > 0) {
        return this.makeResponse(400, "Client contact email already taken");
      }

      const checkPhone: any = await this.selectDataQuerySafe("clients", { phone_number: data.phone_number });
      if (checkPhone.length > 0) {
        return this.makeResponse(400, "Client phone number already taken");
      }


      const checkName: any = await this.selectDataQuerySafe("clients", { business_name: data.business_name });
      if (checkName.length > 0) {
        return this.makeResponse(400, "Client business name already taken");
      }

      const client_id = this.generateRandom4DigitNumber()
      const saveData = {
        client_id: client_id,
        business_name: data.business_name,
        contact_email: data.contact_email,
        phone_number: data.phone_number,
        address: data.address,
        contact_person_name: data.contact_person_name,
        contact_phone: data.contact_phone,
        status: "pending"
      };

      const saveData_ = {
        id: uuidv4(),
        data_content: JSON.stringify(saveData),
        entry_type: "add_business",
        status: "pending",
        maker_id: data.clientId,
        created_at: this.getMySQLDateTime(),
        updated_at: this.getMySQLDateTime()
      }
      const result = await this.insertData("maker_checker", saveData_);
      return this.makeResponse(200, "Business added for approval successfully", result);

    } catch (error: any) {

      console.error("Error adding app pair price >>>>>>>>>>:", error);
      return this.makeResponse(500, "Error adding business for approval");
    }
  }


  async addApproveBusiness(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "businessApprove");
      const accessRight2 = await this.getLoggedUserAccessRights(data?.clientId, "clientsApprove");
      if ((!accessRight || !accessRight2) || (!accessRight && !accessRight2)) {
        return this.makeResponse(405, "You are not authorized to approve business", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status && data.status !== 'rejected') {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair = await this.selectDataQuerySafe("maker_checker", { id });
      if (checkPair.length === 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId)) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }


      const dataContent = JSON.parse(checkPair[0].data_content);

      if (data?.status === 'approved') {
        const checkEmail: any = await this.selectDataQuerySafe("clients", { contact_email: dataContent.contact_email });
        if (checkEmail.length > 0) {
          return this.makeResponse(400, "Client email already taken");
        }

        const checkContactEmail: any = await this.selectDataQuerySafe("client_logins", { email: dataContent.contact_email });
        if (checkContactEmail.length > 0) {
          return this.makeResponse(400, "Client contact email already taken");
        }

        const checkPhone: any = await this.selectDataQuerySafe("clients", { phone_number: dataContent.phone_number });
        if (checkPhone.length > 0) {
          return this.makeResponse(400, "Client phone number already taken");
        }

        const checkName: any = await this.selectDataQuerySafe("clients", { business_name: dataContent.business_name });
        if (checkName.length > 0) {
          return this.makeResponse(400, "Client business name already taken.");
        }
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data?.clientId, reason: data?.reason, status: data?.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "Business creation request was rejected");
      }
      const saveData = {
        client_id: dataContent?.client_id,
        business_name: dataContent?.business_name,
        contact_email: dataContent?.contact_email,
        phone_number: dataContent?.phone_number,
        address: dataContent?.address,
        contact_person_name: dataContent?.contact_person_name,
        contact_phone: dataContent?.contact_phone,
        status: "active",
        default_password: true
      };

      const wallet = await this.createWallet(dataContent?.client_id)
      const clientId = await this.insertData("clients", saveData);
      const password = this.generateSecurePassword(12, true);
      const encryptedPassword = await this.generatePassword(password);
      const defaultRole: any = await this.defaultRole();
      const adminData = {
        client_id: dataContent?.client_id,
        email: dataContent?.contact_email,
        password: encryptedPassword,
        first_name: dataContent?.contact_person_name.split(" ")[0],
        last_name: dataContent?.contact_person_name.split(" ")[1],
        role: defaultRole?.id,
        status: "active",
        default_password: true
      };
      await this.insertData("client_logins", adminData);
      this.sendEmail("CLIENT_REGISTRATION", dataContent?.contact_email, dataContent?.first_name ?? "", password);

      return this.makeResponse(200, "Business added successfully", clientId);

    } catch (error: any) {
      return this.makeResponse(500, "Error adding business");
    }
  }



  async updateBusiness(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "businessEdit");
      const accessRight2 = await this.getLoggedUserAccessRights(data?.clientId, "clientsEdit");
      if ((!accessRight || !accessRight2) || (!accessRight && !accessRight2)) {
        return this.makeResponse(405, "You are not authorized to update business", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const existingAdmin: any = await this.selectDataQuerySafe("clients", { client_id: id });
      if (existingAdmin.length === 0) {
        return this.makeResponse(400, "Business is not known");
      }

      let saveData: any = {
        business_name: data?.business_name,
        phone_number: data?.phone_number,
        address: data?.address,
        contact_person_name: data?.contact_person_name,
        contact_phone: data?.contact_phone,
        status: data?.status
      };

      await this.updateData("clients", `client_id = '${data.id}'`, saveData);
      return this.makeResponse(200, "Business updated successfully");

    } catch (error: any) {
      console.error("Error Role:", error);
      return this.makeResponse(500, "Error updating business details");
    }
  }

  async AdminActions(data: any) {
    this.saveOperationLog("BULK_UPDATE_BUSINESS_ADMIN_DETAILS", data?.clientId || data?.client_id || "", data?.userid, "admin", "client_logins", "1", data, data.ip);
    if (data?.action === "send_bulk_email") {
      // return this.sendBulkEmail(data);
    }
    return this.makeResponse(400, "Invalid action");

  }

  async sendBulkEmail(data: any) {
    const { operation, receiver } = data;
    try {
      if (receiver === "admins") {
        const existingAdmin: any = await this.selectDataQuerySafe("clients");
        for (let i = 0; i < existingAdmin.length; i++) {
          const contact_email = existingAdmin[i].contact_email;
          const contact_person_name = existingAdmin[i].contact_person_name;
          await this.sendEmail(operation, contact_email, contact_person_name ?? "");
        }
      } else {
        return this.makeResponse(400, "Invalid receiver");
      }
      return this.makeResponse(200, "Bulk email sent successfully");
    } catch (error: any) {
      console.error("Error sending bulk email:", error);
      return this.makeResponse(500, "Error sending bulk email");
    }
  }


  async BulkUpdateBusinessAdminDetails(adminId: string) {


    const existingAdmin: any = await this.selectDataQuerySafe("client_logins");
    let successCount = 0;
    let errorCount = 0;
    for (let i = 0; i < existingAdmin.length; i++) {
      successCount++;
      let saveContactUserData: any = {}
      let password = this.generateSecurePassword(12, true);
      const encryptedPassword: any = await this.generatePassword(password);
      saveContactUserData.password = encryptedPassword;
      saveContactUserData.default_password = "true";
      saveContactUserData.default_password_expiry = this.getMySQLDateTime_future(12, "hours");
      console.log("SENDING EMAIL TO: " + i, existingAdmin[i].email);
      await this.updateData("client_logins", `id = '${existingAdmin[i].id}'`, saveContactUserData);
      await this.sendEmail("CLIENT_PASSWORD_CHANGE_BULK", existingAdmin[i].email, existingAdmin[i]?.first_name ?? "", password);
    }
    return this.makeResponse(200, "Business admin details updated successfully", { successCount, errorCount });
  }


  async updateBusinessAdminDetails(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "businessEdit");
      const accessRight2 = await this.getLoggedUserAccessRights(data?.clientId, "clientsEdit");
      if ((!accessRight || !accessRight2) || (!accessRight && !accessRight2)) {
        return this.makeResponse(405, "You are not authorized to update business admin details", []);
      }


      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const existingAdmin: any = await this.selectDataQuerySafe("client_logins", { id });
      if (existingAdmin.length === 0) {
        return this.makeResponse(400, "Business admin is not known");
      }

      const existingBusiness: any = await this.selectDataQuerySafe("clients", { client_id: existingAdmin[0].client_id });
      if (existingBusiness.length === 0) {
        return this.makeResponse(400, "Business is not known");
      }

      let saveContactUserData: any = {}
      let password = this.generateSecurePassword(12, true);
      if (data?.reset_password && data?.reset_password === true) {
        const encryptedPassword: any = await this.generatePassword(password);
        saveContactUserData.password = encryptedPassword;
        saveContactUserData.default_password = "true";
        saveContactUserData.default_password_expiry = this.getMySQLDateTime_future(4, "hours");
      }

      if (data?.reset_role && data?.reset_role === true) {
        const defaultRole: any = await this.defaultRole();
        saveContactUserData.role = defaultRole?.id;
      }
      if (data?.first_name !== "") {
        saveContactUserData.first_name = data?.first_name;
      }
      if (data?.last_name !== "") {
        saveContactUserData.last_name = data?.last_name;
      }
      if (data?.status !== "") {
        saveContactUserData.status = data?.status;
      }

      await this.updateData("client_logins", `id = '${existingAdmin[0].id}'`, saveContactUserData);
      if (data?.reset_password && data?.reset_password === true) {
        this.sendEmail("CLIENT_PASSWORD_CHANGE", existingAdmin[0].email, existingAdmin[0]?.first_name ?? "", password);
      }
      return this.makeResponse(200, "Business admin details updated successfully");

    } catch (error: any) {
      console.error("Error Role:", error);
      return this.makeResponse(500, "Error updating business admin details");
    }
  }



  async getClients(data: any) {
    try {
      // Check access rights
      const canViewBusiness = await this.getLoggedUserAccessRights(data?.clientId, "businessView");
      const canViewClients = await this.getLoggedUserAccessRights(data?.clientId, "clientsView");

      if (!canViewBusiness || !canViewClients) {
        return this.makeResponse(405, "You are not authorized to view clients", []);
      }

      // Pagination handling
      if (data?.page && Number(data.page) >= 0) {
        const limit = Number(data.size) || 10;
        const currentPage = Number(data.page);
        const offset = (currentPage - 1) * limit;

        // Search filters
        const searchAddress = data?.address?.trim();
        const searchPhone = data?.phone?.trim();
        const searchTerm = data?.search?.trim();

        // Build search query dynamically
        const conditions: string[] = [];
        if (searchTerm) {
          conditions.push(`(business_name LIKE '%${searchTerm}%' OR contact_email LIKE '%${searchTerm}%')`);
        }
        if (searchAddress) {
          conditions.push(`address LIKE '%${searchAddress}%'`);
        }
        if (searchPhone) {
          conditions.push(`phone_number LIKE '%${searchPhone}%'`);
        }

        const searchQuery = conditions.length > 0
          ? `WHERE ${conditions.join(" AND ")}`
          : `WHERE client_id != ''`;

        // Get total count
        const totalResult: any = await this.callQuerySafe(`SELECT COUNT(*) AS total FROM clients ${searchQuery}`);
        const totalItems = totalResult[0]?.total || 0;
        const totalPages = Math.ceil(totalItems / limit);

        // Pagination values
        const nextPage = currentPage + 1 <= totalPages ? currentPage + 1 : null;
        const previousPage = currentPage > 1 ? currentPage - 1 : null;

        // Fetch paginated data
        const users = await this.callQuerySafe(`SELECT * FROM clients ${searchQuery} LIMIT ${limit} OFFSET ${offset}`) as any[];
        const total_businesses = await this.callQuerySafe(`SELECT COUNT(*) AS total FROM clients ${searchQuery}`) as any[];
        const total_businesses_pending =
          await this.callQuerySafe(`SELECT COUNT(*) AS total FROM clients ${searchQuery} AND status = 'pending'`) as any[];
        const total_businesses_rejected =
          await this.callQuerySafe(`SELECT COUNT(*) AS total FROM clients ${searchQuery} AND status = 'rejected'`) as any[];
        const total_businesses_active =
          await this.callQuerySafe(`SELECT COUNT(*) AS total FROM clients ${searchQuery} AND status = 'active'`) as any[];

        return this.makeResponse(200, "clients", {
          items: users,
          pagination: {
            current_page: currentPage,
            next_page: nextPage,
            previous_page: previousPage,
            total_pages: totalPages,
            total_items: totalItems,
            items_per_page: limit
          },
          totals: {
            total_businesses: (total_businesses as any[])[0]?.total,
            total_businesses_pending: (total_businesses_pending as any[])[0]?.total,
            total_businesses_rejected: (total_businesses_rejected as any[])[0]?.total,
            total_businesses_active: (total_businesses_active as any[])[0]?.total
          }
        });
      }

      // No pagination, return all clients
      const allClients = await this.selectDataQuerySafe("clients");
      return this.makeResponse(200, "clients", allClients);

    } catch (error: any) {
      console.error("Error fetching system users:", error);
      return this.makeResponse(500, "Error fetching system users");
    }
  }



  async getClientById(clientId: string) {
    try {
      const client = await this.selectDataQuerySafe("clients", { client_id: clientId });
      if (client.length === 0) {
        return this.makeResponse(400, "Client not found");
      }

      return this.makeResponse(200, "Client fetched successfully", client[0]);
    } catch (error: any) {
      console.error("Error fetching client:", error);
      return this.makeResponse(500, "Error fetching client");
    }
  }


  async getBusinessesToBeApproved(data: any) {
    try {
      // Check access rights
      const canViewBusiness = await this.getLoggedUserAccessRights(data?.clientId, "businessView");
      const canViewClients = await this.getLoggedUserAccessRights(data?.clientId, "clientsView");
      if (!canViewBusiness || !canViewClients) {
        return this.makeResponse(405, "You are not authorized to view pending business", []);
      }

      // Status filter
      const currentStatus = data?.status || "pending";

      let businesses: any[] = [];
      let totalItems = 0;

      if (data?.page) {
        // Pagination params
        const currentPage = parseInt(data.page) > 0 ? parseInt(data.page) : 1;
        const limit = parseInt(data?.size) > 0 ? parseInt(data.size) : 8; // default 8
        const offset = (currentPage - 1) * limit;

        // Base where clause
        let whereClause = ` WHERE ( entry_type = 'add_business' OR entry_type = 'edit_business' ) AND status = '${currentStatus}'`;

        // Search filter (string in JSON text, excluding status key)
        if (data?.search && typeof data.search === "string" && data.search.trim() !== "") {
          const searchStr = data.search.trim().replace(/'/g, "''");
          whereClause += ` AND data_content LIKE '%${searchStr}%' 
                          AND data_content NOT LIKE '%"status":"${searchStr}%'`;
        }

        // Count query
        const countResult: any = await this.callQuerySafe(`SELECT COUNT(*) AS total FROM maker_checker ${whereClause}`);
        totalItems = countResult.length > 0 ? parseInt(countResult[0].total) : 0;

        // Paginated query
        businesses = await this.callQuerySafe(
          `SELECT * FROM maker_checker ${whereClause} ORDER BY created_at DESC LIMIT ${limit} OFFSET ${offset}`
        ) as any;

        const totalPages = Math.ceil(totalItems / limit);
        const nextPage = currentPage < totalPages ? currentPage + 1 : null;
        const previousPage = currentPage > 1 ? currentPage - 1 : null;

        if (businesses.length === 0) {
          return this.makeResponse(400, "No business requests", {
            items: [],
            pagination: {
              current_page: currentPage,
              next_page: nextPage,
              previous_page: previousPage,
              total_pages: totalPages,
              total_items: totalItems,
              items_per_page: limit
            }
          });
        }

        // Parse JSON content
        const businessesData: any[] = await Promise.all(
          businesses.map(async (contentData: any) => ({
            ...contentData,
            data_content: JSON.parse(contentData.data_content)
          }))
        );

        return this.makeResponse(200, "Business listed successfully", {
          items: businessesData,
          pagination: {
            current_page: currentPage,
            next_page: nextPage,
            previous_page: previousPage,
            total_pages: totalPages,
            total_items: totalItems,
            items_per_page: limit
          }
        });

      } else {
        // No pagination
        const businesses: any = await this.callQuerySafe(
          "SELECT * FROM maker_checker WHERE (entry_type = 'add_business' OR entry_type = 'edit_business') AND status = ? ORDER BY created_at DESC",
          [currentStatus]
        );

        if (businesses.length === 0) {
          return this.makeResponse(400, "No business requests", []);
        }

        const businessesData: any[] = await Promise.all(
          businesses.map(async (contentData: any) => ({
            ...contentData,
            data_content: JSON.parse(contentData.data_content)
          }))
        );

        return this.makeResponse(200, "Business listed successfully", businessesData);
      }

    } catch (error: any) {
      return this.makeResponse(500, "Error fetching app business requests");
    }
  }






  /*****
  *   add business custom fees
  * */
  async addApproveBusinessFees(id: string, data: any): Promise<any> {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "businessEdit");
      const accessRight2 = await this.getLoggedUserAccessRights(data?.clientId, "clientsEdit");
      if ((!accessRight || !accessRight2) || (!accessRight && !accessRight2)) {
        return this.makeResponse(405, "You are not authorized to add business custom fees", []);
      }

      const responseData = await this.confirmUser2Fa(data);
      if (!responseData?.status && data.status !== 'rejected') return this.makeResponse(400, responseData?.message);


      const checkPair: any = await this.callQuerySafe("SELECT * FROM maker_checker WHERE id = ?", [id]);
      if (checkPair.length === 0) return this.makeResponse(400, "Request entry not found");
      if (checkPair[0].status !== 'pending') return this.makeResponse(400, "Request entry already processed");
      if (String(checkPair[0].maker_id) === String(data.clientId)) return this.makeResponse(400, "A maker cannot approve their own request");

      await this.updateData("maker_checker", `id = '${id}'`, {
        approved_at: this.getMySQLDateTime(),
        checker_id: data.clientId,
        reason: data.reason,
        status: data.status
      });

      if (data.status === 'rejected') return this.makeResponse(200, "Business custom fees creation request was rejected");

      const dataContent = JSON.parse(checkPair[0].data_content);

      // check if entry exists
      const existingEntry: any = await this.callQuerySafe("SELECT * FROM custome_fees WHERE client_id = ? AND product_id = ?", [dataContent.client_id, dataContent.product_id]);
      if (existingEntry.length > 0) {
        // delete existing fees
        await this.deleteData("custome_fees", `client_id = '${dataContent.client_id}' AND product_id = '${dataContent.product_id}'`);
      }

      const saveData = {
        id: dataContent.id,
        client_id: dataContent.client_id,
        product_id: dataContent.product_id,
        fee_type: dataContent.fee_type,
        amount: dataContent.amount,
        active_status: dataContent.active_status
      };

      const result = await this.insertData("custome_fees", saveData);
      if (dataContent.fee_type === "TIER") {

        console.log("dataContent.tier_fee_range", dataContent);
        dataContent.tier_fee_range = (dataContent.tier_fee_range && typeof dataContent.tier_fee_range === "string") ? JSON.parse(dataContent.tier_fee_range) : dataContent.tier_fee_range;
        await this.saveTierFees(dataContent, dataContent.id, "custome_fee");
      }
      return this.makeResponse(200, "Business custom fees added successfully", result);


    } catch (error: any) {
      console.error("Error approving business fees:", error);
      return this.makeResponse(500, "Error adding business fees");
    }
  }


  async updateBusinessFees(product_id: string, client_id: string, data: any): Promise<any> {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "businessEdit");
      const accessRight2 = await this.getLoggedUserAccessRights(data?.clientId, "clientsEdit");
      if ((!accessRight || !accessRight2) || (!accessRight && !accessRight2)) {
        return this.makeResponse(405, "You are not authorized to update business custom fees", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) return this.makeResponse(200, responseData?.message);
      if (!data?.clientId) return this.makeResponse(400, "Unknown user account");

      const clientDetails = await this.selectDataQuerySafe("clients", { client_id: client_id });
      if (clientDetails.length === 0) return this.makeResponse(400, "Business is not known");

      const products: any = await this.callQuerySafe("SELECT * FROM products WHERE product_id = ?", [product_id]);
      if (products.length === 0) return this.makeResponse(400, "Fee product is not known");

      if (data.fee_type == "percentage" && Number(data.amount) > 100) return this.makeResponse(400, "Percentage fee type amount cannot be greater than 100");
      if (Number(data.amount) <= 0 && data.fee_type !== "TIER") return this.makeResponse(400, "Fee cannot be less than 0");


      if (!["PERCENTAGE", "FLAT", "TIER"].includes(data.fee_type)) {
        return this.makeResponse(400, "Invalid fee type");
      }

      // if tier make sure tier_fee_range is given and has atleast 1 object
      if (data.fee_type === "TIER") {
        if (!data.tier_fee_range || data.tier_fee_range.length === 0) {
          return this.makeResponse(400, "Tier fee range is required");
        }

        for (const tier_fee_range of data.tier_fee_range) {
          if (!tier_fee_range.min_amount || !tier_fee_range.max_amount || !tier_fee_range.fee_amount) {
            return this.makeResponse(400, "Tier fee range is required");
          }
        }
      }



      const feeType = ["PERCENTAGE", "FLAT", "TIER"];
      if (!feeType.includes(data.fee_type)) {
        return this.makeResponse(400, "Invalid fee type");
      }

      // if tier make sure tier_fee_range is given and has atleast 1 object
      if (data.fee_type === "TIER") {

        if (!data.tier_fee_range || data.tier_fee_range.length === 0) {
          return this.makeResponse(400, "Tier fee range is required");
        }

        for (const tier_fee_range of data.tier_fee_range) {
          if (!tier_fee_range.min_amount || !tier_fee_range.max_amount || !tier_fee_range.fee_amount) {
            return this.makeResponse(400, "Tier fee range is required");
          }
        }

      }


      const fees: any = await this.callQuerySafe(
        "SELECT * FROM custome_fees WHERE client_id = ? AND product_id = ? AND deleted_at IS NULL ORDER BY created_at DESC LIMIT 1",
        [client_id, product_id]
      );
      if (fees.length === 0) {
        const checkPending: any = await this.callQuerySafe(
          "SELECT * FROM maker_checker WHERE entry_type = 'add_business_fees' AND status = 'pending' AND JSON_UNQUOTE(JSON_EXTRACT(data_content, '$.product_id')) = ? AND JSON_UNQUOTE(JSON_EXTRACT(data_content, '$.client_id')) = ?",
          [product_id, client_id]
        );
        if (checkPending.length > 0) return this.makeResponse(206, "Business custom fees already placed for verification");

        const saveData = {
          id: uuidv4(),
          client_id: client_id,
          product_id: product_id,
          fee_type: data.fee_type,
          amount: data.amount,
          active_status: data.active_status,
          tier_fee_range: data.tier_fee_range,
          product_name: products[0].product_name,
          product_code: products[0].product_code,
          product_transaction_type: products[0].transaction_type,
          product_currency: products[0].currency
        };

        const saveData_ = {
          id: uuidv4(),
          data_content: JSON.stringify(saveData),
          entry_type: "add_business_fees",
          status: "pending",
          maker_id: data.clientId,
          created_at: this.getMySQLDateTime(),
          updated_at: this.getMySQLDateTime()
        };

        const result = await this.insertData("maker_checker", saveData_);
        return this.makeResponse(200, "Business fees successfully added for approval", result);
      }


      const feesData = {
        amount: data.amount,
        fee_type: data.fee_type,
        active_status: data.active_status
      };

      await this.updateData("custome_fees", `client_id = '${client_id}' AND product_id = '${product_id}'`, feesData);
      if (data.fee_type === "TIER") {
        await this.saveTierFees(data, fees[0].id, "custome_fee");
      }


      return this.makeResponse(200, "Client custom fee updated successfully");
    } catch (error: any) {
      console.error("Error editing business fees:", error);
      return this.makeResponse(500, "Error editing business custom fee");
    }
  }


  async deleteBusinessFees(client_id: string, product_id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "businessEdit");
      const accessRight2 = await this.getLoggedUserAccessRights(data?.clientId, "clientsEdit");
      if ((!accessRight || !accessRight2) || (!accessRight && !accessRight2)) {
        return this.makeResponse(405, "You are not authorized to delete business custom fees", []);
      }


      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) return this.makeResponse(200, responseData?.message);

      const clientDetails = await this.selectDataQuerySafe("clients", { client_id });
      if (clientDetails.length === 0) return this.makeResponse(400, "Business is not known");

      const products = await this.selectDataQuerySafe("products", { product_id });
      if (products.length === 0) return this.makeResponse(400, "Fee product is not known");

      const feesData = {
        deleted_at: this.getMySQLDateTime(),
        product_id: product_id,
        client_id: client_id
      };
      const fees: any = await this.callQuerySafe("SELECT * FROM custome_fees WHERE client_id = ? AND product_id = ? AND deleted_at IS NULL ORDER BY created_at DESC LIMIT 1", [client_id, product_id]);
      if (fees.length > 0) {
        await this.deleteData("tier_fees", `custome_fee_id = '${fees[0].id}'`);
        // log save delete custome fees - tiered 
        await this.saveOperationLog("DELETE", data?.clientId, data?.userId || data?.userid || "", "admin", "tier_fees", product_id, feesData, "1")
      }

      await this.deleteData("custome_fees", `client_id = '${client_id}' AND product_id = '${product_id}'`);
      // log save delete custome fees
      await this.saveOperationLog("DELETE", data?.clientId, data?.userId || data?.userid || "", "admin", "custome_fees", product_id, feesData, "1")
      return this.makeResponse(200, "Client custom fee deleted successfully");

    } catch (error: any) {

      console.error("Error deleting business custom fee:", error);
      return this.makeResponse(500, "Error deleting business custom fee");
    }
  }

  async getAllBusinessFees(client_id: string, data: any) {
    try {
      // Access rights
      const canViewBusiness = await this.getLoggedUserAccessRights(data?.clientId, "businessView");
      const canViewClients = await this.getLoggedUserAccessRights(data?.clientId, "clientsView");
      if (!canViewBusiness || !canViewClients) {
        return this.makeResponse(405, "You are not authorized to view business fees", []);
      }

      const limit = parseInt(data?.size) > 0 ? parseInt(data.size) : 8;
      let whereClause = `1=1`;

      // Search filter
      if (data?.search && typeof data.search === "string" && data.search.trim() !== "") {
        const searchStr = data.search.trim().replace(/'/g, "''");
        whereClause += ` AND (fee_type LIKE '%${searchStr}%' OR product_id LIKE '%${searchStr}%' OR product_name LIKE '%${searchStr}%' OR product_code LIKE '%${searchStr}%' OR transaction_type LIKE '%${searchStr}%')`;
      }

      let fees: any[] = [];
      let totalItems = 0;

      if (data?.page) {
        // Pagination params
        const currentPage = parseInt(data.page) > 0 ? parseInt(data.page) : 1;
        const offset = (currentPage - 1) * limit;

        // Count total products
        const countResult: any = await this.callQuerySafe(`SELECT COUNT(*) AS total FROM products WHERE ${whereClause}`);
        totalItems = countResult.length > 0 ? parseInt(countResult[0].total) : 0;
        const totalPages = Math.ceil(totalItems / limit);
        const nextPage = currentPage < totalPages ? currentPage + 1 : null;
        const previousPage = currentPage > 1 ? currentPage - 1 : null;

        // Fetch paginated products
        fees = await this.callQuerySafe(`
          SELECT * FROM products 
          WHERE ${whereClause}
          ORDER BY created_at DESC 
          LIMIT ${limit} OFFSET ${offset}
        `) as any;

        // Enrich with custom fees
        const feesData = await Promise.all(
          fees.map(async (fee: any) => {

            const custom_fee: any[] = await this.selectDataQuerySafe(
              "custome_fees",
              { product_id: fee.product_id, client_id, deleted_at: null }
            );

            if (custom_fee.length > 0 && custom_fee[0].fee_type === "TIER") {
              custom_fee[0].tier_fees = await this.callQuerySafe("SELECT * FROM tier_fees WHERE custome_fee_id = ?", [custom_fee[0].id]);
            }

            return {
              ...fee,
              fee_amount: custom_fee[0]?.amount ?? fee?.fee_amount,
              custom_tag: (custom_fee[0]?.amount && custom_fee[0]?.amount !== "") ? "custom" : "",
              fee_type: custom_fee[0]?.fee_type ?? fee?.fee_type,
              custom_fee: custom_fee[0]
            };
          })
        );

        return this.makeResponse(200, "Business transaction fees fetched", {
          items: feesData,
          pagination: {
            current_page: currentPage,
            next_page: nextPage,
            previous_page: previousPage,
            total_pages: totalPages,
            total_items: totalItems,
            items_per_page: limit
          }
        });

      } else {
        // No pagination, still apply limit
        fees = await this.callQuerySafe(`
          SELECT * FROM products
          WHERE ${whereClause}
          ORDER BY created_at DESC 
          LIMIT ${limit}
        `) as any;

        const feesData = await Promise.all(
          fees.map(async (fee: any) => {
            const custom_fee: any[] = await this.selectDataQuerySafe(
              "custome_fees",
              { product_id: fee.product_id, client_id, deleted_at: null }
            );

            if (custom_fee.length > 0 && custom_fee[0].fee_type === "TIER") {
              custom_fee[0].tier_fees = await this.selectDataQuerySafe("tier_fees", { custome_fee_id: custom_fee[0].id });
            }


            return {
              ...fee,
              fee_amount: custom_fee[0]?.amount ?? fee?.fee_amount,
              custom_tag: (custom_fee[0]?.amount && custom_fee[0]?.amount !== "") ? "custom" : "",
              fee_type: custom_fee[0]?.fee_type ?? fee?.fee_type,
              custom_fee: custom_fee[0]
            };
          })
        );

        return this.makeResponse(200, "Business transaction fees fetched", feesData);
      }

    } catch (error: any) {
      console.error("Error getting all business fees:", error);
      return this.makeResponse(500, "Error getting all business fees");
    }
  }




  async getBusinessFee(client_id: string, product_id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "businessView");
      const accessRight2 = await this.getLoggedUserAccessRights(data?.clientId, "clientsView");
      if ((!accessRight || !accessRight2) || (!accessRight && !accessRight2)) {
        return this.makeResponse(405, "You are not authorized to view business fee", []);
      }

      const fees: any[] = await this.selectDataQuerySafe("products", { product_id });
      const feesData = await Promise.all(
        fees.map(async (fee: any) => {
          let custom_fee: any = await this.callQuerySafe("SELECT * FROM custome_fees WHERE product_id = ? AND client_id = ? AND deleted_at IS NULL ORDER BY created_at DESC", [fee.product_id, client_id])
          if (custom_fee.length > 0 && custom_fee[0].fee_type === "TIER") {
            custom_fee[0].tier_fees = await this.callQuerySafe("SELECT * FROM tier_fees WHERE custome_fee_id = ?", [custom_fee[0].id]);
          }

          return {
            ...fee,
            fee_amount: custom_fee[0]?.amount ?? fee?.fee_amount,
            custom_tag: (custom_fee[0]?.amount && custom_fee[0]?.amount !== "") ? "custom" : "",
            fee_type: custom_fee[0]?.fee_type ?? fee?.fee_type,
            custom_fee: custom_fee[0]
          };
        }));

      return this.makeResponse(200, "business transaction fee fetch", feesData);
    } catch (error: any) {
      console.error("Error getting a business fees:", error);
      return this.makeResponse(500, "Error getting a business fees");
    }
  }



  async getBusinessCustomFeesToBeApproved(data: any) {
    try {
      // Access rights
      const canApproveBusiness = await this.getLoggedUserAccessRights(data?.clientId, "businessApprove");
      const canApproveClients = await this.getLoggedUserAccessRights(data?.clientId, "clientsApprove");
      if (!canApproveBusiness || !canApproveClients) {
        return this.makeResponse(405, "You are not authorized to view business fees requests", []);
      }

      const currentStatus = "pending";
      let whereClause = `
        WHERE (maker_checker.entry_type = 'add_business_fees' OR maker_checker.entry_type = 'edit_business_fees') 
        AND maker_checker.status = '${currentStatus}'
      `;

      // Filter by client_id if given
      if (data?.client_id && data.client_id.trim() !== "") {
        whereClause += ` AND JSON_UNQUOTE(JSON_EXTRACT(data_content, '$.client_id')) = '${data.client_id}'`;
      }

      // Search filter (excluding "status" key)
      if (data?.search && typeof data.search === "string" && data.search.trim() !== "") {
        const searchStr = data.search.trim().replace(/'/g, "''");
        whereClause += ` AND data_content LIKE '%${searchStr}%' 
                         AND data_content NOT LIKE '%"status":"${searchStr}%'`;
      }

      let results: any[] = [];
      let totalItems = 0;

      if (data?.page) {
        // Pagination params
        const currentPage = parseInt(data.page) > 0 ? parseInt(data.page) : 1;
        const limit = parseInt(data?.size) > 0 ? parseInt(data.size) : 8;
        const offset = (currentPage - 1) * limit;

        // Count total
        const countResult = await this.callQuerySafe(`SELECT COUNT(*) AS total FROM maker_checker ${whereClause}`) as any[];
        totalItems = countResult.length > 0 ? parseInt(countResult[0].total) : 0;
        const totalPages = Math.ceil(totalItems / limit);
        const nextPage = currentPage < totalPages ? currentPage + 1 : null;
        const previousPage = currentPage > 1 ? currentPage - 1 : null;

        // Fetch paginated data
        results = await this.callQuerySafe(`
          SELECT * FROM maker_checker 
          ${whereClause}
          ORDER BY created_at DESC 
          LIMIT ${limit} OFFSET ${offset}
        `) as any[];

        if (results.length === 0) {
          return this.makeResponse(400, "No business fees requests", {
            items: [],
            pagination: {
              current_page: currentPage,
              next_page: nextPage,
              previous_page: previousPage,
              total_pages: totalPages,
              total_items: totalItems,
              items_per_page: limit
            }
          });
        }

        // Parse JSON content
        const parsedResults = await Promise.all(
          results.map(async (item: any) => ({
            ...item,
            data_content: JSON.parse(item.data_content)
          }))
        );

        return this.makeResponse(200, "Business fees listed successfully", {
          items: parsedResults,
          pagination: {
            current_page: currentPage,
            next_page: nextPage,
            previous_page: previousPage,
            total_pages: totalPages,
            total_items: totalItems,
            items_per_page: limit
          }
        });

      } else {
        // No pagination
        results = await this.callQuerySafe(`
          SELECT * FROM maker_checker 
          ${whereClause}
          ORDER BY created_at DESC
        `) as any[];

        if (results.length === 0) {
          return this.makeResponse(400, "No business fees requests", []);
        }

        const parsedResults = await Promise.all(
          results.map(async (item: any) => ({
            ...item,
            data_content: JSON.parse(item.data_content)
          }))
        );

        return this.makeResponse(200, "Business fees listed successfully", parsedResults);
      }

    } catch (error: any) {
      return this.makeResponse(500, "Error fetching app business fees");
    }
  }


  /*****
  * 
  *   update the users section 
  * 
  * */
  async createSystemUser(data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "usersCreate");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to create system user", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const { email, first_name, last_name, user_role } = data;
      const userRole: any = await this.selectDataQuerySafe("roles", { id: user_role });
      if (userRole.length === 0) {
        return this.makeResponse(400, "Unknown provided user role");
      }

      const existingAdmin: any = await this.selectDataQuerySafe("system_users", { email });
      if (existingAdmin.length > 0) {
        return this.makeResponse(400, "System user already exists");
      }

      const checkPair_ = await this.selectDataQuerySafe("maker_checker", { status: 'pending', data_content: { email } });
      if (checkPair_.length > 0) {
        return this.makeResponse(206, "Admin account already placed for verification");
      }

      const saveData = {
        id: uuidv4(),
        email: email,
        password: "",
        role: user_role,
        status: 'inactive',
        first_name: first_name,
        last_name: last_name
      };

      const saveData_ = {
        id: uuidv4(),
        data_content: JSON.stringify(saveData),
        entry_type: "add_admin",
        status: "pending",
        maker_id: data.clientId,
        created_at: this.getMySQLDateTime(),
        updated_at: this.getMySQLDateTime()
      }
      const result = await this.insertData("maker_checker", saveData_);
      return this.makeResponse(200, "User account placed for approval", result);
    } catch (error: any) {
      console.log("Error roles", error);
      return this.makeResponse(500, "Error adding account");
    }
  }

  async createApproveSystemUser(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "usersApprove");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to approve system user", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status && data.status !== 'rejected') {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair = await this.selectDataQuerySafe("maker_checker", { id });
      if (checkPair.length === 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId)) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data?.clientId, reason: data?.reason, status: data?.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "System user creation request was rejected");
      }
      const dataContent = JSON.parse(checkPair[0].data_content);
      const password = this.generateSecurePassword(12, true);
      const encryptedPassword: any = await this.generatePassword(password);


      const userData = {
        email: dataContent.email,
        password: encryptedPassword,
        role: dataContent.role,
        status: "active", //dataContent.status,
        first_name: dataContent.first_name,
        last_name: dataContent.last_name
      };
      const response: any = await this.insertData("system_users", userData);
      this.sendEmail("ADMIN_REGISTRATION", dataContent?.email, dataContent?.first_name, password);

      return this.makeResponse(200, "User created successfully");
    } catch (error: any) {

      console.log("errro on approving content", error);
      return this.makeResponse(500, "Error adding app system users");
    }
  }



  async updateSystemUser(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "usersEdit");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to update system user", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const { first_name, last_name } = data;

      const existingAdmin: any = await this.selectDataQuerySafe("system_users", { id });
      if (existingAdmin.length === 0) {
        return this.makeResponse(400, "System user is not known");
      }


      if (data?.user_role && data?.user_role !== "") {
        const userRole: any = await this.selectDataQuerySafe("roles", { id: data?.user_role });
        if (userRole.length === 0) {
          return this.makeResponse(400, "Unknown provided user role");
        }
      }

      let saveData: any = {
        updated_at: this.getMySQLDateTime()
      };

      let password: any;
      if (data?.reset_password && data?.reset_password === true) {
        password = this.generateSecurePassword(12, true);
        const encryptedPassword: any = await this.generatePassword(password);
        saveData.password = encryptedPassword;
      }

      if (data?.status && data?.status !== "") {
        saveData.status = data.status;
      }

      if (data?.first_name && data?.first_name !== "") {
        saveData.first_name = data.first_name;
      }

      if (data?.last_name && data?.last_name !== "") {
        saveData.last_name = data.last_name;
      }

      if (data?.user_role && data?.user_role !== "") {
        saveData.role = data.user_role;
      }


      await this.updateData("system_users", `id = '${id}'`, saveData);
      if (data?.reset_password && data?.reset_password === true) {
        this.sendEmail("USER_PASSWORD_CHANGE", existingAdmin[0].email, existingAdmin[0]?.first_name ?? "", password);
      }
      return this.makeResponse(200, "User account updated successfully");
    } catch (error: any) {


      console.error("Error Role:", error);
      return this.makeResponse(500, "Error updating app user");
    }
  }

  async updateApprovalSystemUser(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "usersApprove");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to approve system user", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair = await this.selectDataQuerySafe("maker_checker", { id });
      if (checkPair.length === 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId)) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data.clientId, reason: data?.reason, status: data.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "System user updating request was rejected");
      }
      const dataContent: any = JSON.parse(checkPair[0].data_content);


      let saveData: any = {
        name: dataContent.name,
        details: dataContent.details,
        status: dataContent.status,
        updated_at: dataContent.updated_at
      };

      let password: any;
      if (dataContent?.reset_password && dataContent?.reset_password === true) {
        password = this.generateSecurePassword(12, true);
        const encryptedPassword: any = await this.generatePassword(password);
        saveData.password = encryptedPassword;
      }

      if (dataContent?.role) {
        saveData.role = dataContent.role;
      }

      if (dataContent?.status) {
        saveData.status = dataContent.status;
      }

      await this.updateData("system_users", `id = '${dataContent.id}'`, saveData);
      if (dataContent?.reset_password && dataContent?.reset_password === true) {
        this.sendEmail("CLIENT_REGISTRATION", dataContent.email, dataContent.first_name, dataContent.password);
      }
      return this.makeResponse(200, "User account updated successfully");

    } catch (error: any) {
      console.error("Error adding role:", error);
      return this.makeResponse(500, "Error updating role");
    }
  }


  async getSystemUsersToBeApproved(data: any) {
    try {
      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "usersView");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to view system users requests", []);
      }

      const currentStatus = data?.status || "pending";
      const { page = 1, size = 8, search = '', maker_id = '' } = data || {};
      const limit = parseInt(size) > 0 ? parseInt(size) : 8;
      const offset = (page - 1) * limit;

      // Build search condition
      let searchCondition = '';
      if (search && search.trim() !== '') {
        const searchStr = search.trim().replace(/'/g, "''");
        searchCondition = `AND data_content LIKE '%${searchStr}%'`;
      }

      if (maker_id && maker_id.trim() !== '') {
        searchCondition = `AND maker_id = '${maker_id}'`;
      }

      // Count total
      const countQuery = `
        SELECT COUNT(*) AS total
        FROM maker_checker
        WHERE (entry_type = 'add_admin' OR entry_type = 'edit_admin')
          AND status = '${currentStatus}'
          ${searchCondition}
      `;
      const countResult: any = await this.callQuerySafe(countQuery);
      const totalItems = countResult[0]?.total || 0;

      // Fetch paginated data
      let usersDataRaw: any[] = [];
      if (data?.page) {
        usersDataRaw = await this.callQuerySafe(`
          SELECT 
              mc.*, 
              CONCAT(maker.first_name, ' ', maker.last_name) AS maker_name, 
              CONCAT(checker.first_name, ' ', checker.last_name) AS checker_name,
              r.name AS role_name
          FROM maker_checker mc
          LEFT JOIN system_users maker 
              ON mc.maker_id = maker.id
          LEFT JOIN system_users checker 
              ON mc.checker_id = checker.id
          LEFT JOIN roles r 
              ON r.id = JSON_UNQUOTE(JSON_EXTRACT(mc.data_content, '$.role'))
          WHERE 
              (mc.entry_type = 'add_admin' OR mc.entry_type = 'edit_admin')
              AND mc.status = '${currentStatus}'
              ${searchCondition}
          ORDER BY mc.created_at DESC
          LIMIT ${limit} OFFSET ${offset}
        `) as any[];
      } else {
        usersDataRaw = await this.callQuerySafe(
          `SELECT * FROM maker_checker WHERE (entry_type = 'add_admin' OR entry_type = 'edit_admin') AND status = '${currentStatus}' ${searchCondition} ORDER BY created_at DESC`
        ) as any[];
      }

      if (usersDataRaw.length === 0) {
        return this.makeResponse(400, "No user requests", data?.page ? {
          items: [],
          pagination: {
            current_page: page,
            total_pages: 0,
            total_items: 0,
            items_per_page: limit,
            hasNextPage: false,
            hasPrevPage: false,
            next_page: null,
            previous_page: null
          }
        } : []);
      }

      // Parse JSON content
      const usersData = usersDataRaw.map((item: any) => ({
        ...item,
        data_content: JSON.parse(item.data_content)
      }));

      // Pagination metadata
      const totalPages = Math.ceil(totalItems / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;
      const paginationInfo = {
        current_page: page,
        total_pages: totalPages,
        total_items: totalItems,
        items_per_page: limit,
        hasNextPage,
        hasPrevPage,
        next_page: hasNextPage ? Number(page) + 1 : null,
        previous_page: hasPrevPage ? Number(page) - 1 : null
      };

      return this.makeResponse(200, "Users listed successfully", data?.page ? {
        items: usersData,
        pagination: paginationInfo
      } : usersData);

    } catch (error: any) {
      console.error("Error fetching system users requests:", error);
      return this.makeResponse(500, "Error fetching system users requests");
    }
  }



  async userProfile(data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "usersView");
      if (!accessRight) {
        // return this.makeResponse(401, "You are not authorized to access", []);
      }

      if (data?.clientId === undefined) {
        return this.makeResponse(400, "Unknown user account");
      }

      const user = (await this.callQuerySafe(`SELECT u.*, r.name as role_name, 
          r.id as role_id, r.details as role_details, r.status as role_status
          FROM system_users u   
          LEFT JOIN roles r ON u.role = r.id
          WHERE u.id = '${data?.clientId}'`)) as any[];
      if (user.length === 0) {
        return this.makeResponse(400, "System user is not known");
      }

      delete user[0].password;
      let acessRights: any[] = [];
      // defult user role view rights

      let menuList: any[] = [];
      if (user[0].role === "0000-0000-0000-0003") {



        const acessRightsAll: any = await this.callQuerySafe(`
                                        SELECT r.*
                                        FROM access_rights r WHERE (r.type = 'admin' OR r.type IS NULL) AND r.deleted_at IS NULL
                                        ORDER BY r.created_at DESC
                                      `);
        const allowedRightsIds: any = ["d1d46ab5-41c1-11f0-bd21-16243d6fb08b", "d1d46d88-41c1-11f0-bd21-16243d6fb08b", "d1b17217-41c1-11f0-bd21-16243d6fb08b", "d17176b6-41c1-11f0-bd21-16243d6fb08b", "d149ac93-41c1-11f0-bd21-16243d6fb08b"]
        // add a new key to acessRightsAll that is access_rights_status
        acessRights = acessRightsAll.filter((role: any) => allowedRightsIds.includes(role.id));
        acessRights.forEach((role: any) => {
          role.access_rights_status = "active";
          role.access_rights_status_id = "";
          role.role_id = role.id;
        });

        const defaultRole: any = await this.defaultAdminRole();
        user[0].role_id = defaultRole?.id;
        user[0].role_name = defaultRole?.name;
        user[0].role_details = defaultRole?.details;
        user[0].role_status = defaultRole?.status;
        menuList = await this.getTheMenuList(user[0]);

      } else {

        acessRights = await this.getRoleAccessRights(user[0].role);
        menuList = await this.getTheMenuList(user[0]);
      }


      let userData: UserData = {
        id: user[0].id,
        first_name: user[0].first_name,
        last_name: user[0].last_name,
        email: user[0].email,
        role: user[0].role,
        role_details: {
          id: user[0].role_id,
          name: user[0].role_name,
          details: user[0].role_details,
          status: user[0].role_status,
          access_rights: acessRights
        },
        status: user[0].status,
        updated_at: user[0].updated_at,
        created_at: user[0].created_at,
        menu_list: menuList
      };
      return this.makeResponse(200, "user details", userData);
    } catch (error: any) {

      console.error("Error fetching user profile:", error);
      return this.makeResponse(500, "Error fetching user profile");
    }
  }

  async userDetails(id: any) {
    try {

      const user = (await this.callQuerySafe(`SELECT u.*, r.name as role_name,
            r.id as role_id, r.details as role_details, r.status as role_status
            FROM system_users u   
            LEFT JOIN roles r ON u.role = r.id
            WHERE u.id = '${id}'`)) as any[];
      if (user.length === 0) {
        return this.makeResponse(400, "System user is not known");
      }

      delete user[0].password;
      const acessRights = await this.getRoleAccessRights(user[0].role);
      const menuList = await this.getTheMenuList(user[0]);

      let userData: UserData = {
        id: user[0].id,
        first_name: user[0].first_name,
        last_name: user[0].last_name,
        email: user[0].email,
        role: user[0].role,
        role_details: {
          id: user[0].role_id,
          name: user[0].role_name,
          details: user[0].role_details,
          status: user[0].role_status,
          access_rights: acessRights
        },
        status: user[0].status,
        updated_at: user[0].updated_at,
        created_at: user[0].created_at,
        menu_list: menuList
      };

      return this.makeResponse(200, "user details", userData);

    } catch (error: any) {

      console.error("Error fetching user profile:", error);
      return this.makeResponse(500, "Error fetching user profile");
    }
  }


  async getTheMenuList(user: any) {   
    try {

     
      // const query = `
      //                   SELECT app_menu_items.*
      //                   FROM app_menu_items
      //                   INNER JOIN role_app_menu_items 
      //                     ON role_app_menu_items.app_menu_item_id = app_menu_items.id
      //                   WHERE app_menu_items.menu_type = 'ADMIN'
      //                     AND role_app_menu_items.deleted_at IS NULL
      //                     AND role_app_menu_items.role_id = '${user.role_id}'
      //                   ORDER BY app_menu_items.menu_precision ASC
      //                 `;

      const query = `
                      SELECT app_menu_items.*
                      FROM app_menu_items
                      WHERE app_menu_items.menu_type = 'ADMIN'
                      ORDER BY app_menu_items.menu_precision ASC
                    `;

      const menuList = await this.callRawQuery(query);
      const parentMenuList: any = menuList.filter((menu: any) => menu.parent_id === null);
      const childMenuList: any = menuList.filter((menu: any) => menu.parent_id !== null);
      
      // Add third-level children (grandchildren)
      const menuList_ = parentMenuList.map((parent: any) => {
        const children = childMenuList
          .filter((child: any) => child.parent_id === parent.id)
          .map((child: any) => {
            const grandChildren = childMenuList.filter(
              (grand: any) => grand.parent_id === child.id
            );
            return { ...child, children: grandChildren };
          });
      
        return { ...parent, children };
      });
      
      return menuList_;
      
    } catch (error: any) {

      console.log("Error getting menu list", error)
      return [];
    } 
  }  


  async resetPassword(data: any) {
    try {

      const { clientId, current_password, new_password } = data;
      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }


      if (!clientId || !current_password || !new_password) {
        return this.makeResponse(400, "Account current and new passwords are required");
      }

      // Verify current password
      const admin = await this.selectDataQuerySafe("system_users", { id: clientId });

      // Check if stored password exists and is valid
      if (!admin[0]?.password || typeof admin[0].password !== 'string') {
        return this.makeResponse(400, "Current password is incorrect");
      }

      const originalPassword = await this.verifyPassword(current_password, admin[0].password);
      if (originalPassword === false) {
        return this.makeResponse(400, "Current password is incorrect");
      }

      // Update with new password
      const newPassword_ = await this.generatePassword(new_password);
      await this.updateData("system_users", `id = '${clientId}'`, { password: newPassword_ });
      return this.makeResponse(200, "Password updated successfully");

    } catch (error: any) {

      console.error("Error resetting password:", error);
      return this.makeResponse(500, "Error resetting password");
    }
  }

  async changeProfile(data: any) {
    try {

      const { clientId, first_name, last_name } = data;
      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      if (!clientId || (!first_name && !last_name)) {
        return this.makeResponse(400, "Missing required fields");
      }
      const admin = await this.selectDataQuerySafe("system_users", { id: clientId });
      if (admin.length === 0) {
        return this.makeResponse(400, "User not found");
      }

      const updateData: any = {};
      if (first_name) updateData.first_name = first_name;
      if (last_name) updateData.last_name = last_name;
      await this.updateData("system_users", `id = '${clientId}'`, updateData);
      return this.makeResponse(200, "Profile updated successfully");
    } catch (error: any) {
      console.error("Error updating profile:", error);
      return this.makeResponse(500, "Error updating profile");
    }
  }





  /*****
   * 
   *   update the roles section 
   * */

  async addRole(data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "rolesCreate");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to create role", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }
      const VALIDATION = this.validateRolesData(data);
      if (!VALIDATION.success) {
        return this.makeResponse(400, "Invalid data", VALIDATION.error.errors);
      }
      const savedType: any = await this.callQuerySafe(`SELECT COUNT(*) as total FROM roles r WHERE r.name = '${data?.name}' AND r.deleted_at IS NULL`);
      if (savedType[0].total > 0) {
        return this.makeResponse(400, "Role name already exists");
      }
      
      const saveData = {
        id: uuidv4(),
        name: data?.name,
        details: data?.details || "",
        status: data?.status || 'active',
        created_at: this.getMySQLDateTime(),
        access_rights: data.access_right, 
        app_menu_items: data?.app_menu_items || []
      };
      const saveData_ = {
        id: uuidv4(),
        data_content: JSON.stringify(saveData),
        entry_type: "add_role",
        status: "pending",
        maker_id: data.clientId,
        created_at: this.getMySQLDateTime(),
        updated_at: this.getMySQLDateTime()
      }
      const result = await this.insertData("maker_checker", saveData_);
      return this.makeResponse(200, "Role placed for approval successfully", result);
    } catch (error: any) {
      console.log("Error roles", error);
      return this.makeResponse(500, "Error adding role");
    }
  }

  async addApproveRole(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "rolesApprove");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to approve role", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status && data.status !== 'rejected') {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair = await this.selectDataQuerySafe("maker_checker", { id });
      if (checkPair.length === 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      // A maker cannot approve their own request if the request is approved
      if (data?.status === 'approved' && String(checkPair[0].maker_id) === String(data.clientId)) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data?.clientId, reason: data?.reason, status: data?.status });
      
      const dataContent = JSON.parse(checkPair[0].data_content);
      const saveData = {
        id: dataContent.id,
        name: dataContent?.name,
        details: dataContent?.details || "",
        status: dataContent?.status || 'active',
        created_at: dataContent.created_at
      };

      if (data?.status === 'rejected') {
        this.saveOperationLog("OTHER", data?.clientId, data?.clientId, 'admin', "roles", "", JSON.stringify(saveData),  "1");
        return this.makeResponse(200, "Role creation request was rejected");
      }

      

      const result = await this.insertData("roles", saveData);
      await this.updateRoleAccessRights(dataContent.id, dataContent.access_rights);
      await this.updateRoleAppModules(dataContent.id, dataContent.app_menu_items);


      // take app operation logs 
      this.saveOperationLog("INSERT", data?.clientId, data?.clientId, 'admin', "roles", "", JSON.stringify(saveData),  "1");
      return this.makeResponse(200, "Role added successfully", result);
    
    } catch (error: any) {
      console.log("ROLE Approving issues", error)
      return this.makeResponse(500, "Error approving role");
    }
  }


  async updateRole(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "rolesEdit");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to update role", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status && data.status !== 'rejected') {
        return this.makeResponse(400, responseData?.message);
      }

      const VALIDATION = this.validateRolesData(data);
      if (!VALIDATION.success) {
        return this.makeResponse(400, "Invalid data", VALIDATION.error.errors);
      }

      const savedType: any = await this.callQuerySafe(`
        SELECT COUNT(*) as total
        FROM roles r 
        WHERE r.id != '${id}' AND r.name = '${data?.name}' AND r.deleted_at IS NULL
      `);

      if (savedType[0].total > 0) {
        return this.makeResponse(400, "Role name already exists");
      }

      const saveData = {
        name: data.name,
        details: data.details,
        status: data.status,
        updated_at: data.updated_at
      };
      await this.updateData("roles", `id = '${id}'`, saveData);
      await this.updateRoleAccessRights(id, data.access_right);
      await this.updateRoleAppModules(id, data.app_menu_items);
      return this.makeResponse(200, "Role updated successfully");

    } catch (error: any) {
      console.error("Error Role:", error);
      return this.makeResponse(500, "Error updating app Role");
    }
  }


  async deleteRole(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "rolesDelete");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to delete role", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }
      const saveData = {
        updated_at: data.updated_at,
        deleted_at: this.getMySQLDateTime()
      };

      await this.updateData("roles", `id = '${id}'`, saveData);
      await this.updateRoleAccessRights(id, []);
      await this.updateRoleAppModules(id, []);
      const defaultRole: any = await this.defaultAdminRole();
      await this.updateData("system_users", `role = '${id}'`, { role: defaultRole.id });
      return this.makeResponse(200, "Role deleted successfully");

    } catch (error: any) {
      console.error("Error Role:", error);
      return this.makeResponse(500, "Error updating app Role");
    }
  }









  /***
   *  update the roles section 
   * */
  async updateApprovalRole(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "rolesApprove");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to approve role", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status && data.status !== 'rejected') {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair = await this.selectDataQuerySafe("maker_checker", { id });
      if (checkPair.length === 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      // A maker cannot approve their own request if the request is approved
      if (data?.status === 'approved' &&  String(checkPair[0].maker_id) === String(data.clientId)) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }

      const dataContent: any = JSON.parse(checkPair[0].data_content);
      const theRole: any = await this.selectDataQuerySafe("roles", { id: dataContent.id });
      if (theRole.length === 0) {
        return this.makeResponse(400, "Role not found");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data.clientId, reason: data?.reason, status: data.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "Role updating request was rejected");
      }

      const saveData = {
        name: dataContent.name,
        details: dataContent.details,
        status: dataContent.status,
        updated_at: dataContent.updated_at
      };
      await this.updateData("roles", `id = '${dataContent.id}'`, saveData);
      await this.updateRoleAccessRights(dataContent.id, dataContent.access_rights);
      await this.updateRoleAppModules(dataContent.id, dataContent.app_menu_items);
      return this.makeResponse(200, "Role updated successfully");

    } catch (error: any) {
      console.error("Error adding role:", error);
      return this.makeResponse(500, "Error updating role");
    }
  }

  

  async getRoleAccessRights(roleId: string): Promise<any[]> {

    let accessRights: any[] = [];
    if (roleId === "0000-0000-0000-0003") {

      const acessRightsAll: any = await this.callQuerySafe(`
                                      SELECT r.*
                                      FROM access_rights r WHERE (r.type = 'admin' OR r.type IS NULL) AND r.deleted_at IS NULL
                                      ORDER BY r.created_at DESC
                                    `);
      const allowedRightsIds: any = ["d1d46ab5-41c1-11f0-bd21-16243d6fb08b", "d1d46d88-41c1-11f0-bd21-16243d6fb08b", "d1b17217-41c1-11f0-bd21-16243d6fb08b", "d17176b6-41c1-11f0-bd21-16243d6fb08b", "d149ac93-41c1-11f0-bd21-16243d6fb08b"]
      // add a new key to acessRightsAll that is access_rights_status
      accessRights = acessRightsAll.filter((role: any) => allowedRightsIds.includes(role.id));
      accessRights.map((role: any) => {
        role.access_rights_status = "active";
        role.access_rights_status_id = "";
        role.role_id = role.id;
      });
    } else {
      accessRights = (await this.callQuerySafe(` SELECT 
                                            rar.id as access_rights_status_id, 
                                            ar.id as role_id,  
                                            ar.name as name, 
                                            ar.status as access_rights_status
                                            FROM access_rights ar
                                            INNER JOIN role_access_rights rar ON ar.id = rar.access_right_id
                                            WHERE rar.role_id = '${roleId}'
                                            AND rar.status = 'active'  
                                            AND ar.deleted_at IS NULL 
                                            AND rar.deleted_at IS NULL
                                          `)) as any[];
    }

    return accessRights;
  }



  async getRoles(data: any) {
    try {
      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "rolesView");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to view roles", []);
      }

      const page = data?.page ? parseInt(data.page, 10) : null;
      const limit = data?.size ? parseInt(data.size, 10) : 8;
      const offset = page ? (page - 1) * limit : 0;

      let whereClause = `WHERE r.deleted_at IS NULL`;
      if (data?.search && typeof data.search === "string" && data.search.trim() !== "") {
        const searchStr = data.search.trim().replace(/'/g, "''");
        whereClause += ` AND r.name LIKE '%${searchStr}%'`;
      }
      if (data?.status && typeof data.status === "string" && data.status.trim() !== "") {
        whereClause += ` AND r.status = '${data.status.trim().replace(/'/g, "''")}'`;
      }

      // Get total count for pagination
      let totalItems: any = 0;
      if (page) {
        const totalResult: any = await this.callQuerySafe(`
          SELECT COUNT(*) AS total
          FROM roles r
          ${whereClause}
        `);
        totalItems = totalResult[0]?.total || 0;
      }

      // Main query with pagination
      const query = `
        SELECT r.*
        FROM roles r
        ${whereClause}
        ORDER BY r.created_at DESC
        ${page ? `LIMIT ${limit} OFFSET ${offset}` : ""}
      `;
      const savedType: any = await this.callQuerySafe(query);

      // Fetch access rights for each role
      if (savedType.length > 0) {
        for (const role of savedType) {
          const accessRights = await this.callQuerySafe(`
            SELECT rar.access_right_id AS role_access_rights_id, 
                   rar.role_id AS role_id, 
                   ar.name AS name, 
                   ar.status AS access_rights_status
            FROM access_rights ar
            INNER JOIN role_access_rights rar ON ar.id = rar.access_right_id
            WHERE rar.role_id = '${role.id}'
              AND rar.status = 'active'
              AND ar.deleted_at IS NULL 
              AND rar.deleted_at IS NULL
          `);
          role.access_rights = accessRights;
        }
      }

      // Add pagination metadata if paginated
      if (page) {
        const totalPages = Math.ceil(totalItems / limit);
        const hasNextPage = page < totalPages;
        const hasPrevPage = page > 1;
        return this.makeResponse(200, "Roles fetched successfully", {
          items: savedType,
          pagination: {
            current_page: page,
            total_pages: totalPages,
            total_items: totalItems,
            items_per_page: limit,
            hasNextPage,
            hasPrevPage,
            next_page: hasNextPage ? Number(page) + 1 : null,
            previous_page: hasPrevPage ? Number(page) - 1 : null
          }
        });
      }

      return this.makeResponse(200, "Roles fetched successfully", savedType);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching roles");
    }
  }




  async getRolesAccessRights(data: any) {
    try {
      const savedType: any = await this.callQuerySafe(`
        SELECT r.*
        FROM access_rights r WHERE (r.type = 'admin' OR r.type IS NULL) AND r.deleted_at IS NULL
        ORDER BY r.created_at DESC
      `);
      return this.makeResponse(200, "Role access rights fetched successfully", savedType);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching role access rights");
    }
  }


  async getRole(id: string) {
    try {
      const savedType: any = await this.callQuerySafe(`
        SELECT r.*
        FROM roles r
        WHERE r.id = '${id}' AND 
        r.deleted_at IS NULL
        ORDER BY r.created_at DESC
      `);

      if (savedType.length === 0) {
        return this.makeResponse(400, "Role not found");
      }

      const accessRights = await this.callQuerySafe(`
         SELECT rar.access_right_id as role_access_rights_id, 
                   rar.role_id as role_id, 
                   ar.name as name, 
                   ar.status as access_rights_status
            FROM access_rights ar
            INNER JOIN role_access_rights rar ON ar.id = rar.access_right_id
            WHERE rar.role_id = '${savedType[0].id}'
            AND rar.status = 'active'  
            AND ar.deleted_at IS NULL 
            AND rar.deleted_at IS NULL
      `);
      savedType[0].access_rights = accessRights;
      savedType[0].menu_list     = await this.getTheMenuList({role_id: id})

      return this.makeResponse(200, "Role fetched successfully", savedType[0]);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching role");
    }
  }




  // get roles to be approved
  async getRolesToBeApproved(data: any) {
    try {
      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "rolesView");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to view roles requests", []);
      }

      let currentStatus: any = "pending";
      if (data?.status) {
        currentStatus = data?.status;
      }

      // Pagination defaults
      const page = data?.page && Number(data.page) > 0 ? Number(data.page) : 1;
      const limit = data?.size && Number(data.size) > 0 ? Number(data.size) : 8;
      const offset = (page - 1) * limit;

      // Base condition
      let whereClause = `( entry_type = 'add_role' OR entry_type = 'edit_role' ) AND mc.status = '${currentStatus}'`;

      // Search by text in data_content
      if (data?.search && typeof data.search === "string" && data.search.trim() !== "") {
        const searchStr = data.search.trim().replace(/'/g, "''");
        whereClause += ` AND mc.data_content LIKE '%${searchStr}%'`;
      }

      // Filter by maker_id
      if (data?.maker_id) {
        whereClause += ` AND maker_id = '${data.maker_id}'`;
      }

      // Count total items for pagination
      const totalResult = await this.callQuerySafe(`
        SELECT COUNT(*) AS total
        FROM maker_checker mc
        WHERE ${whereClause}
      `) as any[];
      const totalItems = (totalResult as any[])[0]?.total || 0;

      if (totalItems === 0) {
        return this.makeResponse(400, "No roles requests");
      }

      // Fetch paginated data
      const contentData: any[] = await this.callQuerySafe(`
        SELECT 
            mc.*, 
            CASE 
                WHEN mc.maker_id IS NOT NULL THEN CONCAT(maker.first_name, ' ', maker.last_name)
                ELSE NULL
            END AS maker_name,
            CASE 
                WHEN mc.checker_id IS NOT NULL THEN CONCAT(checker.first_name, ' ', checker.last_name)
                ELSE NULL
            END AS checker_name
        FROM maker_checker mc
        LEFT JOIN system_users maker 
            ON mc.maker_id = maker.id AND mc.maker_id IS NOT NULL
        LEFT JOIN system_users checker 
            ON mc.checker_id = checker.id AND mc.checker_id IS NOT NULL
        WHERE ${whereClause}
        ORDER BY mc.created_at DESC
        LIMIT ${limit} OFFSET ${offset};        
      `) as any[];

      // get al access rights
      const accessRights: any = await this.callQuerySafe(`
        SELECT * FROM access_rights WHERE deleted_at IS NULL
      `);


      const contentDataData: any[] = await Promise.all(
        contentData.map(async (pairPrice: any) => {

          const dataContent: any = JSON.parse(pairPrice.data_content);
          // filter out access rights and show those whose ids are in the dataContent.access_rights
          const filteredAccessRights: any = accessRights.filter((accessRight_: any) => dataContent.access_rights.includes(accessRight_.id));
          dataContent.access_rights_details = filteredAccessRights;
          return {
            ...pairPrice,
            data_content: dataContent
          };
        })
      );


      // Pagination metadata
      const totalPages = Math.ceil(totalItems / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;
      const paginationInfo = {
        current_page: page,
        total_pages: totalPages,
        total_items: totalItems,
        items_per_page: limit,
        hasNextPage,
        hasPrevPage,
        next_page: hasNextPage ? Number(page) + 1 : null,
        previous_page: hasPrevPage ? Number(page) - 1 : null
      };

      return this.makeResponse(200, "Roles listed successfully", {
        items: contentDataData,
        pagination: paginationInfo
      });

    } catch (error: any) {
      return this.makeResponse(500, "Error fetching app roles");
    }
  }



  /**
   *  update the products section 
   * */
  async updateProduct(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "feesEdit");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to update product", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      //check if product exists
      const product = await this.selectDataQuerySafe("products", { product_id: id });
      if (product.length === 0) {
        return this.makeResponse(400, "Product not found");
      }

      const feeType = ["PERCENTAGE", "FLAT", "TIER"];
      if (!feeType.includes(data.fee_type)) {
        return this.makeResponse(400, "Invalid fee type");
      }

      // if tier make sure tier_fee_range is given and has atleast 1 object
      if (data.fee_type === "TIER") {
        if (!data.tier_fee_range || data.tier_fee_range.length === 0) {
          return this.makeResponse(400, "Tier fee range is required");
        }

        for (const tier_fee_range of data.tier_fee_range) {
          if (!tier_fee_range.min_amount || !tier_fee_range.max_amount || !tier_fee_range.fee_amount) {
            return this.makeResponse(400, "Tier fee range is required");
          }
        }
      } else {

        // for percentage make sure fee_amount is less than 100
        if (data.fee_type === "PERCENTAGE" && data.fee_amount > 100) {
          return this.makeResponse(400, "Percentage fee amount cannot be greater than 100");
        }

        // for flat make sure fee_amount is greater than 0
        if (data.fee_type === "FLAT" && data.fee_amount <= 0) {
          return this.makeResponse(400, "Flat fee amount cannot be less than 0");
        }
      }

      const saveData: any = {};
      saveData.fee_type = data.fee_type;

      if (data.provider_fee) {
        if (isNaN(data.provider_fee)) {
          return this.makeResponse(400, "Provider fee must be a number");
        }
        saveData.provider_fee = data.provider_fee;
      }

      if (data.fee_type !== "TIER") {
        saveData.fee_amount = data.fee_amount;
      }
      await this.updateData("products", `product_id  = '${id}'`, saveData);

      // saving tier fees
      if (data.fee_type === "TIER") {
        await this.saveTierFees(data, id, "product");
      }
      return this.makeResponse(200, "Product updated  successfully");

    } catch (error: any) {
      return this.makeResponse(500, "Error editing product");
    }
  }


  async updateApproveProduct(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "feesApprove");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to approve product", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status && data.status !== 'rejected') {
        return this.makeResponse(400, responseData?.message);
      }

      const product = await this.selectDataQuerySafe("products", { product_id: id });
      if (product.length === 0) {
        return this.makeResponse(400, "Product not found");
      }

      const checkPair = await this.selectDataQuerySafe("maker_checker", { id });
      if (checkPair.length > 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId)) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data?.clientId, reason: data?.reason, status: data?.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "Product updating request was rejected");
      }
      const dataContent = JSON.parse(checkPair[0].data_content);
      const saveData = {
        fee_type: dataContent.fee_type,
        fee_amount: dataContent.fee_amount
      };
      await this.updateData("products", `id = '${dataContent.id}'`, saveData);
      return this.makeResponse(200, "Product updated  successfully");
    } catch (error: any) {
      return this.makeResponse(500, "Error updating product");
    }
  }

  async getProductsToBeApproved(data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "feesView");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to view products requests", []);
      }

      let currenctStatus: any = "pending";
      if (data?.status) {
        currenctStatus = data?.status;
      }
      const responseData = await this.callQuerySafe(
        "SELECT * FROM maker_checker WHERE (entry_type = 'add_product' OR entry_type = 'edit_product') AND status = ?",
        [currenctStatus]
      ) as any[];
      if (responseData.length === 0) {
        return this.makeResponse(400, "No products requests");
      }
      const pairPricesData: any[] = await Promise.all(
        responseData.map(async (data: any) => {
          const dataContent: any = JSON.parse(data.data_content);
          return {
            ...data,
            data_content: dataContent
          };
        })
      );
      return this.makeResponse(200, "Products listed successfully", pairPricesData);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching app products");
    }
  }


  async getProducts(data: any) {
    try {
      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "feesView");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to view products", []);
      }

      const { page, size = 8, currency = '', transaction_type = '', status = '', search = '' } = data || {};
      const limit = parseInt(size) > 0 ? parseInt(size) : 8;
      const offset = page ? (Number(page) - 1) * limit : 0;

      // Build where clause dynamically
      let whereClause = "1=1"; // always true

      if (currency && currency.trim() !== "") {
        whereClause += ` AND currency = '${currency}'`;
      }

      if (transaction_type && transaction_type.trim() !== "") {
        whereClause += ` AND transaction_type = '${transaction_type}'`;
      }

      if (status && status.trim() !== "") {
        whereClause += ` AND status = '${status}'`;
      }

      if (search && typeof search === "string" && search.trim() !== "") {
        const searchStr = search.trim().replace(/'/g, "''"); // escape single quotes
        whereClause += ` AND (
          name LIKE '%${searchStr}%' 
          OR description LIKE '%${searchStr}%'
          OR sku LIKE '%${searchStr}%'
        )`;
      }

      // ✅ If no page → fetch all
      if (!page) {
        let products: any = await this.callQuerySafe(`Select * from products where ${whereClause} ORDER BY created_at DESC`) as any[];
        if (!products || products.length === 0) {
          return this.makeResponse(400, "No products found", []);
        }

        // check if tier and map on a tier vollection object 
        products = products.map(async (product: any) => {
          if (product.fee_type === "TIER") {
            product.tier_fees = await this.selectDataQuerySafe("tier_fees", { product_id: product.product_id });

          }
          return product;
        });
        products = await Promise.all(products);
        return this.makeResponse(200, "Products fetched successfully", products);
      }

      // ✅ With pagination
      // Count total
      const countQuery = `SELECT COUNT(*) AS total FROM products WHERE ${whereClause}`;
      const countResult: any = await this.callQuerySafe(countQuery);
      const totalItems = countResult[0]?.total || 0;

      if (totalItems === 0) {
        return this.makeResponse(400, "No products found", {
          items: [],
          pagination: {
            current_page: Number(page),
            total_pages: 0,
            total_items: 0,
            items_per_page: limit,
            hasNextPage: false,
            hasPrevPage: false,
            next_page: null,
            previous_page: null
          }
        });
      }

      const totalPages = Math.ceil(totalItems / limit);

      // Fetch paginated data
      let products: any = await this.callQuerySafe(`Select * from products where ${whereClause} ORDER BY created_at DESC LIMIT ${limit} OFFSET ${offset}`) as any[];
      // check if tier and map on a tier vollection object 
      if (products.length > 0) {
        products = products.map(async (product: any) => {
          if (product.fee_type == "TIER") {
            product.tier_fees = await this.callQuerySafe(`Select * from tier_fees where product_id = ${product.product_id} ORDER BY created_at DESC`) as any[];
          }
          return product;
        });
        products = await Promise.all(products);
      }

      return this.makeResponse(200, "Products fetched successfully", {
        items: products,
        pagination: {
          current_page: Number(page),
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: limit,
          hasNextPage: Number(page) < totalPages,
          hasPrevPage: Number(page) > 1,
          next_page: Number(page) < totalPages ? Number(page) + 1 : null,
          previous_page: Number(page) > 1 ? Number(page) - 1 : null
        }
      });

    } catch (error: any) {
      console.error("Error fetching products:", error);
      return this.makeResponse(500, "Error fetching products");
    }
  }


  async getProduct(id: string, data: any) {
    try {
      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "feesView");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to view products", []);
      }

      const product: any = await this.selectDataQuerySafe("products", { product_id: '${id}' });
      if (product.length === 0) {
        return this.makeResponse(400, "Product not found");
      }

      if (product[0].fee_type == "TIER") {

        product[0].tier_fees = await this.callQuerySafe(`Select * from tier_fees where product_id = '${product[0].product_id}' ORDER BY created_at DESC`) as any[];

      }
      return this.makeResponse(200, "Product fetched successfully", product[0]);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching product");
    }
  }

  /// rates section 
  async addAppPairPrices(data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "ratesCreate");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to add exchange pair price", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      //ckeck og pair exists
      const checkPair: any = await this.callQuerySafe(
        "SELECT * FROM exchange_rates WHERE base_currency = ? AND quote_currency = ?",
        [data.base_currency, data.quote_currency]
      );
      if (checkPair.length > 0) {
        return this.makeResponse(206, "Pair already exists");
      }

      const saveData = {
        id: uuidv4(),
        base_currency: data.base_currency,
        quote_currency: data.quote_currency,
        created_at: this.getMySQLDateTime(),
        pair: `${data.base_currency}/${data.quote_currency}`,
        hasCrypto: false,
        referencePrice: "",
        markup: data.markup,
        markdown: data.markdown
      };

      const saveData_ = {
        id: uuidv4(),
        data_content: JSON.stringify(saveData),
        entry_type: "add_exchange_rate",
        status: "pending",
        maker_id: data.clientId,
        created_at: this.getMySQLDateTime(),
        updated_at: this.getMySQLDateTime()
      }
      const result = await this.insertData("maker_checker", saveData_);
      return this.makeResponse(200, "Exchange pair price added for approval successfully", result);

    } catch (error: any) {
      console.error("Error adding app pair price >>>>>>>>>>:", error);
      return this.makeResponse(500, "Error adding app pair price");
    }
  }


  async addAppApprovePairPrices(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "ratesApprove");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to approve exchange pair price", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status && data.status !== 'rejected') {
        return this.makeResponse(206, responseData?.message);
      }

      const checkPair = await this.selectDataQuerySafe("maker_checker", { id });
      if (checkPair.length === 0) {
        return this.makeResponse(206, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(206, "Request entry was already approved/rejected");
      }

      if (checkPair[0].maker_id === data.clientId) {
        return this.makeResponse(206, "A maker cannot approve their own request");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data?.clientId, reason: data?.reason, status: data?.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "Pair price creation request was rejected");
      }
      const dataContent = JSON.parse(checkPair[0].data_content);
      const saveData = {
        id: dataContent.id,
        base_currency: dataContent.base_currency,
        quote_currency: dataContent.quote_currency,
        created_at: dataContent.created_at,
        pair: dataContent.pair,
        hasCrypto: dataContent.hasCrypto,
        referencePrice: dataContent.referencePrice,
        markup: dataContent.markup,
        markdown: dataContent.markdown
      };

      const result = await this.insertData("exchange_rates", saveData);
      return this.makeResponse(200, "Exchange pair price added successfully", result);
    } catch (error: any) {
      return this.makeResponse(500, "Error adding app pair price");
    }
  }

  async updateAppPairPrices(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "ratesEdit");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to update exchange pair price", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(206, responseData?.message);
      }

      const prices = await this.selectDataQuerySafe("exchange_rates", { id });
      if (prices.length === 0) {
        return this.makeResponse(206, "Exchange pair price not found");
      }

      const saveData = {
        hasCrypto: data.hasCrypto,
        referencePrice: data.referencePrice,
        markup: data.markup,
        markdown: data.markdown,
        updated_at: this.getMySQLDateTime()
      };
      await this.updateData("exchange_rates", `id = '${id}'`, saveData);
      return this.makeResponse(200, "Exchange pair price updated successfully");

    } catch (error: any) {
      console.error("Error adding app pair price:", error);
      return this.makeResponse(500, "Error updating app pair price");
    }
  }

  async updateApprovalAppPairPrices(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "ratesApprove");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to approve exchange pair price", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status && data.status !== 'rejected') {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair = await this.selectDataQuerySafe("maker_checker", { id });
      if (checkPair.length === 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId)) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }


      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data.clientId, reason: data?.reason, status: data.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "Pair price updating request was rejected");
      }
      const dataContent: any = JSON.parse(checkPair[0].data_content);

      const saveData = {
        hasCrypto: dataContent.hasCrypto,
        referencePrice: dataContent.referencePrice,
        markup: dataContent.markup,
        markdown: dataContent.markdown,
        updated_at: this.getMySQLDateTime()
      };
      await this.updateData("exchange_rates", `id = '${dataContent.id}'`, saveData);
      return this.makeResponse(200, "Exchange pair price updated successfully");
    } catch (error: any) {
      console.error("Error adding app pair price:", error);
      return this.makeResponse(500, "Error updating app pair price");
    }
  }


  async getPairPricesToBeApproved(data: any) {
    try {
      // Check access rights
      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "ratesApprove");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to approve exchange pair price requests", []);
      }

      // Default status
      let currentStatus: string = "pending";
      if (data?.status) {
        currentStatus = data?.status;
      }

      // Base query
      let whereClause = `( entry_type = 'add_exchange_rate' OR entry_type = 'edit_exchange_rate' ) AND status = '${currentStatus}'`;

      // Search filter (search base_currency or quote_currency inside JSON text)
      if (data?.search && typeof data.search === "string" && data.search.trim() !== "") {
        const searchStr = data.search.trim().replace(/'/g, "''"); // escape single quotes
        whereClause += ` AND (data_content LIKE '%"base_currency":"${searchStr}%' OR data_content LIKE '%"quote_currency":"${searchStr}%')`;
      }

      // ✅ If no pagination requested → fetch all as before
      if (!data?.page) {
        const pairPrices: any[] = await this.callQuerySafe(`SELECT * FROM maker_checker ${whereClause}`) as any[];
        if (pairPrices.length === 0) {
          return this.makeResponse(400, "No exchange pair price requests");
        }

        const pairPricesData: any[] = await Promise.all(
          pairPrices.map(async (pairPrice: any) => {
            const dataContent: any = JSON.parse(pairPrice.data_content);
            return {
              ...pairPrice,
              data_content: dataContent
            };
          })
        );

        return this.makeResponse(200, "Pair prices listed successfully", pairPricesData);
      }

      // ✅ Else → apply pagination
      const page = Number(data?.page) > 0 ? Number(data.page) : 1;
      const limit = Number(data?.size) > 0 ? Number(data.size) : 8;
      const offset = (page - 1) * limit;

      // Get total items count
      const totalItemsResult: any[] = await this.callQuerySafe(`SELECT * FROM maker_checker ${whereClause}`) as any[];
      const totalItems = totalItemsResult.length;

      if (totalItems === 0) {
        return this.makeResponse(400, "No exchange pair price requests");
      }

      const totalPages = Math.ceil(totalItems / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      // Get paginated data
      const pairPrices: any[] = await this.callQuerySafe(
        `SELECT * FROM maker_checker WHERE ${whereClause} ORDER BY created_at DESC LIMIT ${limit} OFFSET ${offset}`
      ) as any[];

      const pairPricesData: any[] = await Promise.all(
        pairPrices.map(async (pairPrice: any) => {
          const dataContent: any = JSON.parse(pairPrice.data_content);
          return {
            ...pairPrice,
            data_content: dataContent
          };
        })
      );

      // Final response
      return this.makeResponse(200, "forex exchange response data", {
        items: pairPricesData,
        pagination: {
          current_page: page,
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: limit,
          hasNextPage,
          hasPrevPage,
          next_page: hasNextPage ? page + 1 : null,
          previous_page: hasPrevPage ? page - 1 : null
        }
      });

    } catch (error: any) {
      return this.makeResponse(500, "Error fetching app pair price");
    }
  }


  async getAppPairPrices(data: any) {
    try {
      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "ratesView");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to view exchange pair price requests", []);
      }

      // Base query
      let whereClause = `WHERE enabled = 1`;

      // Filter by default_currency
      if (data?.default_currency && typeof data.default_currency === "string" && data.default_currency.trim() !== "") {
        whereClause += ` AND base_currency = '${data.default_currency.trim().replace(/'/g, "''")}'`;
      }

      // Search filter
      if (data?.search && typeof data.search === "string" && data.search.trim() !== "") {
        const searchStr = data.search.trim().replace(/'/g, "''");
        whereClause += ` AND (base_currency LIKE '%${searchStr}%' OR quote_currency LIKE '%${searchStr}%')`;
      }

      let prices: any = [];
      let pagination: any = null;

      if (data?.page) {
        // Pagination logic
        const page = Number(data.page) > 0 ? Number(data.page) : 1;
        const pageSize = Number(data.size) > 0 ? Number(data.size) : 8;
        const offset = (page - 1) * pageSize;

        // Get total count
        const totalItemsResult: any = await this.callQuerySafe(`
          SELECT COUNT(*) AS total FROM exchange_rates ${whereClause}
        `);
        const totalItems = totalItemsResult[0]?.total || 0;
        const totalPages = Math.ceil(totalItems / pageSize);

        // Fetch paginated items
        prices = await this.callQuerySafe(`
          SELECT id, enabled, hasCrypto, referencePrice, base_currency, 
                 quote_currency, pair, markup, markdown, created_at, updated_at 
          FROM exchange_rates
          ${whereClause}
          ORDER BY created_at DESC
          LIMIT ${pageSize} OFFSET ${offset}
        `);


        pagination = {
          current_page: page,
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: pageSize,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          next_page: page < totalPages ? page + 1 : null,
          previous_page: page > 1 ? page - 1 : null
        };




      } else {
        // No pagination — return all items
        prices = await this.callQuerySafe(`
          SELECT id, enabled, hasCrypto, referencePrice, base_currency, 
                 quote_currency, pair, markup, markdown, created_at, updated_at 
          FROM exchange_rates
          ${whereClause}
          ORDER BY created_at DESC
        `);
      }

      // Fetch current rates for items
      const pricesData = await Promise.all(
        prices.map(async (currencyPair: any) => {
          let rateDetails: any = await ratesService.getRates({
            quote: currencyPair.base_currency,
            base: currencyPair.quote_currency
          });

          let fBestExchangeRate = rateDetails?.status ? 1 / Number(rateDetails?.price) : "";

          return {
            ...currencyPair,
            current_exchange_rate: fBestExchangeRate
          };
        })
      );

      if (data.page === undefined) {
        return this.makeResponse(200, "Exchange pair prices fetched successfully", pricesData);
      }




      return this.makeResponse(200, "Exchange pair prices fetched successfully", {
        items: pricesData,
        pagination: pagination
      });

    } catch (error: any) {
      console.error("Error fetching app pair price:", error);
      return this.makeResponse(500, "Error fetching app pair prices");
    }
  }




  async getAppPairPriceById(id: string) {
    try {
      const prices = await this.selectDataQuerySafe("exchange_rates", { id });
      if (prices.length === 0) {
        return this.makeResponse(400, "Exchange pair price not found");
      }
      delete prices[0].updatedBy
      let rateDetails: any = "";
      let fBestExchangeRate: any = "";
      rateDetails = await ratesService.getRates({ quote: prices[0].base_currency, base: prices[0].quote_currency });
      fBestExchangeRate = (rateDetails?.status) ? 1 / Number(rateDetails?.price) : "";
      prices[0].current_exchange_rate = fBestExchangeRate


      return this.makeResponse(200, "Exchange pair price fetched successfully", prices[0]);
    } catch (error: any) {
      console.error("Error fetching app pair price:", error);
      return this.makeResponse(500, "Error fetching app pair price");
    }
  }


  async getBaseCurrencyRate(base_currency: string) {
    try {
      const prices: any = (await this.callQuerySafe(`
      SELECT id, enabled, hasCrypto, referencePrice, base_currency, 
             quote_currency, pair, markup, markdown, created_at, updated_at 
      FROM exchange_rates 
      WHERE enabled = 1 AND base_currency = '${base_currency}'
    `));
      const pricesData = await Promise.all(
        prices.map(async (currencyPair: any) => {
          let fBestExchangeRate;
          let rateDetails: any = {};

          rateDetails = await ratesService.getRates({ quote: currencyPair.base_currency, base: currencyPair?.quote_currency });
          fBestExchangeRate = (rateDetails?.status) ? 1 / Number(rateDetails?.price) : "";

          return {
            ...currencyPair,
            current_exchange_rate: fBestExchangeRate
          };
        })
      );
      return this.makeResponse(200, "Exchange pair prices fetched successfully", pricesData);
    } catch (error: any) {
      console.error("Error fetching app pair price:", error);
      return this.makeResponse(500, "Error fetching app pair prices");
    }
  }


  async getPairsExchangeAmount(data: any) {
    try {
      const prices: any = (await this.callQuerySafe(`
      SELECT id, enabled, hasCrypto, referencePrice, base_currency,  quote_currency, pair, markup, markdown, created_at, updated_at 
      FROM exchange_rates WHERE enabled = 1 AND base_currency = '${data.base_currency}' AND quote_currency = '${data.quote_currency}'
    `));

      if (prices.length === 0) {
        return this.makeResponse(400, "Exchange pair not found");
      }


      if (prices[0].updated_at) {
        const updatedAt = new Date(prices[0].updated_at);
        const now = new Date();
        const diffInMinutes = (now.getTime() - updatedAt.getTime()) / (1000 * 60);

        if (diffInMinutes < 1) {
          const cachedRate = prices[0].referencePrice || "";
          const fBestExchangeRate = cachedRate ? Number(cachedRate) : "";
          const fBestExchangeRateAmount = fBestExchangeRate ? (fBestExchangeRate * data.amount) : "";

          const result = {
            rate: fBestExchangeRate,
            exchangedAmount: fBestExchangeRateAmount,
            source: "cached"
          };
          return this.makeResponse(200, "Exchange pair amount calculated successfully (cached)", result);
        }
      }

      const rateDetails: any = await ratesService.getRates({ quote: prices[0].base_currency, base: prices[0]?.quote_currency });
      console.log("rateDetails", rateDetails);
      const fBestExchangeRate: any = (rateDetails?.status) ? Number(rateDetails?.price) : "";
      const fBestExchangeRateAmount: any = (fBestExchangeRate * data.amount);

      const result = {
        rate: fBestExchangeRate,
        exchangedAmount: fBestExchangeRateAmount
      };

      return this.makeResponse(200, "Exchange pair amount calculated successfully", result);
    } catch (error: any) {
      console.error("Error fetching app pair price:", error);
      return this.makeResponse(500, "Error fetching app pair prices");
    }
  }


  // banks
  async getASystemBanks(data: any, id: any) {
    try {

      const banks = await this.selectDataQuerySafe("banks", { id: id });
      if (banks.length === 0) {
        return this.makeResponse(400, "Bank not found");
      }
      return this.makeResponse(200, "Bank fetched successfully", banks[0]);
    } catch (error: any) {
      console.error("Error fetching Bank:", error);
      return this.makeResponse(500, "Error fetching Bank");
    }
  }


  async getSystemBanks(data: any) {
    try {
      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "settingsView");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to view payment methods", []);
      }

      const { page, size = 8, search = '', country = '', currency = '' } = data || {};
      const limit = parseInt(size) > 0 ? parseInt(size) : 8;
      const offset = page ? (Number(page) - 1) * limit : 0;

      // Build search condition
      let whereClause = "WHERE 1=1"; // always true, makes appending easy

      if (search && search.trim() !== "") {
        const searchStr = search.trim().replace(/'/g, "''");
        whereClause += ` AND details LIKE '%${searchStr}%'`;
      }

      if (country && country.trim() !== "") {
        whereClause += ` AND country = '${country}'`;
      }

      if (currency && currency.trim() !== "") {
        whereClause += ` AND currency = '${currency}'`;
      }

      // ✅ If no pagination → fetch all banks
      if (!page) {
        const query = `SELECT * FROM banks ${whereClause} ORDER BY created_at DESC`;
        const banks: any = await this.callQuerySafe(query);

        if (!banks || banks.length === 0) {
          return this.makeResponse(400, "No banks found", []);
        }

        return this.makeResponse(200, "Banks fetched successfully", banks);
      }

      // ✅ With pagination
      // Get total count
      const countQuery = `SELECT COUNT(*) AS total FROM banks ${whereClause}`;
      const countResult: any = await this.callQuerySafe(countQuery);
      const totalItems = countResult[0]?.total || 0;

      if (totalItems === 0) {
        return this.makeResponse(400, "No banks found", {
          items: [],
          pagination: {
            current_page: Number(page),
            total_pages: 0,
            total_items: 0,
            items_per_page: limit,
            hasNextPage: false,
            hasPrevPage: false,
            next_page: null,
            previous_page: null
          }
        });
      }

      const totalPages = Math.ceil(totalItems / limit);

      // Fetch paginated banks
      const query = `
        SELECT * 
        FROM banks 
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `;
      const banks = await this.callQuerySafe(query);

      return this.makeResponse(200, "Banks fetched successfully", {
        items: banks,
        pagination: {
          current_page: Number(page),
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: limit,
          hasNextPage: Number(page) < totalPages,
          hasPrevPage: Number(page) > 1,
          next_page: Number(page) < totalPages ? Number(page) + 1 : null,
          previous_page: Number(page) > 1 ? Number(page) - 1 : null
        }
      });

    } catch (error: any) {
      console.error("Error fetching Banks:", error);
      return this.makeResponse(500, "Error fetching app Banks");
    }
  }


  async addSystemBanks(data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "settingsEdit");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to add payment methods", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      //check og pair exists
      const checkPair = await this.selectDataQuerySafe("banks", { account_number: data.account_number });
      if (checkPair.length > 0) {
        return this.makeResponse(206, "Account number already registered");
      }

      const checkPair_ = await this.selectDataQuerySafe("maker_checker", { status: 'pending', data_content: { $like: `%${data.account_number}%` } });
      if (checkPair_.length > 0) {
        return this.makeResponse(206, "Account number already placed for verification");
      }

      const saveData = {
        id: uuidv4(),
        bank_name: data.bank_name,
        branch_name: data.branch_name ?? "",
        branch_address: data.branch_address ?? "",
        account_name: data.account_name,
        account_number: data.account_number,
        swift_code: data.swift_code,
        country: data.country,
        currency: data.currency,
        beneficiary_address: data.beneficiary_address ?? "",
        created_at: this.getMySQLDateTime()
      };

      const saveData_ = {
        id: uuidv4(),
        data_content: JSON.stringify(saveData),
        entry_type: "add_payment_methods",
        status: "pending",
        maker_id: data.clientId,
        created_at: this.getMySQLDateTime(),
        updated_at: this.getMySQLDateTime()
      }
      const result = await this.insertData("maker_checker", saveData_);
      return this.makeResponse(200, "Exchange payment method added for approval successfully", result);

    } catch (error: any) {
      console.error("Error adding payment method  >>>>>>>>>>:", error);
      return this.makeResponse(500, "Error adding payment method ");
    }
  }

  async editApprovalPaymentMthods(id: string, data: any) {

    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "settingsApprove");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to approve payment methods", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        // return this.makeResponse(206, responseData?.message);
      }

      const checkPair = await this.selectDataQuerySafe("maker_checker", { id: id });
      if (checkPair.length === 0) {
        return this.makeResponse(206, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(206, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId)) {
        return this.makeResponse(206, "A maker cannot approve their own request");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data?.clientId, reason: data?.reason, status: data?.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "Payment method request was rejected");
      }
      const dataContent = JSON.parse(checkPair[0].data_content);

      const checkPair_ = await this.selectDataQuerySafe("banks", { account_number: dataContent.account_number });
      if (checkPair_.length > 0) {
        return this.makeResponse(206, "Bank account number already registered");
      }


      const saveData = {
        id: dataContent.id,
        bank_name: dataContent.bank_name,
        branch_name: dataContent.branch_name ?? "",
        branch_address: dataContent.branch_address ?? "",
        account_name: dataContent.account_name,
        account_number: dataContent.account_number,
        swift_code: dataContent.swift_code,
        country: dataContent.country,
        currency: dataContent.currency,
        beneficiary_address: dataContent.beneficiary_address ?? "",
        created_at: dataContent.created_at
      };

      const result = await this.insertData("banks", saveData);
      return this.makeResponse(200, "Payment method added successfully", result);
    } catch (error: any) {
      return this.makeResponse(500, "Error adding bank account");
    }
  }



  async getAllPendingPaymentMethods(data: any) {
    try {
      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "settingsView");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to view pending payment methods", []);
      }

      // Default status
      let currentStatus: string = "pending";
      if (data?.status) {
        currentStatus = data?.status;
      }

      const { page, size = 8, search = '', country = '', currency = '' } = data || {};
      const limit = parseInt(size) > 0 ? parseInt(size) : 8;
      const offset = page ? (Number(page) - 1) * limit : 0;

      // Build base where clause
      let whereClause = `( entry_type = 'add_payment_methods' OR entry_type = 'edit_payment_methods' ) AND status = '${currentStatus}'`;

      // Search filter (inside JSON text: details, country, currency)
      if (search && search.trim() !== "") {
        const searchStr = search.trim().replace(/'/g, "''");
        whereClause += ` AND data_content LIKE '%"details":"${searchStr}%'`;
      }
      if (country && country.trim() !== "") {
        whereClause += ` AND data_content LIKE '%"country":"${country}%'`;
      }
      if (currency && currency.trim() !== "") {
        whereClause += ` AND data_content LIKE '%"currency":"${currency}%'`;
      }

      // ✅ If no pagination → fetch all
      if (!page) {
        const paymentMtds: any[] = await this.callQuerySafe(`Select * from maker_checker where ${whereClause} ORDER BY created_at DESC`) as any[];
        if (paymentMtds.length === 0) {
          return this.makeResponse(400, "No payment method requests");
        }

        const paymentMtdsData: any[] = await Promise.all(
          paymentMtds.map(async (paymentMtd: any) => {
            const dataContent: any = JSON.parse(paymentMtd.data_content);
            return { ...paymentMtd, data_content: dataContent };
          })
        );

        return this.makeResponse(200, "Payment method requests listed successfully", paymentMtdsData);
      }

      // ✅ With pagination
      const allItems: any[] = await this.callQuerySafe(`SELECT * FROM maker_checker ${whereClause}`) as any[];
      const totalItems = allItems.length;

      if (totalItems === 0) {
        return this.makeResponse(400, "No payment method requests", {
          items: [],
          pagination: {
            current_page: Number(page),
            total_pages: 0,
            total_items: 0,
            items_per_page: limit,
            hasNextPage: false,
            hasPrevPage: false,
            next_page: null,
            previous_page: null
          }
        });
      }

      const totalPages = Math.ceil(totalItems / limit);

      const paymentMtds: any[] = await this.callQuerySafe(`Select * from maker_checker where ${whereClause} ORDER BY created_at DESC LIMIT ${limit} OFFSET ${offset}`) as any[];

      const paymentMtdsData: any[] = await Promise.all(
        paymentMtds.map(async (paymentMtd: any) => {
          const dataContent: any = JSON.parse(paymentMtd.data_content);
          return { ...paymentMtd, data_content: dataContent };
        })
      );

      return this.makeResponse(200, "Payment method requests listed successfully", {
        items: paymentMtdsData,
        pagination: {
          current_page: Number(page),
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: limit,
          hasNextPage: Number(page) < totalPages,
          hasPrevPage: Number(page) > 1,
          next_page: Number(page) < totalPages ? Number(page) + 1 : null,
          previous_page: Number(page) > 1 ? Number(page) - 1 : null
        }
      });

    } catch (error: any) {
      console.error("Error fetching payment methods:", error);
      return this.makeResponse(500, "Error fetching app payment method");
    }
  }



  async updateSystemBanks(id: string, data: any) {
    try {

      const accessRight = await this.getLoggedUserAccessRights(data?.clientId, "settingsEdit");
      if (!accessRight) {
        return this.makeResponse(405, "You are not authorized to update payment methods", []);
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(206, responseData?.message);
      }

      const existBank = await this.selectDataQuerySafe("banks", { id: id });
      if (existBank.length === 0) {
        return this.makeResponse(400, "Bank account not found");
      }

      const checkPair = await this.selectDataQuerySafe("banks", { id: { $ne: id }, account_number: data.account_number });
      if (checkPair.length > 0) {
        return this.makeResponse(206, "Bank account number already registered");
      }
      const saveData = {
        bank_name: data.bank_name,
        branch_name: data.branch_name ?? "",
        branch_address: data.branch_address ?? "",
        account_name: data.account_name,
        account_number: data.account_number,
        swift_code: data.swift_code,
        country: data.country,
        currency: data.currency,
        beneficiary_address: data.beneficiary_address ?? ""
      };
      const updateBank = await this.updateData("banks", `id = '${id}'`, saveData);
      return this.makeResponse(200, `Bank account updated successfully`);

    } catch (error: any) {
      console.error("Error adding bank account:", error);
      return this.makeResponse(500, "Error updating bank account");
    }
  }


  async getcredentialsReset(data: any) {
    try {

      // Pagination defaults
      const page = data?.page && Number(data.page) > 0 ? Number(data.page) : 1;
      const limit = data?.size && Number(data.size) > 0 ? Number(data.size) : 8;
      const offset = (page - 1) * limit;

      // Build where clause
      let whereClause = `WHERE rcr.request_approver = 'SYSTEM_ADMIN' AND rcr.used != 1 `;

      // if  data?.reset_type is not null then add it to the where clause
      if (data?.reset_type && data?.reset_type !== null) {
        whereClause += ` AND rcr.reset_type = '${data?.reset_type}'`;
      }

      // Add search filter if provided
      if (data?.search && typeof data.search === "string" && data.search.trim() !== "") {
        const searchStr = data.search.trim().replace(/'/g, "''");
        whereClause += ` AND (cl.first_name  LIKE '%${searchStr}%' OR cl.last_name  LIKE '%${searchStr}%'  LIKE '%${searchStr}%' OR rcr.user._id LIKE '%${searchStr}%' OR rcr.client_id LIKE '%${searchStr}%' OR rcr.status LIKE '%${searchStr}%')`;
      }

      // Get total count for pagination
      const totalItemsResult: any = await this.callQuerySafe(`
        SELECT COUNT(*) AS total FROM reset_credentials_requests rcr 
        LEFT JOIN client_logins cl ON rcr.user_id = cl.id 
        LEFT JOIN clients c ON rcr.client_id = c.client_id 
        ${whereClause}
      `);

      const totalItems = totalItemsResult[0]?.total || 0;
      const totalPages = Math.ceil(totalItems / limit);

      // Fetch paginated reset password requests
      const passwordResetRequests: any = await this.callQuerySafe(`
        SELECT 
          rcr.*,
          cl.id as login_id,
          cl.first_name,
          cl.last_name,
          cl.email as login_email,
          c.business_name
        FROM reset_credentials_requests rcr
        LEFT JOIN client_logins cl ON rcr.user_id = cl.id
        LEFT JOIN clients c ON rcr.client_id = c.client_id
        ${whereClause}
        ORDER BY rcr.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `);

      // Calculate pagination properties
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      const paginationInfo = {
        items: passwordResetRequests,
        pagination: {
          current_page: page,
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: limit,
          hasNextPage,
          hasPrevPage,
          next_page: hasNextPage ? page + 1 : null,
          previous_page: hasPrevPage ? page - 1 : null
        }
      };

      return this.makeResponse(200, "Password reset requests fetched successfully", paginationInfo);
    } catch (err: any) {

      console.log("Error getting credentials reset request:", err);
      return this.makeResponse(500, "Error getting credentials reset request");
    }
  }



  async getOperationAppLogs(data: any) {
    try {
      // Pagination defaults
      const page = data?.page && Number(data.page) > 0 ? Number(data.page) : 1;
      const limit = data?.size && Number(data.size) > 0 ? Number(data.size) : 8;
      const offset = (page - 1) * limit;

      // Build where clause
      let whereClause = `WHERE 1=1`;

      // Add operation type filter if provided
      if (data?.operation_type && data?.operation_type !== null) {
        whereClause += ` AND operation_type = '${data?.operation_type}'`;
      }

      // Add table name filter if provided
      if (data?.table_name && data?.table_name !== null) {
        whereClause += ` AND table_name = '${data?.table_name}'`;
      }

      // Add executed_by filter if provided
      if (data?.executed_by && data?.executed_by !== null) {
        whereClause += ` AND executed_by = '${data?.executed_by}'`;
      }

      // Add search filter if provided
      if (data?.search && typeof data.search === "string" && data.search.trim() !== "") {
        const searchStr = data.search.trim().replace(/'/g, "''");
        whereClause += ` AND (operation_type LIKE '%${searchStr}%' OR table_name LIKE '%${searchStr}%' OR executed_by LIKE '%${searchStr}%' OR record_id LIKE '%${searchStr}%' OR ip_address LIKE '%${searchStr}%')`;
      }

      // Get total count for pagination
      const totalItemsResult: any = await this.callQuerySafe(`
        SELECT COUNT(*) AS total FROM operation_logs ${whereClause}
      `);

      const totalItems = totalItemsResult[0]?.total || 0;
      const totalPages = Math.ceil(totalItems / limit);

      // Check if there are any logs
      if (totalItems === 0) {
        return this.makeResponse(203, "No operation logs found");
      }

      // Fetch paginated operation logs
      const operationLogs: any = await this.callQuerySafe(`
        SELECT * FROM operation_logs
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `);

      // Calculate pagination properties
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      const paginationInfo = {
        items: operationLogs,
        pagination: {
          current_page: page,
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: limit,
          hasNextPage,
          hasPrevPage,
          next_page: hasNextPage ? page + 1 : null,
          previous_page: hasPrevPage ? page - 1 : null
        }
      };

      return this.makeResponse(200, "Operation logs fetched successfully", paginationInfo);
    } catch (err: any) {
      console.log("Error getting operation logs:", err);
      return this.makeResponse(500, "Error getting operation logs");
    }
  }


  async confirmTeamPasswordReset(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const resetRequest: any[] = await this.selectDataQuerySafe("reset_credentials_requests", { id: id, reset_type: 'PASSWORD', used: 0 });
      if (resetRequest.length === 0) {
        return this.makeResponse(400, "Reset request not found");
      }

      

      let existingAdmin: any[];
      if(resetRequest[0].request_initiator === 'SYSTEM_USER'){
        existingAdmin = await this.selectDataQuerySafe("system_users", { id: resetRequest[0].user_id });
        if (existingAdmin.length === 0) {
          return this.makeResponse(400, "Admin account is not known");
        }
      } else {
        existingAdmin = await this.selectDataQuerySafe("client_logins", { id: resetRequest[0].user_id });
        if (existingAdmin.length === 0) {
          return this.makeResponse(400, "Business admin is not known");
        }
      }
      

      let saveContactUserData: any = {}
      let password: any;
      // Generate a secure password that meets policy requirements
      password = this.generateSecurePassword(12, true);
      const encryptedPassword: any = await this.generatePassword(password);
      saveContactUserData.password = encryptedPassword;
      saveContactUserData.default_password = true;

    
      if(resetRequest[0].request_initiator === 'SYSTEM_USER'){

          // update clinet login 
          await this.updateData("system_users", `id = '${resetRequest[0].user_id}'`, saveContactUserData);
          // update reset credentials requests 
          const saveContactUserData_ = {
            used: 1,
            request_approver_id: data?.userId,
            updated_at: this.getMySQLDateTime_()
          }
          await this.updateData("reset_credentials_requests", `user_id = '${resetRequest[0].user_id}' AND request_initiator = 'SYSTEM_USER' AND reset_type = 'PASSWORD'`, saveContactUserData_);
          this.sendEmail("USER_PASSWORD_CHANGE", existingAdmin[0].email, existingAdmin[0]?.first_name ?? "", password);
          await this.saveOperationLog("UPDATE", existingAdmin[0].client_id, existingAdmin[0].id, "client", "reset_credentials_requests", "", saveContactUserData_, "1")
          await this.saveOperationLog("UPDATE", existingAdmin[0].client_id, existingAdmin[0].id, "client", "client_logins", "", saveContactUserData, "1")

      } else {

          await this.updateData("client_logins", `id = '${resetRequest[0].user_id}'`, saveContactUserData);
          // update reset credentials requests 
          const saveContactUserData_ = {
            used: 1,
            request_approver_id: data?.userId,
            updated_at: this.getMySQLDateTime_()
          }
          await this.updateData("reset_credentials_requests", `user_id = '${resetRequest[0].user_id}' AND client_id = '${existingAdmin[0].client_id}' AND reset_type = 'PASSWORD'`, saveContactUserData_);
          this.sendEmail("CLIENT_PASSWORD_CHANGE", existingAdmin[0].email, existingAdmin[0]?.first_name ?? "", password);
          await this.saveOperationLog("UPDATE", existingAdmin[0].client_id, existingAdmin[0].id, "client", "reset_credentials_requests", "", saveContactUserData_, "1")
          await this.saveOperationLog("UPDATE", existingAdmin[0].client_id, existingAdmin[0].id, "client", "client_logins", "", saveContactUserData, "1")
      }
      
      return this.makeResponse(200, (resetRequest[0].request_initiator === 'SYSTEM_USER')? "Admin password reset has been confirmed successfully":"User password reset has been confirmed successfully");
    } catch (error: any) {
      console.log("Error updating user password", error)
      return this.makeResponse(500, "Error confirming user password reset");
    }
  }


  async cancelTeamPasswordReset(id: string, data: any) {
    try {

      const resetRequest: any[] = await this.selectDataQuerySafe("reset_credentials_requests", { id: id, reset_type: 'PASSWORD', used: 0 });
      if (resetRequest.length === 0) {
        return this.makeResponse(400, "Reset request not found");
      }

      let existingAdmin: any[];
      if(resetRequest[0].request_initiator === 'SYSTEM_USER'){
        existingAdmin = await this.selectDataQuerySafe("system_users", { id: resetRequest[0].user_id });
        if (existingAdmin.length === 0) {
          return this.makeResponse(400, "Admin account is not known");
        }
      } else {
        existingAdmin = await this.selectDataQuerySafe("client_logins", { id: resetRequest[0].user_id });
        if (existingAdmin.length === 0) {
          return this.makeResponse(400, "Business admin is not known");
        }
      }

      // update reset credentials requests 
      const saveContactUserData_ = {
        used: 1,
        request_approver_id: data?.userId,
        updated_at: this.getMySQLDateTime_()
      }

      if(resetRequest[0].request_initiator === 'SYSTEM_USER'){
        await this.updateData("reset_credentials_requests", `user_id = '${resetRequest[0].user_id}' AND request_initiator = 'SYSTEM_USER' AND reset_type = 'PASSWORD'`, saveContactUserData_);
      } else {
        await this.updateData("reset_credentials_requests", `user_id = '${resetRequest[0].user_id}' AND client_id = '${existingAdmin[0].client_id}' AND reset_type = 'PASSWORD'`, saveContactUserData_);
      }
      return this.makeResponse(200, (resetRequest[0].request_initiator === 'SYSTEM_USER')? "Admin account password reset has been cancelled successfully": "User password reset has been cancelled successfully");

    } catch (error: any) {
      console.log("Error cancelling user password reset >>>>>>>>>>>>>", error)
      return this.makeResponse(500, "Error cancelling user password reset");
    }
  }


  async confirmTeam2faReset(id: string, data: any) {
    try {


      const client_id: any = data?.userId;
      data.user_type = 'admin';
      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const resetRequest: any[] = await this.selectDataQuerySafe("reset_credentials_requests", { id: id, reset_type: '2FA', used: 0 });
      if (resetRequest.length === 0) {
        return this.makeResponse(400, "Reset request not found");
      }


      let existingAdmin: any[];

      if(resetRequest[0].request_initiator === 'SYSTEM_USER'){
       
        existingAdmin = await this.selectDataQuerySafe("system_users", { id: resetRequest[0].user_id });
        if (existingAdmin.length === 0) {
          return this.makeResponse(400, "Admin account is not known");
        }
        await this.deleteData("user_2fa", `user_id = '${resetRequest[0].user_id}' AND user_type = 'admin'`);
      } else {

        existingAdmin = await this.selectDataQuerySafe("client_logins", { id: resetRequest[0].user_id });
        if (existingAdmin.length === 0) {
          return this.makeResponse(400, "Business admin is not known");
        }
        await this.deleteData("user_2fa", `user_id = '${resetRequest[0].user_id}' AND user_type = 'client'`);
      }  
        

      // update reset credentials requests 
      const saveContactUserData_ = {
        used: 1,
        request_approver_id: data?.userId,
        updated_at: this.getMySQLDateTime_()
      }

      if(resetRequest[0].request_initiator === 'SYSTEM_USER'){

          await this.updateData("reset_credentials_requests", `user_id = '${resetRequest[0].user_id}' AND request_initiator = 'SYSTEM_USER' AND reset_type = '2FA'`, saveContactUserData_);
          // send mail
          this.sendEmail("USER_2FA_CHANGE", existingAdmin[0].email, existingAdmin[0]?.first_name ?? "", "");
      } else {

          await this.updateData("reset_credentials_requests", `user_id = '${resetRequest[0].user_id}' AND client_id = '${existingAdmin[0].client_id}' AND reset_type = '2FA'`, saveContactUserData_);
          // send mail
          this.sendEmail("CLIENT_2FA_CHANGE", existingAdmin[0].email, existingAdmin[0]?.first_name ?? "", "");z
      }    
      return this.makeResponse(200, "user password reset email sent successfully");
    
    
    } catch (error: any) {

      console.log("Error updating user password", error)
      return this.makeResponse(500, "Error updating user password");
    }
  }


  async cancelTeam2faReset(id: string, data: any) {
    try {
      const resetRequest: any[] = await this.selectDataQuerySafe("reset_credentials_requests", { id: id, reset_type: '2FA', used: 0 });
      if (resetRequest.length === 0) {
        return this.makeResponse(400, "Reset request not found");
      }

      let existingAdmin: any[];
      if(resetRequest[0].request_initiator === 'SYSTEM_USER'){
        existingAdmin = await this.selectDataQuerySafe("system_users", { id: resetRequest[0].user_id });
        if (existingAdmin.length === 0) {
          return this.makeResponse(400, "Admin account is not known");
        }
      } else {
        existingAdmin = await this.selectDataQuerySafe("client_logins", { id: resetRequest[0].user_id });
        if (existingAdmin.length === 0) {
          return this.makeResponse(400, "Business admin is not known");
        }
      }

      // update reset credentials requests 
      const saveContactUserData_ = {
                  used: 1,
                  request_approver_id: data?.userId,
                  updated_at: this.getMySQLDateTime_()
              }

      if(resetRequest[0].request_initiator === 'SYSTEM_USER'){
        await this.updateData("reset_credentials_requests", `user_id = '${resetRequest[0].user_id}' AND request_initiator = 'SYSTEM_USER' AND reset_type = '2FA'`, saveContactUserData_);
      } else {
        await this.updateData("reset_credentials_requests", `user_id = '${resetRequest[0].user_id}' AND client_id = '${existingAdmin[0].client_id}' AND reset_type = '2FA'`, saveContactUserData_);
      }
      return this.makeResponse(200,  (resetRequest[0].request_initiator === 'SYSTEM_USER')? "Admin user 2fa reset has been cancelled successfully" : "User 2fa reset has been cancelled successfully");

    } catch (error: any) {
      console.log("Error cancelling user password reset", error)
      return this.makeResponse(500, "Error cancelling user password reset");
    }
  }



  /***
   *  get the menu list of logged user
   * */
  async getMenuList(data: any) {
    try {
      const menuList: any = await this.callRawQuery(`
        SELECT * FROM app_menu_items 
        WHERE menu_type = 'ADMIN' 
        ORDER BY menu_precision ASC
      `);
      
      const parentMenuList: any = menuList.filter((menu: any) => menu.parent_id === null);
      const childMenuList: any = menuList.filter((menu: any) => menu.parent_id !== null);
      
      // Add third-level children (grandchildren)
      const menuList_ = parentMenuList.map((parent: any) => {
        const children = childMenuList
          .filter((child: any) => child.parent_id === parent.id)
          .map((child: any) => {
            const grandChildren = childMenuList.filter(
              (grand: any) => grand.parent_id === child.id
            );
            return { ...child, children: grandChildren };
          });
      
        return { ...parent, children };
      });
      return this.makeResponse(200, "Menu list fetched successfully", menuList_);
    } catch (error: any) {
      console.log("Error getting menu list", error)
      return this.makeResponse(500, "Error getting menu list"); 
    }
  }
}