import ExportHelper from '../helpers/export.helper';
import Model from '../helpers/model';
import { getCurrentDateTimeMySQL, formatDateTimeForMySQL } from '../helpers/dateHelper';
import EmailSender from "../helpers/email.helper";

interface ExportJobParams {
  id: string;
  file_name: string;
  file_type: string;
  export_type: string;
  filters: any;
  total_records: number;
  client_id?: string;
  report_type?: string;
  created_by?: string;
}

const sendmail = new EmailSender()
export default class ExportJobService {
  private model: Model;

  constructor() {
    this.model = new Model();
  }

  /**
   * Process a queued export job
   */
  async processExportJob(job: ExportJobParams): Promise<void> {
    try {
      console.log(`Starting to process export job #${job.id}`);
      
      // Update status to PROCESSING
      await this.model.updateDataSafe(
        'export_files',
        { id: job.id },
        { 
          status: 'PROCESSING',
          file_key: 'processing', // Temporary value
          file_url: 'processing'  // Temporary value
        }
      );

      // Generate filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${job.export_type}_export_${timestamp}`;

      // Fetch data based on export type
      let formattedData: any[] = [];
      
      if (job.export_type === 'transactions') {
        formattedData = await this.fetchTransactionData(job.filters);
      } else if (job.export_type === 'reconciliation') {
        formattedData = await this.fetchReconciliationData(job.filters);
      }

      // Check if data is empty
      if (!formattedData || formattedData.length === 0) {
        // Update status to FAILED with no data message
        await this.model.updateDataSafe(
          'export_files',
          { id: job.id },
          {
            status: 'FAILED',
            error_message: 'No data found for the selected filters and date range',
            completed_at: getCurrentDateTimeMySQL()
          }
        );
        console.log(`Export job #${job.id} completed with no data`);
        return;
      }

      // Export to file format
      let exportResult;
      if (job.file_type === 'pdf') {
        exportResult = await ExportHelper.exportToPDFAndUpload(
          formattedData,
          filename,
          `${job.export_type === 'transactions' ? 'Transactions' : 'Reconciliation'} Export Report`
        );
      } else {
        exportResult = await ExportHelper.exportToExcelAndUpload(
          formattedData,
          filename,
          job.export_type === 'transactions' ? 'Transactions' : 'Reconciliation'
        );
      }

      // Update with final results
      await this.model.updateDataSafe(
        'export_files',
        { id: job.id },
        {
          status: 'COMPLETED',
          file_name: exportResult.filename,
          file_key: exportResult.key,
          file_url: exportResult.url,
          total_records: formattedData.length,
          completed_at: getCurrentDateTimeMySQL()
        }
      );

      // Send email notification if report_type is ADMIN
      if (job.report_type === 'ADMIN' && job.created_by) {
        try {
          // Get user email from system_users table
          const userResult: any = await this.model.callQuerySafe(
            `SELECT email, first_name, last_name FROM system_users WHERE id = ?`,
            [job.created_by]
          );
          
          if (userResult && userResult.length > 0 && userResult[0].email) {
            const userEmail = userResult[0].email;
            const userName = `${userResult[0].first_name || ''} ${userResult[0].last_name || ''}`.trim() || 'User';
            const fileUrl = exportResult.url;
            
            // Send email using notification template "REPORT_DOWNLOAD"
            await this.model.sendDownloadEmail(
              'REPORT_DOWNLOAD',
              userEmail,
              userName,
              { file_url: fileUrl, report_name: exportResult.filename, hours: 6 }
            );
            
            console.log(`Email notification sent to ${userEmail} for export job #${job.id}`);
          } else {
            console.log(`User not found or email missing for created_by: ${job.created_by}`);
          }
        } catch (emailError: any) {
          console.error(`Error sending email notification for export job #${job.id}:`, emailError);
          // Don't fail the job if email fails
        }
      }

      console.log(`Successfully completed export job #${job.id}`);
    } catch (error: any) {
      console.error(`Error processing export job #${job.id}:`, error);
      
      // Update status to FAILED
      await this.model.updateDataSafe(
        'export_files',
        { id: job.id },
        {
          status: 'FAILED',
          error_message: error.message || 'Unknown error occurred',
          completed_at: getCurrentDateTimeMySQL()
        }
      );
      
      throw error;
    }
  }

  /**
   * Count transaction records based on filters
   */
  async countTransactionRecords(filters: any): Promise<number> {
    try {
      const params: any[] = [];
      const whereConditions: string[] = [];

      // Build where clause based on filters
      if (filters.search?.trim()) {
        whereConditions.push(`(reference_id LIKE ? OR service_name LIKE ? OR receiver_account LIKE ?)`);
        params.push(`%${filters.search.trim()}%`, `%${filters.search.trim()}%`, `%${filters.search.trim()}%`);
      }
      if (filters.currency?.trim()) {
        whereConditions.push(`currency = ?`);
        params.push(filters.currency.trim().toUpperCase());
      }
      if (filters.transaction_type?.trim()) {
        whereConditions.push(`trans_type = ?`);
        params.push(filters.transaction_type.trim());
      }
      if (filters.status?.trim()) {
        whereConditions.push(`status = ?`);
        params.push(filters.status.trim().toUpperCase());
      }
      if (filters.message?.trim()) {
        whereConditions.push(`message LIKE ?`);
        params.push(`%${filters.message.trim()}%`);
      }
      if (filters.start_date?.trim() && filters.end_date?.trim()) {
        const startDateFormatted = new Date(filters.start_date.trim()).toISOString();
        const endDateFormatted = new Date(filters.end_date.trim()).toISOString();
        whereConditions.push(`created_at >= ? AND created_at <= ?`);
        params.push(startDateFormatted, endDateFormatted);
      }

      const whereClause = whereConditions.length ? `WHERE ${whereConditions.join(' AND ')}` : '';
      const query = `SELECT COUNT(*) as count FROM transactions ${whereClause}`;

      const result: any = await this.model.callQuerySafe(query, params);
      return result[0]?.count || 0;
    } catch (error: any) {
      console.error('Error counting transaction records:', error);
      throw new Error(`Failed to count transaction records: ${error.message}`);
    }
  }

  /**
   * Count reconciliation records based on filters
   */
  async countReconciliationRecords(filters: any): Promise<number> {
    try {
      const params: any[] = [];
      const whereConditions: string[] = [];

      if (filters.client_id) {
        whereConditions.push(`client_id = ?`);
        params.push(filters.client_id);
      }

      if (filters.search?.trim()) {
        whereConditions.push(`(reference_id LIKE ? OR service_name LIKE ? OR receiver_account LIKE ?)`);
        params.push(`%${filters.search.trim()}%`, `%${filters.search.trim()}%`, `%${filters.search.trim()}%`);
      }
      if (filters.currency?.trim()) {
        whereConditions.push(`currency = ?`);
        params.push(filters.currency.trim().toUpperCase());
      }
      if (filters.transaction_type?.trim()) {
        whereConditions.push(`trans_type = ?`);
        params.push(filters.transaction_type.trim());
      }
      if (filters.status?.trim()) {
        whereConditions.push(`status = ?`);
        params.push(filters.status.trim().toUpperCase());
      }
      if (filters.message?.trim()) {
        whereConditions.push(`message LIKE ?`);
        params.push(`%${filters.message.trim()}%`);
      }
      if (filters.start_date?.trim() && filters.end_date?.trim()) {
        const startDateFormatted = new Date(filters.start_date.trim()).toISOString();
        const endDateFormatted = new Date(filters.end_date.trim()).toISOString();
        whereConditions.push(`created_at >= ? AND created_at <= ?`);
        params.push(startDateFormatted, endDateFormatted);
      }

      const whereClause = whereConditions.length ? `WHERE ${whereConditions.join(' AND ')}` : '';
      const query = `SELECT COUNT(*) as count FROM transactions ${whereClause}`;

      const result: any = await this.model.callQuerySafe(query, params);
      return result[0]?.count || 0;
    } catch (error: any) {
      console.error('Error counting reconciliation records:', error);
      throw new Error(`Failed to count reconciliation records: ${error.message}`);
    }
  }

  /**
   * Fetch transaction data based on filters
   */
  async fetchTransactionData(filters: any): Promise<any[]> {
    try {
      const params: any[] = [];
      const whereConditions: string[] = [];

      // Build where clause based on filters
      if (filters.search?.trim()) {
        whereConditions.push(`(reference_id LIKE ? OR service_name LIKE ? OR receiver_account LIKE ?)`);
        params.push(`%${filters.search.trim()}%`, `%${filters.search.trim()}%`, `%${filters.search.trim()}%`);
      }
      if (filters.currency?.trim()) {
        whereConditions.push(`currency = ?`);
        params.push(filters.currency.trim().toUpperCase());
      }
      if (filters.transaction_type?.trim()) {
        whereConditions.push(`trans_type = ?`);
        params.push(filters.transaction_type.trim());
      }
      if (filters.status?.trim()) {
        whereConditions.push(`status = ?`);
        params.push(filters.status.trim().toUpperCase());
      }
      if (filters.message?.trim()) {
        whereConditions.push(`message LIKE ?`);
        params.push(`%${filters.message.trim()}%`);
      }
      if (filters.start_date?.trim() && filters.end_date?.trim()) {
        const startDateFormatted = new Date(filters.start_date.trim()).toISOString();
        const endDateFormatted = new Date(filters.end_date.trim()).toISOString();
        whereConditions.push(`created_at >= ? AND created_at <= ?`);
        params.push(startDateFormatted, endDateFormatted);
      }

      const whereClause = whereConditions.length ? `WHERE ${whereConditions.join(' AND ')}` : '';
      const query = `SELECT * FROM transactions ${whereClause} ORDER BY created_at DESC`;

      // Fetch all records without pagination
      const transactionsResult: any = await this.model.callQuerySafe(query, params);
      const transactions: any[] = Array.isArray(transactionsResult) ? transactionsResult : [];
      
      // Format for export
      return ExportHelper.formatTransactionData(transactions);
    } catch (error: any) {
      console.error('Error fetching transaction data:', error);
      throw new Error(`Failed to fetch transaction data: ${error.message}`);
    }
  }

  /**
   * Fetch reconciliation data based on filters
   */
  async fetchReconciliationData(filters: any): Promise<any[]> {
    try {
      // Similar to transactions but with client_id
      const params: any[] = [];
      const whereConditions: string[] = [];

      if (filters.client_id) {
        whereConditions.push(`client_id = ?`);
        params.push(filters.client_id);
      }

      // Build where clause based on filters
      if (filters.search?.trim()) {
        whereConditions.push(`(reference_id LIKE ? OR service_name LIKE ? OR receiver_account LIKE ?)`);
        params.push(`%${filters.search.trim()}%`, `%${filters.search.trim()}%`, `%${filters.search.trim()}%`);
      }
      if (filters.currency?.trim()) {
        whereConditions.push(`currency = ?`);
        params.push(filters.currency.trim().toUpperCase());
      }
      if (filters.transaction_type?.trim()) {
        whereConditions.push(`trans_type = ?`);
        params.push(filters.transaction_type.trim());
      }
      if (filters.status?.trim()) {
        whereConditions.push(`status = ?`);
        params.push(filters.status.trim().toUpperCase());
      }
      if (filters.message?.trim()) {
        whereConditions.push(`message LIKE ?`);
        params.push(`%${filters.message.trim()}%`);
      }
      if (filters.start_date?.trim() && filters.end_date?.trim()) {
        const startDateFormatted = new Date(filters.start_date.trim()).toISOString();
        const endDateFormatted = new Date(filters.end_date.trim()).toISOString();
        whereConditions.push(`created_at >= ? AND created_at <= ?`);
        params.push(startDateFormatted, endDateFormatted);
      }

      const whereClause = whereConditions.length ? `WHERE ${whereConditions.join(' AND ')}` : '';
      const query = `SELECT * FROM transactions ${whereClause} ORDER BY created_at DESC`;

      // Fetch all records without pagination
      const transactionsResult: any = await this.model.callQuerySafe(query, params);
      const transactions: any[] = Array.isArray(transactionsResult) ? transactionsResult : [];
      
      // Format for export
      return ExportHelper.formatTransactionData(transactions);
    } catch (error: any) {
      console.error('Error fetching reconciliation data:', error);
      throw new Error(`Failed to fetch reconciliation data: ${error.message}`);
    }
  }

  /**
   * Get pending export jobs
   */
  async getPendingJobs(limit: number = 5): Promise<any[]> {
    try {
      const jobsResult: any = await this.model.callQuerySafe(
        `SELECT * FROM export_files WHERE status = 'PENDING' AND deleted = FALSE ORDER BY created_at ASC LIMIT ?`,
        [limit]
      );
      const jobs: any[] = Array.isArray(jobsResult) ? jobsResult : [];
      return jobs;
    } catch (error: any) {
      console.error('Error fetching pending jobs:', error);
      return [];
    }
  }

  /**
   * Check for stuck processing jobs and reset them
   */
  async resetStuckJobs(): Promise<void> {
    try {
      // Reset jobs that have been processing for more than 1 hour
      const oneHourAgo = formatDateTimeForMySQL(new Date(Date.now() - 60 * 60 * 1000));
      
      await this.model.callQuerySafe(
        `UPDATE export_files SET status = 'PENDING', file_key = NULL, file_url = NULL 
         WHERE status = 'PROCESSING' AND created_at < ?`,
        [oneHourAgo]
      );
      
      console.log('Reset stuck processing jobs');
    } catch (error: any) {
      console.error('Error resetting stuck jobs:', error);
    }
  }
}

