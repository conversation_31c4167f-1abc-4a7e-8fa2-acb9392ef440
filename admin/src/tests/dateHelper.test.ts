import { validateDateRange, getMaxExportDateRange } from '../helpers/dateHelper';

describe('DateHelper - Date Range Validation', () => {
  describe('validateDateRange', () => {
    it('should allow valid date range within 2 months', () => {
      const result = validateDateRange('2024-01-01', '2024-02-28', 2);
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should allow same start and end date', () => {
      const result = validateDateRange('2024-01-15', '2024-01-15', 2);
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject 3-month date range', () => {
      const result = validateDateRange('2024-01-01', '2024-03-01', 2);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('exceeds the maximum allowed duration');
    });

    it('should reject date range exceeding 2 months', () => {
      const result = validateDateRange('2024-01-01', '2024-04-01', 2);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('exceeds the maximum allowed duration');
    });

    it('should reject when start date is after end date', () => {
      const result = validateDateRange('2024-02-01', '2024-01-01', 2);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Start date must be before or equal to end date');
    });

    it('should reject invalid date format', () => {
      const result = validateDateRange('invalid-date', '2024-01-01', 2);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Invalid date format');
    });

    it('should reject invalid date format for end date', () => {
      const result = validateDateRange('2024-01-01', 'invalid-date', 2);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Invalid date format');
    });

    it('should allow when dates are not provided', () => {
      const result = validateDateRange(null, null, 2);
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should allow when only one date is provided', () => {
      const result1 = validateDateRange('2024-01-01', null, 2);
      expect(result1.isValid).toBe(true);

      const result2 = validateDateRange(null, '2024-01-01', 2);
      expect(result2.isValid).toBe(true);
    });

    it('should allow single month range', () => {
      const result = validateDateRange('2024-01-01', '2024-01-31', 2);
      expect(result.isValid).toBe(true);
    });

    it('should handle different year ranges correctly', () => {
      const result = validateDateRange('2023-12-01', '2024-01-31', 2);
      expect(result.isValid).toBe(true);
    });

    it('should calculate 3-month range correctly', () => {
      const result = validateDateRange('2024-01-01', '2024-04-01', 2);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('2 months');
    });

    it('should allow custom maxMonths value', () => {
      const result = validateDateRange('2024-01-01', '2024-06-30', 6);
      expect(result.isValid).toBe(true);
    });

    it('should allow January to February (same year) - exactly 2 months', () => {
      const result = validateDateRange('2024-01-01', '2024-02-28', 2);
      expect(result.isValid).toBe(true);
    });

    it('should reject 4-month range', () => {
      const result = validateDateRange('2024-01-01', '2024-05-01', 2);
      expect(result.isValid).toBe(false);
    });

    it('should provide parsed date objects when valid', () => {
      const result = validateDateRange('2024-01-15', '2024-02-15', 2);
      expect(result.isValid).toBe(true);
      expect(result.start).toBeInstanceOf(Date);
      expect(result.end).toBeInstanceOf(Date);
    });
  });

  describe('getMaxExportDateRange', () => {
    it('should return valid date range', () => {
      const result = getMaxExportDateRange();
      expect(result).toHaveProperty('minDate');
      expect(result).toHaveProperty('maxDate');
      expect(result.minDate).toMatch(/^\d{4}-\d{2}-\d{2}$/);
      expect(result.maxDate).toMatch(/^\d{4}-\d{2}-\d{2}$/);
    });

    it('should return maxDate as today', () => {
      const result = getMaxExportDateRange();
      const today = new Date().toISOString().split('T')[0];
      expect(result.maxDate).toBe(today);
    });

    it('should return minDate 2 months ago', () => {
      const result = getMaxExportDateRange();
      const expectedDate = new Date();
      expectedDate.setMonth(expectedDate.getMonth() - 2);
      const expectedMinDate = expectedDate.toISOString().split('T')[0];
      
      // Due to month rolling, dates might differ by 1-2 days
      // Verify the dates are within a reasonable range
      const diffInDays = Math.abs(
        (new Date(result.minDate).getTime() - new Date(expectedMinDate).getTime()) / (1000 * 60 * 60 * 24)
      );
      expect(diffInDays).toBeLessThanOrEqual(2);
    });
  });
});

