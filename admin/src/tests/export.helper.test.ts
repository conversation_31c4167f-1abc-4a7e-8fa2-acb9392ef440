import ExportHelper from '../helpers/export.helper';
import ExcelJS from 'exceljs';

describe('ExportHelper - Excel Export', () => {
  describe('exportToExcel', () => {
    it('should export valid data to Excel format', async () => {
      // Arrange
      const testData = [
        {
          'Trans ID': 'TXN001',
          'Client ID': 'CLIENT001',
          'Amount': '1000.00',
          'Status': 'SUCCESS',
          'Currency': 'UGX',
          'Created At': '2024-01-01 10:00:00'
        },
        {
          'Trans ID': 'TXN002',
          'Client ID': 'CLIENT002',
          'Amount': '2000.00',
          'Status': 'PENDING',
          'Currency': 'USD',
          'Created At': '2024-01-02 11:00:00'
        }
      ];
      const filename = 'test_export';

      // Act
      const result = await ExportHelper.exportToExcel(testData, filename);

      // Assert
      expect(result).toBeDefined();
      expect(result.buffer).toBeInstanceOf(Buffer);
      expect(result.buffer.length).toBeGreaterThan(0);
      expect(result.filename).toBe('test_export.xlsx');
      expect(result.mime_type).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

      // Verify Excel file can be read
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(result.buffer as any);
      
      const worksheet = workbook.getWorksheet('Data');
      expect(worksheet).toBeDefined();
      
      // Check headers
      const headerRow = worksheet?.getRow(1);
      expect(headerRow?.values).toContain('Trans ID');
      expect(headerRow?.values).toContain('Client ID');
      expect(headerRow?.values).toContain('Amount');
      
      // Check data rows
      expect(worksheet?.rowCount).toBeGreaterThan(1);
      const firstDataRow = worksheet?.getRow(2);
      expect(firstDataRow?.getCell(1).value).toBe('TXN001');
    });

    it('should handle empty data array', async () => {
      // Arrange
      const testData: any[] = [];
      const filename = 'empty_export';

      // Act
      const result = await ExportHelper.exportToExcel(testData, filename);

      // Assert
      expect(result).toBeDefined();
      expect(result.buffer).toBeInstanceOf(Buffer);
      expect(result.buffer.length).toBeGreaterThan(0);
      expect(result.filename).toBe('empty_export.xlsx');

      // Verify Excel file can be read
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(result.buffer as any);
      
      const worksheet = workbook.getWorksheet('Data');
      expect(worksheet).toBeDefined();
      expect(worksheet?.rowCount).toBeGreaterThanOrEqual(2); // Headers + info row
    });

    it('should handle data with null and undefined values', async () => {
      // Arrange
      const testData = [
        {
          'Trans ID': 'TXN001',
          'Client ID': 'CLIENT001',
          'Amount': null,
          'Status': undefined,
          'Currency': 'UGX'
        }
      ];
      const filename = 'null_values_export';

      // Act
      const result = await ExportHelper.exportToExcel(testData, filename);

      // Assert
      expect(result).toBeDefined();
      expect(result.buffer).toBeInstanceOf(Buffer);
      expect(result.buffer.length).toBeGreaterThan(0);
      
      // Verify Excel file can be read
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(result.buffer as any);
      const worksheet = workbook.getWorksheet('Data');
      
      // Check that null/undefined values are converted to empty strings
      const dataRow = worksheet?.getRow(2);
      expect(dataRow?.getCell(3).value).toBe(''); // Amount column
      expect(dataRow?.getCell(4).value).toBe(''); // Status column
    });

    it('should handle date values', async () => {
      // Arrange
      const testDate = new Date('2024-01-01');
      const testData = [
        {
          'Trans ID': 'TXN001',
          'Date': testDate
        }
      ];
      const filename = 'date_export';

      // Act
      const result = await ExportHelper.exportToExcel(testData, filename);

      // Assert
      expect(result).toBeDefined();
      expect(result.buffer).toBeInstanceOf(Buffer);
      
      // Verify Excel file can be read
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(result.buffer as any);
      const worksheet = workbook.getWorksheet('Data');
      
      // Check that date is converted to string
      const dataRow = worksheet?.getRow(2);
      expect(typeof dataRow?.getCell(2).value).toBe('string');
    });

    it('should use custom sheet name', async () => {
      // Arrange
      const testData = [{ 'ID': '1', 'Name': 'Test' }];
      const filename = 'custom_sheet';
      const sheetName = 'Transactions';

      // Act
      const result = await ExportHelper.exportToExcel(testData, filename, sheetName);

      // Assert
      expect(result).toBeDefined();
      
      // Verify Excel file has custom sheet name
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(result.buffer as any);
      const worksheet = workbook.getWorksheet(sheetName);
      expect(worksheet).toBeDefined();
    });

    it('should throw error for invalid data type', async () => {
      // Arrange
      const testData = 'invalid data' as any;
      const filename = 'invalid_export';

      // Act & Assert
      await expect(ExportHelper.exportToExcel(testData, filename)).rejects.toThrow('Invalid export data provided');
    });

    it('should handle large datasets', async () => {
      // Arrange - Generate large dataset
      const testData = Array.from({ length: 1000 }, (_, i) => ({
        'ID': `TXN${i}`,
        'Amount': (i * 100).toString(),
        'Status': 'SUCCESS',
        'Timestamp': new Date().toISOString()
      }));
      const filename = 'large_export';

      // Act
      const result = await ExportHelper.exportToExcel(testData, filename);

      // Assert
      expect(result).toBeDefined();
      expect(result.buffer).toBeInstanceOf(Buffer);
      expect(result.buffer.length).toBeGreaterThan(0);
      
      // Verify all rows are present
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(result.buffer as any);
      const worksheet = workbook.getWorksheet('Data');
      expect(worksheet?.rowCount).toBe(1001); // 1000 data rows + 1 header row
    });

    it('should auto-fit columns', async () => {
      // Arrange
      const testData = [
        { 'Short': 'A', 'LongColumn': 'This is a very long string that should trigger column auto-fit', 'Number': '12345' }
      ];
      const filename = 'autofit_export';

      // Act
      const result = await ExportHelper.exportToExcel(testData, filename);

      // Assert
      expect(result).toBeDefined();
      
      // Verify Excel file can be read
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(result.buffer as any);
      const worksheet = workbook.getWorksheet('Data');
      
      // Check that columns have width set
      const columns = worksheet?.columns;
      expect(columns).toBeDefined();
    });

    it('should apply proper styling to headers', async () => {
      // Arrange
      const testData = [{ 'ID': '1', 'Name': 'Test' }];
      const filename = 'styled_export';

      // Act
      const result = await ExportHelper.exportToExcel(testData, filename);

      // Assert
      expect(result).toBeDefined();
      
      // Verify Excel file can be read
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(result.buffer as any);
      const worksheet: any  = workbook.getWorksheet('Data');
      
      // Check header row styling
      const headerRow = worksheet?.getRow(1);
      expect(headerRow).toBeDefined();
      
      // Verify file has proper borders on all cells
      const allRows = worksheet?.rows;
      if (allRows && allRows.length > 0) {
        allRows.forEach((row: any) => {
          row.eachCell((cell: any) => {
            expect(cell.border).toBeDefined();
          });
        });
      }
    });

    it('should handle special characters in data', async () => {
      // Arrange
      const testData = [
        { 'Text': 'Test & Special <Characters> "Quote"', 'Symbol': '€$£¥', 'Number': '123,456.78' }
      ];
      const filename = 'special_chars_export';

      // Act
      const result = await ExportHelper.exportToExcel(testData, filename);

      // Assert
      expect(result).toBeDefined();
      expect(result.buffer.length).toBeGreaterThan(0);
      
      // Verify Excel file can be read
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(result.buffer as any);
      const worksheet = workbook.getWorksheet('Data');
      
      // Check that special characters are preserved
      const dataRow = worksheet?.getRow(2);
      expect(dataRow?.getCell(1).value).toContain('Special');
    });

    it('should validate buffer is proper Buffer object', async () => {
      // Arrange
      const testData = [{ 'ID': '1', 'Value': 'test' }];
      const filename = 'buffer_validation';

      // Act
      const result = await ExportHelper.exportToExcel(testData, filename);

      // Assert
      expect(result).toBeDefined();
      expect(Buffer.isBuffer(result.buffer)).toBe(true);
      expect(result.buffer.byteLength).toBeGreaterThan(0);
    });

    it('should handle numeric values correctly', async () => {
      // Arrange
      const testData = [
        { 'ID': '1', 'Amount': '1000', 'Fee': '50.25', 'Balance': '999999999.99' }
      ];
      const filename = 'numeric_export';

      // Act
      const result: any = await ExportHelper.exportToExcel(testData, filename);

      // Assert
      expect(result).toBeDefined();
      expect(result.buffer.length).toBeGreaterThan(0);
      
      // Verify Excel file can be read
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(result.buffer as any);
      const worksheet = workbook.getWorksheet('Data');
      
      // Check that numeric values are converted to strings
      const dataRow = worksheet?.getRow(2);
      expect(dataRow?.getCell(2).value).toBe('1000');
      expect(dataRow?.getCell(3).value).toBe('50.25');
    });
  });

  describe('validateExportData', () => {
    it('should validate array data correctly', () => {
      const validData = [{ id: 1, name: 'Test' }];
      expect(ExportHelper.validateExportData(validData)).toBe(true);
    });

    it('should return false for non-array data', () => {
      expect(ExportHelper.validateExportData('invalid' as any)).toBe(false);
      expect(ExportHelper.validateExportData(null as any)).toBe(false);
      expect(ExportHelper.validateExportData(undefined as any)).toBe(false);
    });

    it('should return true for empty array', () => {
      expect(ExportHelper.validateExportData([])).toBe(true);
    });

    it('should return false for array of non-objects', () => {
      expect(ExportHelper.validateExportData([1, 2, 3] as any)).toBe(false);
      expect(ExportHelper.validateExportData(['a', 'b', 'c'] as any)).toBe(false);
    });

    it('should return false for array with null values', () => {
      expect(ExportHelper.validateExportData([null] as any)).toBe(false);
    });

    it('should return true for array of valid objects', () => {
      const data = [
        { id: 1 },
        { id: 2, name: 'Test' },
        { complex: { nested: 'value' } }
      ];
      expect(ExportHelper.validateExportData(data)).toBe(true);
    });
  });
});

