import express, { Request, Response } from "express";
import Accounts from "../models/admin";
import { JWTMiddleware } from "../helpers/jwt.middleware";
import { LiquidityRailService } from "../services/liquidityrail.service";
import path from 'path';
import fs from 'fs';
import EmailSender from "../helpers/email.helper";
import Deposit from "../models/deposit";
import Withdraw from "../models/withdraw";
import Notifications from "../models/notifications";


const router = express.Router();
const accounts = new Accounts();
const liquidityRailService = new LiquidityRailService();
const sendmail = new EmailSender()
const deposits = new Deposit();
const withdraws = new Withdraw();
const notifications = new Notifications();
const applyJWTConditionally = (req: Request, res: Response, next: any) => {
  JWTMiddleware.verifyAdminToken(req, res, next);
  next()
};
const saveApiLog = (req: Request, res: Response, next: any) => {
  // thirdPartyHandler.saveApiLog(req.body, req.ip || "");  // Moved to wallet service
  next();
}

/**
 * ============================================================
 * PUBLIC ADMIN ENDPOINTS - LIMITED THIRD PARTY ACCESS
 * ============================================================
 * These endpoints can be shared with trusted third parties
 * who need limited administrative capabilities
 */

// Authentication endpoint (no JWT required)
router.post("/login", adminLogin);
router.post("/login/confirm", adminConfirmLogin);

// Client information endpoints
router.get("/clients/:id", getClientById);
router.get("/balances", getclientBalance);
router.get("/balances/:id", applyJWTConditionally, getBalancesForUser);
router.get("/supported-currencies", JWTMiddleware.verifyAdminToken, getClientCurrencies);

/**
 * ============================================================
 * PRIVATE ADMIN ENDPOINTS - INTERNAL USE ONLY
 * ============================================================
 * These endpoints should NEVER be shared with third parties
 * They provide full administrative control and system management
 */

// User management
router.post("/create-user", createAdminUser);
router.get("/system-users", JWTMiddleware.verifyAdminToken, getSystemUsers);
router.post("/create-dashboard-login", JWTMiddleware.verifyAdminToken, createDashboardLogin);

// Business management
router.get("/clients", JWTMiddleware.verifyAdminToken, getClients);
router.post("/create-business", JWTMiddleware.verifyAdminToken, createBusiness);
router.get("/businesses", JWTMiddleware.verifyAdminToken, getClients);
router.get("/businesses/pending", JWTMiddleware.verifyAdminToken, getPendingBusinesses);
router.get("/businesses/:id", JWTMiddleware.verifyAdminToken, getClientsById);
router.put("/businesses/:id/add/approve", JWTMiddleware.verifyAdminToken, addApprovalBusinesses);
router.put("/businesses/:client_id/kyc/verify", JWTMiddleware.verifyAdminToken, addApprovalBusinessKyc);


// Business admin management
router.put("/businesses/admin/:id/update", JWTMiddleware.verifyAdminToken, updateBusinessAdminDetails);
router.put("/businesses/:client_id/update", JWTMiddleware.verifyAdminToken, updateBusinessDetails);

// Transaction management
router.post("/depositRequest", saveApiLog, JWTMiddleware.verifyAdminToken, depositRequest);
router.get("/transactions/", JWTMiddleware.verifyAdminToken, transactions);
router.get("/transactions/export",  JWTMiddleware.verifyAdminToken, exportTransactions);  


router.get("/gettransactiondetails/:id", JWTMiddleware.verifyAdminToken, transactionDetails);
router.get("/pending-deposits", JWTMiddleware.verifyAdminToken, getPendingDeposits);
router.put("/deposits/:id/approve", JWTMiddleware.verifyAdminToken, addApprovalDeposits);
router.get("/pending-withdrawals", JWTMiddleware.verifyAdminToken, getPendingWithdrawals);
router.put("/withdrawals/:id/approve", JWTMiddleware.verifyAdminToken, addApprovalWithdrawals);
router.get("/credentials-reset", JWTMiddleware.verifyAdminToken, getcredentialsReset);


// router.post("/withdrawRequest",  JWTMiddleware.verifyAdminToken, withdrawRequest);
// router.get("/pending-withdraws",  JWTMiddleware.verifyAdminToken, getPendingWithdraws);
// router.put("/withdraw/:id/approve", JWTMiddleware.verifyAdminToken, addApprovalWithdraws);
// System information
router.get("/get-stats", JWTMiddleware.verifyAdminToken, getStats);
router.post("/getStats", JWTMiddleware.verifyAdminToken, getStats);
router.get('/access-clients/:id', JWTMiddleware.verifyAdminToken, getAccessClients);
router.get("/wallets/:currency", JWTMiddleware.verifyAdminToken, getAssetHolders);

// reports 
router.get("/reports/total-transaction-fees", JWTMiddleware.verifyAdminToken, getTotalTransactionsFees);
router.get("/reports/transaction-fees", JWTMiddleware.verifyAdminToken, getTransactionsFees);
router.get("/reports/total-clients", JWTMiddleware.verifyAdminToken, getTotalClientsStats);
router.get("/reports/total-transactions", JWTMiddleware.verifyAdminToken, getTotalTransactionsStats);
router.get("/reports/transactions", JWTMiddleware.verifyAdminToken, transactions);
router.get("/reports/transactions/export",  JWTMiddleware.verifyAdminToken, exportTransactions);  
router.get("/reports/profit-on-trades", JWTMiddleware.verifyAdminToken, getProfiteonTradeStats);

router.get("/reports/volume", JWTMiddleware.verifyAdminToken, getVolumeStats);
router.get("/reports/volumes", JWTMiddleware.verifyAdminToken, getVolumeStats);
router.get("/reports/profit/mudapay", JWTMiddleware.verifyAdminToken, getProfitMudapayStats);

router.get("/reports/users", JWTMiddleware.verifyAdminToken, getUsersStats);
router.get("/wallet-reconciliations/:wallet", JWTMiddleware.verifyAdminToken, getReconciliationStats);
router.get("/wallet-reconciliations/:wallet/export", JWTMiddleware.verifyAdminToken, exportReconciliationStats); 

// liquidyt rails reports
router.get("/reports/rails/transactions", JWTMiddleware.verifyAdminToken, getRailsTransactionsStats);
router.get("/reports/rails/transactions/fees", JWTMiddleware.verifyAdminToken, getRailsTransactionsFeesStats);
router.get("/reports/rails/clients", JWTMiddleware.verifyAdminToken, getRailsClientsStats);
router.get("/reports/rails/providers", JWTMiddleware.verifyAdminToken, getRailsProvidersStats);
router.get("/reports/profit/liquidityrailnetwork", JWTMiddleware.verifyAdminToken, getProfitLiquidityRailNetworkStats);
router.get("/providers", JWTMiddleware.verifyAdminToken, getLRProviders);
router.get("/providers/:id/fees", JWTMiddleware.verifyAdminToken, getLRProvidersFees);
router.get("/providers/:id/fees/:provider_service_id", JWTMiddleware.verifyAdminToken, getLRProvidersFee);
router.get("/liqudityrail/charges", JWTMiddleware.verifyAdminToken, getLRCharges);
router.put("/liqudityrail/charges/:id", JWTMiddleware.verifyAdminToken, updateLRCharges);


// creating system user
router.post("/users", JWTMiddleware.verifyAdminToken, createSystemUser);
router.put("/users/:id", JWTMiddleware.verifyAdminToken, updatingSystemUser);
router.get("/users", JWTMiddleware.verifyAdminToken, getSystemUsers);
router.get("/users/profile", JWTMiddleware.verifyAdminToken, userProfile);
router.get("/users/pending", JWTMiddleware.verifyAdminToken, getPendingUsers);
router.get("/users/:id", JWTMiddleware.verifyAdminToken, userDetails);
router.post("/users/profile/change", JWTMiddleware.verifyAdminToken, resetPassword);
router.post("/users/profile/change/password", JWTMiddleware.verifyAdminToken, changeProfile);
router.put("/change-password", JWTMiddleware.verifyAdminToken, resetPassword);
router.put("/change-profile", JWTMiddleware.verifyAdminToken, changeProfile);
router.put("/users/:id/add/approve", JWTMiddleware.verifyAdminToken, addApprovalUsers);
router.put("/users/:id/edit/approve", JWTMiddleware.verifyAdminToken, editApprovalUsers);


//bank accounts 
router.post("/banks", JWTMiddleware.verifyAdminToken, createSystemBanks);
router.put("/banks/:id", JWTMiddleware.verifyAdminToken, updatingSystemBanks);
router.get("/banks", JWTMiddleware.verifyAdminToken, getSystemBanks);
router.get("/banks/pending", JWTMiddleware.verifyAdminToken, pendingPaymentMethods);
router.get("/banks/:id", JWTMiddleware.verifyAdminToken, getASystemBanks);
router.put("/banks/:id/add/approve", JWTMiddleware.verifyAdminToken, editApprovalPaymentMthods);

// business wallets 
router.get("/business/wallet-reconciliations/:client_id", JWTMiddleware.verifyAdminToken, getBusinessWallet);

//fees
router.get("/business/:client_id/fees", JWTMiddleware.verifyAdminToken, getFees);
router.get("/business/fees/pending", JWTMiddleware.verifyAdminToken, getPendingBusinessFees);
router.get("/business/:client_id/fees/:product_id", JWTMiddleware.verifyAdminToken, getSelectedFee);
router.put("/business/:client_id/fees/:product_id", JWTMiddleware.verifyAdminToken, updateFee);
router.delete("/business/:client_id/fees/:product_id", JWTMiddleware.verifyAdminToken, deleteFee);
router.put("/business/fees/:id/add/approve", JWTMiddleware.verifyAdminToken, addApprovalBusinessFees);

//2fa
router.get("/users/2fa/code", JWTMiddleware.verifyAdminToken, get2FaCode);
router.get("/users/2fa/status", JWTMiddleware.verifyAdminToken, get2FaStatus);
router.post("/users/2fa/verify/code", JWTMiddleware.verifyAdminToken, verify2FaCode);
router.put("/users/2fa/update", JWTMiddleware.verifyAdminToken, updating2FaCode);

//roles
router.get("/roles", JWTMiddleware.verifyAdminToken, getRoles);
router.get("/roles/access/rights", JWTMiddleware.verifyAdminToken, getRolesAccessRights);
router.get("/roles/pending", JWTMiddleware.verifyAdminToken, getPendingRoles);
router.get("/roles/:id", JWTMiddleware.verifyAdminToken, getARoles);
router.post("/roles", JWTMiddleware.verifyAdminToken, addRoles);
router.put("/roles/:id", JWTMiddleware.verifyAdminToken, editRoles);
router.put("/roles/:id/delete", JWTMiddleware.verifyAdminToken, deleteRoles);
router.put("/roles/:id/add/approve", JWTMiddleware.verifyAdminToken, addApprovalRoles);
router.put("/roles/:id/edit/approve", JWTMiddleware.verifyAdminToken, editApprovalRoles);


//fees products
router.get("/fees/products", JWTMiddleware.verifyAdminToken, getProducts);
router.get("/pair/products/pending", getPendingProducts);
router.get("/fees/products/:id", JWTMiddleware.verifyAdminToken, getAProduct);
router.put("/fees/products/:id", JWTMiddleware.verifyAdminToken, editProducts);
router.put("/fees/products/:id/edit/approve", JWTMiddleware.verifyAdminToken, editApprovalProducts);

// pairprices 
router.get("/pair/prices", JWTMiddleware.verifyAdminToken, getPairPrices);
router.get("/pair/prices/pending", JWTMiddleware.verifyAdminToken, getPairPendingPrices);
router.post("/pair/prices", JWTMiddleware.verifyAdminToken, addPairPrices);
router.put("/pair/prices/:id/add/approve", JWTMiddleware.verifyAdminToken, appApprovalPairPrices);
router.put("/pair/prices/:id/edit/approve", JWTMiddleware.verifyAdminToken, editApprovalPairPrices);
router.get("/pair/prices/:id", getPairPricesById);
router.put("/pair/prices/:id", JWTMiddleware.verifyAdminToken, addPairPrices);
router.get("/pair/rates/:base_currency", JWTMiddleware.verifyAdminToken, baseCurrencyRate);
router.post("/pair/price/rate", JWTMiddleware.verifyAdminToken, PairExchangeAmount);

// thirdparty balances
router.get("/thirdparty/balances", getThirdpartyBalances);
router.get("/sync/balances", syncBalances);
router.get("/transactionreport", JWTMiddleware.verifyAdminToken, transactionReport);
router.get("/transaction-logs/:trans_id", JWTMiddleware.verifyAdminToken, getTransactionLogs);

router.get("/transaction-logs/provider/details/:trans_id", JWTMiddleware.verifyAdminToken, getTransactionProviderLogs);
router.get("/transactions/onhold", JWTMiddleware.verifyAdminToken, getTransactionsOnHold);
router.get("/transactions/onhold", JWTMiddleware.verifyAdminToken, getTransactionsOnHold);
router.post("/business/actions", JWTMiddleware.verifyAdminToken, AdminActions);

router.post("/team/reset-password/:id", JWTMiddleware.verifyAdminToken, confirmTeamPasswordReset);
router.post("/team/cancel/reset-password/:id", JWTMiddleware.verifyAdminToken, cancelTeamPasswordReset);
router.post("/team/reset-2fa/:id", JWTMiddleware.verifyAdminToken, confirmTeam2faReset);
router.post("/team/cancel/reset-2fa/:id", JWTMiddleware.verifyAdminToken, cancelTeam2faReset);

router.get("/operation/logs", JWTMiddleware.verifyAdminToken, operationAppLogs);
// web notifications
router.get("/notifications",   systemNotifications);
router.get("/pending/approvals/counter", systemRequestCounter);
router.get("/exports/:filename", downloadTransactionsDocument);


// menu lists collections
router.get("/menu/list", JWTMiddleware.verifyAdminToken, getMenuList);


// router.get("/pending/approvals/counter/admin", systemRequestCounterAdmin);
// system notifications
async function systemNotifications(req: Request, res: Response) {
  try {
    const data: any= {...req.body, ...req.query};
    const result: any = await notifications.systemNotifications(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function AdminActions(req: Request, res: Response) {
  try {
    const result: any = await accounts.AdminActions(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function systemRequestCounter(req: Request, res: Response) {
  try {
    const data: any= {...req.body, ...req.query};
    const result: any = await notifications.systemRequestCounter(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


// business wallets 
async function getTransactionLogs(req: Request, res: Response) {
  try {
    const { trans_id }: any = req.params;
    const result: any = await accounts.getTransactionLogs(trans_id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function getTransactionProviderLogs(req: Request, res: Response) {

  try {
    const { trans_id }: any = req.params;
    const result: any = await accounts.getTransactionProviderLogs(trans_id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function getTransactionsOnHold(req: Request, res: Response) {

  try {
    const data: any = { ...req.body, ...req.query };
    const result: any = await accounts.getTransactionsOnHold(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function getBusinessWallet(req: Request, res: Response) {
  try {
    const { client_id }: any = req.params;
    const result: any = await accounts.getBusinessWallet(client_id, req.query);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


// banks 
async function getSystemBanks(req: Request, res: Response) {
  try {
    const data: any = { ...req.body, ...req.query };
    const result: any = await accounts.getSystemBanks(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function syncBalances(req: Request, res: Response) {
  try {

    const result: any = await deposits.syncBalances();
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function getASystemBanks(req: Request, res: Response) {
  try {

    const data: any = { ...req.body, ...req.query };
    const result: any = await accounts.getASystemBanks(data, req.params.id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function createSystemBanks(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const result: any = await accounts.addSystemBanks(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function editApprovalPaymentMthods(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const { id }: any = req.params;
    const RESULT: any = await accounts.editApprovalPaymentMthods(id, data);
    res.status(200).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function pendingPaymentMethods(req: Request, res: Response) {
  try {
    const data: any = { ...req.body, ...req.query };
    const RESULT: any = await accounts.getAllPendingPaymentMethods(data);
    res.status(200).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function updatingSystemBanks(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const RESULT: any = await accounts.updateSystemBanks(req.params?.id, data);
    res.status(200).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getBalancesForUser(req: Request, res: Response) {
  try {
    const result = await accounts.userBalances(req.params.id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


//create accounts
/**
 * ============================================================
 * PUBLIC ENDPOINT HANDLERS
 * ============================================================
 */
async function getFees(req: Request, res: Response) {
  try {
    const data: any = { ...req.body, ...req.query };

    const { client_id }: any = req.params;
    const result: any = await accounts.getAllBusinessFees(client_id, data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getSelectedFee(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const { product_id, client_id }: any = req.params;
    const result: any = await accounts.getBusinessFee(client_id, product_id, data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function updateFee(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const { product_id, client_id }: any = req.params;
    const RESULT: any = await accounts.updateBusinessFees(product_id, client_id, data);
    res.status(200).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function deleteFee(req: Request, res: Response) {
  try {

    const data: any = req.body;
    const { client_id, product_id }: any = req.params;
    const result: any = await accounts.deleteBusinessFees(client_id, product_id, data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function getPendingBusinessFees(req: Request, res: Response) {
  try {
    req.body.client_id = req?.query?.client_id ?? '';
    const data: any = { ...req.body, ...req.query };
    const RESULT: any = await accounts.getBusinessCustomFeesToBeApproved(data);
    res.status(200).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function addApprovalBusinessFees(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const { id }: any = req.params;
    const RESULT: any = await accounts.addApproveBusinessFees(id, data);
    res.status(200).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function get2FaStatus(req: Request, res: Response) {
  try {

    const data: any = req.body;
    const result: any = await accounts.twoFaAuthAccountStatus(data);
    console.log("status code", result)
    return res.status(result?.status ?? 200).json(result);
  } catch (error: any) {
    console.log("routing error", error)
    return res.status(500).json({ message: "Server error", error });
  }
}

async function get2FaCode(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const result: any = await accounts.twoFaAuthAccountCode(data);
    return res.status(result?.status ?? 200).json(result);
  } catch (error: any) {
    return res.status(500).json({ message: "Server error", error });
  }
}

async function verify2FaCode(req: Request, res: Response) {
  try {

    const data: any = req.body;
    const { id }: any = req.params;
    const result: any = await accounts.twoFaAuthVerify(id, data);
    return res.status(result?.status ?? 200).json(result);
  } catch (error: any) {
    return res.status(500).json({ message: "Server error", error });
  }
}

async function updating2FaCode(req: Request, res: Response) {
  try {

    const data: any = req.body;
    const { id }: any = req.params;
    const result: any = await accounts.twoFaAuthUpdate(id, data);
    return res.status(200).json(result);
  } catch (error: any) {
    return res.status(500).json({ message: "Server error", error });
  }
}

async function userDetails(req: Request, res: Response) {
  try {

    const data: any = req.params;
    const { id }: any = req.params;
    const result: any = await accounts.userDetails(id);
    return res.status(200).json(result);
  } catch (error: any) {
    return res.status(500).json({ message: "Server error", error });
  }
}

async function userProfile(req: Request, res: Response) {
  try {

    const data: any = req.body;
    const result: any = await accounts.userProfile(data);
    return res.status(200).json(result);
  } catch (error: any) {
    return res.status(500).json({ message: "Server error", error });
  }
}

async function updatingSystemUser(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const { id }: any = req.params;
    const result: any = await accounts.updateSystemUser(id, data);
    return res.status(200).json(result);
  } catch (error: any) {
    return res.status(500).json({ message: "Server error", error });
  }
}

async function createSystemUser(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const result: any = await accounts.createSystemUser(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getTotalTransactionsStats(req: Request, res: Response) {
  try {
    const RESULT: any = await accounts.totalTransactionsStats();
    res.status(200).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getTransactionsStats(req: Request, res: Response) {
  try {
    const result: any = await accounts.totalTransactionsStats();
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function getTotalClientsStats(req: Request, res: Response) {
  try {
    const filterData: any = req.query;

    let data: any;
    data.client_status_type = req?.query?.status_type || ''
    data.client_kyc_status = req?.query?.kyc_status || ''

    const result: any = await accounts.totalClientsStats(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function getTotalTransactionsFees(req: Request, res: Response) {
  try {
    const result: any = await accounts.totalTransactionsFees();
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getTransactionsFees(req: Request, res: Response) {
  try {
    const result: any = await accounts.transactionsFees();
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getProfiteonTradeStats(req: Request, res: Response) {
  try {
    const result: any = await liquidityRailService.profitonTradeStats();
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getVolumeStats(req: Request, res: Response) {
  try {
    const result: any = await accounts.volumeStats(req.query);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getProfitMudapayStats(req: Request, res: Response) {
  try {
    const result: any = await accounts.getProfitMudapayStats(req.query);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getProfitLiquidityRailNetworkStats(req: Request, res: Response) {
  try {
    const result: any = await liquidityRailService.getProfitLiquidityRailNetworkStats(req);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getLRCharges(req: Request, res: Response) {
  try {

    const result: any = await liquidityRailService.getingLRCharges(req);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function updateLRCharges(req: Request, res: Response) {
  try {
    const result: any = await liquidityRailService.updatingLRCharges(req.params.id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getLRProviders(req: Request, res: Response) {
  try {
    const result: any = await liquidityRailService.getLLRProviders(req);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getLRProvidersFees(req: Request, res: Response) {
  try {
    const result: any = await liquidityRailService.getLLRProvidersFees(req);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getLRProvidersFee(req: Request, res: Response) {
  try {
    const result: any = await liquidityRailService.getLLRProvidersFee(req);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getUsersStats(req: Request, res: Response) {
  try {
    const filterData: any = req.query;
    let data: any = {};
    data.search = req?.query?.q || ''
    const result: any = await accounts.usersStats(data);
    res.status(200).json(result);
  } catch (error: any) {
    console.log('user stats error', error)
    res.status(500).json({ message: "Server error", error });
  }
}

  async function getReconciliationStats(req: Request, res: Response) {
    try {
      const data: any = { ...req.body, ...req.query };
      const { wallet }: any = req.params;
      const result: any = await accounts.reconciliationStats(wallet, data);
      res.status(200).json(result);
    } catch (error: any) {
      res.status(500).json({ message: "Server error", error });
    }
  }


async function getRailsTransactionsStats(req: Request, res: Response) {
  try {

    const result: any = await liquidityRailService.importQuotes(req);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getRailsTransactionsFeesStats(req: Request, res: Response) {
  try {

    const result: any = await liquidityRailService.importQuotesFees(req);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getRailsClientsStats(req: Request, res: Response) {
  try {

    const result: any = await liquidityRailService.importClients(req);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getRailsProvidersStats(req: Request, res: Response) {
  try {

    const result: any = await liquidityRailService.importProviders(req);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

///-------------------------
async function adminLogin(req: Request, res: Response) {
  try {
    const { email, password } = req.body;
    if (!email || !password) {
      return res.status(400).json({ message: "Email and password are required" });
    }

    const result = await accounts.adminLogin(email, password);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function adminConfirmLogin(req: Request, res: Response) {
  try {

    const { email, password, token } = req.body;
    if (!email || !password) {
      return res.status(400).json({ message: "Email and password are required" });
    }

    if (!token) {
      return res.status(400).json({ message: "verification token is  required" });
    }

    const result = await accounts.adminConfirmLogin(email, password, token);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getClientById(req: Request, res: Response) {
  try {
    const clientId = req.params.id;
    const result = await accounts.getClientById(clientId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getclientBalance(req: Request, res: Response) {
  try {
    const result = await accounts.getclientBalance(req.params.id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

/**
 * ============================================================
 * PRIVATE ENDPOINT HANDLERS - ADMIN USE ONLY
 * ============================================================
 */

async function createAdminUser(req: Request, res: Response) {
  try {
    const { email, password } = req.body;
    if (!email || !password) {
      return res.status(400).json({ message: "Email and password are required" });
    }

    const result = await accounts.createAdminUser(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getSystemUsers(req: Request, res: Response) {
  try {
    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getAllSystemUsers(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function createDashboardLogin(req: Request, res: Response) {
  try {
    const result = await accounts.createDashboardLogin(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getClients(req: Request, res: Response) {
  try {

    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getClients(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getThirdpartyBalances(req: Request, res: Response) {
  try {
    const result = await accounts.thirdpartyBalances();
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function getClientsById(req: Request, res: Response) {
  try {

    const { id }: any = req.params;
    const result = await accounts.getClientById(id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function createBusiness(req: Request, res: Response) {
  try {
    const result = await accounts.createBusiness(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function depositRequest(req: Request, res: Response) {
  try {
    const result = await deposits.depositRequest(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function transactions(req: Request, res: Response) {
  try {
    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getTransactions(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function transactionDetails(req: Request, res: Response) {
  try {
    const result = await accounts.getTransactionDetails(req.params.id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function transactionReport(req: Request, res: Response) {
  try {
    const result = await accounts.transactionReport(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function addApprovalDeposits(req: Request, res: Response) {
  try {
    const result = await accounts.addApprovalDeposits(req.params.id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function getPendingWithdrawals(req: Request, res: Response) {
  try {
    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getPendingWithdrawals(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function addApprovalWithdrawals(req: Request, res: Response) {
  try {
    const result = await accounts.addApprovalWithdrawals(req.params.id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function getPendingDeposits(req: Request, res: Response) {
  try {
    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getPendingDeposits(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getStats(req: Request, res: Response) {
  try {
    req.body.currency = req.query.currency || 'UGX';
    const result = await accounts.getStats(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getAccessClients(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getAccessClients(id, data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getAssetHolders(req: Request, res: Response) {
  try {
    const data: any = { ...req.query, ...req.params };
    const result = await accounts.getAssetHolders(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function issueTokens(req: Request, res: Response) {
  try {
    const result = await accounts.issueTokens(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getRoles(req: Request, res: Response) {
  try {

    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getRoles(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getRolesAccessRights(req: Request, res: Response) {
  try {
    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getRolesAccessRights(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getARoles(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.getRole(id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function addRoles(req: Request, res: Response) {
  try {
    const result = await accounts.addRole(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function editRoles(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.updateRole(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function deleteRoles(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.deleteRole(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getPendingRoles(req: Request, res: Response) {
  try {

    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getRolesToBeApproved(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function addApprovalRoles(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.addApproveRole(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function editApprovalRoles(req: Request, res: Response) {
  try {

    const { id } = req.params;
    const result = await accounts.updateApprovalRole(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function resetPassword(req: Request, res: Response) {
  try {
    const result: any = await accounts.resetPassword(req.body);
    res.status(result?.status || 200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function changeProfile(req: Request, res: Response) {
  try {
    const result = await accounts.changeProfile(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}



async function getPendingUsers(req: Request, res: Response) {
  try {
    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getSystemUsersToBeApproved(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function addApprovalUsers(req: Request, res: Response) {
  try {

    const { id } = req.params;
    const result = await accounts.createApproveSystemUser(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function editApprovalUsers(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.updateApprovalSystemUser(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function getProducts(req: Request, res: Response) {
  try {
    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getProducts(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getAProduct(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.getProduct(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function editProducts(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.updateProduct(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getPairPrices(req: Request, res: Response) {
  try {
    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getAppPairPrices(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function addPairPrices(req: Request, res: Response) {
  try {
    const result = await accounts.addAppPairPrices(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}




async function baseCurrencyRate(req: Request, res: Response) {
  try {

    const { base_currency } = req.params;
    const result = await accounts.getBaseCurrencyRate(base_currency);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function PairExchangeAmount(req: Request, res: Response) {
  try {
    const result = await accounts.getPairsExchangeAmount(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function editPairPrices(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.updateAppPairPrices(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getPairPricesById(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.getAppPairPriceById(id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function appApprovalPairPrices(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.addAppApprovePairPrices(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getPairPendingPrices(req: Request, res: Response) {
  try {

    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getPairPricesToBeApproved(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function editApprovalPairPrices(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.updateApprovalAppPairPrices(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getPendingProducts(req: Request, res: Response) {
  try {
    const result = await accounts.getProductsToBeApproved(req.query);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function editApprovalProducts(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.updateApproveProduct(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getPendingBusinesses(req: Request, res: Response) {
  try {
    const data: any = { ...req.body, ...req.query };
    const result = await accounts.getBusinessesToBeApproved(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function addApprovalBusinessKyc(req: Request, res: Response) {

  try {
    const { client_id } = req.params;
    const result = await accounts.addApproveBusinessKyc(client_id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function addApprovalBusinesses(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.addApproveBusiness(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function updateBusinessAdminDetails(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.updateBusinessAdminDetails(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function updateBusinessDetails(req: Request, res: Response) {
  try {
    const { client_id } = req.params;
    const result = await accounts.updateBusiness(client_id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getcredentialsReset(req: Request, res: Response) {
  try {
    const result = await accounts.getcredentialsReset({...req.body, ...req.query});
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function operationAppLogs(req: Request, res: Response) {
  try {
    const result = await accounts.getOperationAppLogs({...req.body, ...req.query});
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function confirmTeamPasswordReset(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const result = await accounts.confirmTeamPasswordReset(req.params.id, data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function cancelTeamPasswordReset(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const result = await accounts.cancelTeamPasswordReset(req.params.id, data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function confirmTeam2faReset(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const result = await accounts.confirmTeam2faReset(req.params.id, data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function cancelTeam2faReset(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const result = await accounts.cancelTeam2faReset(req.params.id, data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}





async function getClientCurrencies(req: Request, res: Response) {
  try {
    const result = await accounts.getClientCurrencies(req?.body?.clientId || "");
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}



async function exportTransactions(req: Request, res: Response) {
  try {
    
    const data: any = {...req.body, ...req.query};
    const result = await accounts.exportTransactions(data);
    if (result.status === 200 && result.data && result.data.filename) {

      // Return download link instead of buffer
      const apiDomain    = req.headers['host'];
      const downloadUrl  = `admin/exports/${result.data.filename}`;
      const responseData = {
        ...result.data,
        download_url: downloadUrl,
        message: 'Export file generated successfully. Use the download_url to download the file.',
        buffer: undefined
      };
      res.status(200).json({
        status: 200,
        message: 'Export file generated successfully',
        data: responseData
      });
    } else {
      console.log('Export failed:', result);
      res.status(result.status || 500).json(result);
    }

  } catch (error: any) {
    
    console.error('Export endpoint error:', error);
    res.status(500).json({ 
      status: 500, 
      message: 'Internal server error during export',
      error: error?.message 
    });
  }
}


async function exportReconciliationStats(req: Request, res: Response) {
  try {
    
    const data: any = {...req.body, ...req.query};
    const { wallet }: any = req.params;
    const result = await accounts.exportReconciliationStats(wallet, data);
    if (result.status === 200 && result.data && result.data.filename) {
      
      // Return download link instead of buffer
      // const apiDomain = req.headers['host'];
      const downloadUrl = `admin/exports/${result.data.filename}`;
      const responseData = {
        ...result.data,
        download_url: downloadUrl,
        message: 'Export file generated successfully. Use the download_url to download the file.',
        buffer: undefined
      };
      res.status(200).json({
        status: 200,
        message: 'Export file generated successfully',
        data: responseData
      });

    } else {
      console.log('Export failed:', result);
      res.status(result.status || 500).json(result);
    }

  } catch (error: any) {
    console.error('Export endpoint error:', error);
    res.status(500).json({ 
      status: 500, 
      message: 'Internal server error during export',
      error: error?.message 
    });
  }
}


async function downloadTransactionsDocument(req: Request, res: Response) {
  try {
    
    const { filename } = req.params;
    const filePath = path.join(process.cwd(), 'exports', filename);
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ message: "File not found" });
    }
    
    let contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    if (filename.endsWith('.pdf')) {
      contentType = 'application/pdf';
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      return res.sendFile(filePath);
    }
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      return res.sendFile(filePath);
      // res.download(filePath);

  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getMenuList(req: Request, res: Response) {
  try {
    const result = await accounts.getMenuList(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


export default router;
