{"name": "aggregator-backend-monorepo", "version": "1.0.0", "description": "Aggregator backend monorepo with shared tooling", "private": true, "workspaces": ["wallet", "admin", "gateway", "liquidityRailAdmin", "stellarService", "idp"], "scripts": {"prepare": "husky install", "test": "npm run test:all-services", "test:all-services": "npm run test:wallet && npm run test:admin && npm run test:gateway", "test:wallet": "cd wallet && npm test", "test:admin": "cd admin && npm test || echo 'No tests configured for admin'", "test:gateway": "cd gateway && npm test || echo 'No tests configured for gateway'", "typecheck": "npm run typecheck:all-services", "typecheck:all-services": "npm run typecheck:wallet && npm run typecheck:admin && npm run typecheck:liquidityRailAdmin", "typecheck:wallet": "cd wallet && npx tsc --noEmit --skipLibCheck || echo 'TypeScript check failed for wallet'", "typecheck:admin": "cd admin && npx tsc --noEmit --skipLibCheck || echo 'TypeScript check failed for admin'", "typecheck:gateway": "cd gateway && npx tsc --noEmit --skipLibCheck || echo 'TypeScript check failed for gateway'", "typecheck:liquidityRailAdmin": "cd liquidityRailAdmin && npx tsc --noEmit --skipLibCheck || echo 'TypeScript check failed for liquidityRailAdmin'", "typecheck:stellarService": "cd stellarService && npx tsc --noEmit --skipLibCheck || echo 'TypeScript check failed for stellarService'", "typecheck:idp": "cd idp && npx tsc --noEmit --skipLibCheck || echo 'TypeScript check failed for idp'", "lint": "echo '<PERSON><PERSON> not configured yet'", "install:all": "npm install && npm run install:wallet && npm run install:admin && npm run install:gateway && npm run install:liquidityRailAdmin && npm run install:stellarService && npm run install:idp", "install:wallet": "cd wallet && npm install", "install:admin": "cd admin && npm install", "install:gateway": "cd gateway && npm install", "install:liquidityRailAdmin": "cd liquidityRailAdmin && npm install", "install:stellarService": "cd stellarService && npm install", "install:idp": "cd idp && npm install"}, "devDependencies": {"husky": "^8.0.3", "lint-staged": "^15.2.0"}, "lint-staged": {"*.{ts,tsx}": ["echo 'Checking staged TypeScript files...'"]}, "packageManager": "yarn@4.9.2+sha512.1fc009bc09d13cfd0e19efa44cbfc2b9cf6ca61482725eb35bbc5e257e093ebf4130db6dfe15d604ff4b79efd8e1e8e99b25fa7d0a6197c9f9826358d4d65c3c"}