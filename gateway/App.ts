import express, { Request, Response, NextFunction } from 'express';
import axios from 'axios';
import { JWTMiddleware } from './middlewares/jwt.middleware';
import { limiter } from './middlewares/rateLimiter';
import * as https from 'https';
import jwt from 'jsonwebtoken';
import AWS from 'aws-sdk';
import expressFileUpload from 'express-fileupload';
import bodyParser from 'body-parser';
import { createDecipheriv, randomBytes } from 'crypto';
import cors from 'cors';
import { config } from 'dotenv';

config();

// AWS Configuration
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
});
const s3 = new AWS.S3();

// Constants and Express setup
const JWT_SECRET: any = process.env.JWT_SECRET;
const app = express();
app.use(cors());
app.use(expressFileUpload());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
// app.enable('trust proxy'); // Useful if behind a proxy
app.set('trust proxy', true);

// Environment URLs from .env
const stageUrl = process.env.STAGE_URL || 'http://localhost:3005';
const prodUrlPayments = process.env.PROD_URL_PAYMENTS || 'http://localhost:3006';
const prodUrlWeb = process.env.PROD_URL_WEB || 'http://localhost:3007';
const idpUrl = process.env.IDP_URL || 'http://localhost:3008';
const railUrl = process.env.RAIL_URL || 'http://localhost:3009';
const tradingUrl = process.env.TRADE_URL || 'http://localhost:8039';


// Apply additional middlewares if needed
const applyMiddlewares = () => {
  app.use(express.json());
  // app.use(limiter);
};

const instance = axios.create({
  httpsAgent: new https.Agent({
    rejectUnauthorized: false, // Ignores SSL certificate issues (not recommended for production)
  }),
});

// Allow only GET and POST methods
const allowOnlyGetAndPost = (req: Request, res: Response, next: NextFunction) => {
  const allowedMethods = ['GET', 'POST', 'PUT', 'DELETE'];
  if (allowedMethods.includes(req.method)) {
    next();
  } else {
    res.status(405).send('Method Not Allowed');
  }
};

const getCleanIp = (ip: any): string => {
  try {
    if (ip.includes(',')) ip = ip.split(',')[0];
    if (ip.startsWith('::ffff:')) return ip.replace('::ffff:', '');
    return ip;
  } catch (error) {
    console.log(`error`, error)
    return ""
  }
};




// Conditionally apply JWT middleware except on whitelisted routes
const applyJWTConditionally = (req: Request, res: Response, next: NextFunction) => {
  // Add /login to the exempted list
  const exemptedRoutes = [
    "/v1/wallet/webhook",
    "/stellar/health",
    "/idp/health",
    "/v1/health",
    "/web/health",
    "/idp/*",
    "/login",
    "/auth",
    "/web/admin/login",
    "/v1/clients/oauth/token",
    "/clients/oauth/resetPasswordRequest",
    "/clients/oauth/resetPassword",
    "/v1/payment/webhook-utilia",
    "/v1/payment/webhook/*"
  ];
  console.log(`req.path`, req.path)
  if (!exemptedRoutes.some(route => req.path.includes(route))) {
    JWTMiddleware.verifyToken(req, res, next);
  } else {
    next();
  }
};

// Forward requests to microservices based on API key prefix
const forwardToMicroservice = (servicePath: string) => {
  return async (req: Request, res: Response) => {
    try {
      const ip = getCleanIp(req.ip) || (req.headers['x-forwarded-for'] as string) || ""
      req.body.ip = ip
      console.log(`ip`, ip)
    } catch (error) {
      console.log(`error`, error)
    }

    try {
      // Determine the correct base URL by inspecting the API key header
      let baseUrl = prodUrlPayments; // default to production
      const apiKey = req.body['apiKey'];
      console.log(`apiKey`, apiKey)
      console.log(`Body`, req.body)
      if (servicePath === 'auth') {
        baseUrl = prodUrlWeb;
      } else if (servicePath === 'rail') {
        baseUrl = railUrl;
      } else if (servicePath === 'idp') {
        baseUrl = idpUrl;
      } else if (servicePath === 'api-payments') {
        baseUrl = prodUrlPayments;
      } else if (servicePath === 'web') {
        baseUrl = prodUrlWeb;
      } else if (servicePath === 'web-payments') {
        baseUrl = prodUrlPayments;
      } else if (servicePath === 'trading') {
        baseUrl = tradingUrl;
      } else if (apiKey && typeof apiKey === 'string') {
        if (apiKey.startsWith('live_')) {
          baseUrl = prodUrlPayments;
        } else {
          baseUrl = stageUrl;
        }
      }

      const segments = req.originalUrl.split('/').filter(e => e);
      let newPath = segments.length > 1 ? `/${segments.slice(1).join('/')}` : '';
      let url = `${baseUrl}${newPath}`;


      console.log('url:::::', url)
      if (url.includes("rail")) {
        newPath = segments.length > 2 ? `/${segments.slice(2).join('/')}` : '';
        url = `${railUrl}${newPath}`;
      }


      // Prepare headers and include api-key and saccoid if present
      let headers: any = {
        'content-type': 'application/json',
        accept: '*/*',
      };
      if (apiKey) {
        req.body.api_key = apiKey;
        headers['api-key'] = apiKey;
      }

      const bearer = req.headers.authorization;           // e.g. "Bearer eyJ…"
      if (bearer) headers.Authorization = bearer;


      // Prepare the request options for axios
      const requestOptions = {
        method: req.method,
        url: url,
        headers: headers,
        data: req.body,
      };

      console.log("Forwarding request to:", url, "with options:", requestOptions);
      const response = await instance.request(requestOptions);
      console.log("Response from microservice:", response.data);
      res.status(response.status).send(response.data);
    } catch (error: any) {
      console.error('Error forwarding request:', error);
      if (error.response) {
        res.status(error.response.status).send(error.response.data);
      } else {
        res.status(500).send('Internal Server Error');
      }
    }
  };
};

applyMiddlewares();
app.use(allowOnlyGetAndPost);

app.all('/v1/clients/oauth/*', applyJWTConditionally, forwardToMicroservice('web'));
app.all('/web/clients/oauth/*', applyJWTConditionally, forwardToMicroservice('web'));
app.all('/v1/rail/*', forwardToMicroservice('rail'));
app.all('/v1/payment/*', applyJWTConditionally, forwardToMicroservice('api-payments'));
app.all('/web/payment/*', applyJWTConditionally, forwardToMicroservice('web-payments'));
app.all('/web/trading/*', applyJWTConditionally, forwardToMicroservice('trading'));
app.all('/web/*', applyJWTConditionally, forwardToMicroservice('web'));
app.all('/v1/*', applyJWTConditionally, forwardToMicroservice('api-payments'));
app.all('/idp/*', applyJWTConditionally, forwardToMicroservice('idp'));
app.all('/wallet/*', applyJWTConditionally, forwardToMicroservice('web-payments'));
app.all('/client/*', applyJWTConditionally, forwardToMicroservice('web'));
app.all('/rail/*', applyJWTConditionally, forwardToMicroservice('rail'));
// Start the server
const PORT = process.env.PORT || 3000;

app.use('/health', (req: Request, res: Response) => {
  res.status(200).json({ status: 'ok', service: 'gateway' });
});

app.listen(PORT, () => {
  console.log(`API Gateway listening on port ${PORT}`);
});

export default app;
