-- Add bridge_required and related columns to quotes table
ALTER TABLE `quotes` 
ADD COLUMN `bridge_required` tinyint(1) DEFAULT 0 COMMENT 'Whether this quote requires bridging (0=no, 1=yes)' AFTER `transaction_type`,
ADD COLUMN `bridge_from_chain` varchar(100) NULL COMMENT 'Source chain for bridge operation' AFTER `bridge_required`,
ADD COLUMN `bridge_to_chain` varchar(100) NULL COMMENT 'Destination chain for bridge operation' AFTER `bridge_from_chain`,
ADD COLUMN `bridge_fee` decimal(20,8) NULL COMMENT 'Bridge fee amount' AFTER `bridge_to_chain`,
ADD COLUMN `rhino_quote_id` varchar(100) NULL COMMENT 'Rhino quote ID for bridge operations' AFTER `bridge_fee`;

-- Add indexes for better performance
ALTER TABLE `quotes` 
ADD INDEX `idx_bridge_required` (`bridge_required`),
ADD INDEX `idx_rhino_quote_id` (`rhino_quote_id`);




