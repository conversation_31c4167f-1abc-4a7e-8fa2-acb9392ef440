-- Add bridge information columns to quote_log table
ALTER TABLE `quote_log` 
ADD COLUMN `bridge_required` TINYINT(1) DEFAULT 0 COMMENT 'Whether bridging is required for this quote' AFTER `transactionType`,
ADD COLUMN `bridge_from_chain` VARCHAR(50) NULL COMMENT 'Source chain for bridge' AFTER `bridge_required`,
ADD COLUMN `bridge_to_chain` VARCHAR(50) NULL COMMENT 'Destination chain for bridge' AFTER `bridge_from_chain`,
ADD COLUMN `bridge_fee` DECIMAL(20, 8) NULL COMMENT 'Bridge fee amount' AFTER `bridge_to_chain`,
ADD COLUMN `rhino_quote_id` VARCHAR(255) NULL COMMENT 'Rhino bridge quote ID' AFTER `bridge_fee`;

-- Add indexes for faster lookups
ALTER TABLE `quote_log`
ADD INDEX `idx_bridge_required` (`bridge_required`),
ADD INDEX `idx_rhino_quote_id` (`rhino_quote_id`);

