-- Add crypto swap fields to quote_log table
-- These fields track when a crypto-to-crypto swap is required before off-ramp
-- Example: CNGN → USDC swap via Quidax before final off-ramp to KES

ALTER TABLE `quote_log` 
ADD COLUMN `crypto_swap_required` TINYINT(1) DEFAULT 0 
COMMENT 'Whether a crypto swap is required before off-ramp (1=yes, 0=no)' 
AFTER `rhino_quote_id`;

ALTER TABLE `quote_log` 
ADD COLUMN `swap_from_asset` VARCHAR(50) NULL 
COMMENT 'Original crypto asset (e.g., CNGN)' 
AFTER `crypto_swap_required`;

ALTER TABLE `quote_log` 
ADD COLUMN `swap_to_asset` VARCHAR(50) NULL 
COMMENT 'Intermediate crypto asset after swap (e.g., USDC)' 
AFTER `swap_from_asset`;

ALTER TABLE `quote_log` 
ADD COLUMN `swap_from_amount` DECIMAL(20,8) NULL 
COMMENT 'Amount of original crypto asset' 
AFTER `swap_to_asset`;

ALTER TABLE `quote_log` 
ADD COLUMN `intermediate_asset` VARCHAR(50) NULL 
COMMENT 'Intermediate asset code used for provider quote' 
AFTER `swap_from_amount`;

ALTER TABLE `quote_log` 
ADD COLUMN `intermediate_chain` VARCHAR(50) NULL 
COMMENT 'Intermediate asset chain used for provider quote' 
AFTER `intermediate_asset`;

-- Add index for efficient lookups
ALTER TABLE `quote_log` 
ADD INDEX `idx_crypto_swap_required` (`crypto_swap_required`);

