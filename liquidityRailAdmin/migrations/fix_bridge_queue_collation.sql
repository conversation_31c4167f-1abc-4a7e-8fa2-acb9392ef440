-- Fix collation mismatch in bridge_queue table to match quotes table
-- This migration fixes the collation to match the quotes table (utf8mb4_0900_ai_ci)

-- Drop the table if it exists with wrong collation and recreate it
DROP TABLE IF EXISTS `bridge_queue`;

-- Create bridge_queue table with correct collation matching quotes table
CREATE TABLE `bridge_queue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quote_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'Reference to quotes.transId',
  `smart_deposit_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'Rhino smart deposit address to monitor',
  `deposit_chain` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'Chain where deposit should occur (e.g., ETHEREUM, BINANCE)',
  `status` enum('QUEUED', 'PROCESSING', 'COMPLETED', 'FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'QUEUED' COMMENT 'Queue item status',
  `retry_count` int(11) DEFAULT 0 COMMENT 'Number of processing attempts',
  `last_checked_at` timestamp NULL COMMENT 'Last time this item was processed',
  `reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Reason for current status or last failure',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'When item was added to queue',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `quote_id_unique` (`quote_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_smart_deposit_address` (`smart_deposit_address`),
  KEY `idx_deposit_chain` (`deposit_chain`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Queue for managing bridge quotes that need delayed processing';

-- First, add index to quotes.transId if it doesn't exist (required for foreign key)
ALTER TABLE `quotes` ADD INDEX `idx_transId` (`transId`);

-- Add foreign key constraint
ALTER TABLE `bridge_queue`
ADD CONSTRAINT `fk_bridge_queue_quote`
FOREIGN KEY (`quote_id`) REFERENCES `quotes` (`transId`)
ON DELETE CASCADE ON UPDATE CASCADE;
