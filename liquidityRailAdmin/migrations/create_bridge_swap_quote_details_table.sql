-- Create bridge_swap_quote_details table for storing detailed information about bridge/swap quotes
CREATE TABLE IF NOT EXISTS `bridge_swap_quote_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quote_id` varchar(50) NOT NULL COMMENT 'Reference to quotes.transId',
  `from_chain` varchar(100) NOT NULL COMMENT 'Source blockchain (e.g., ETHEREUM, MATIC_POS)',
  `to_chain` varchar(100) NOT NULL COMMENT 'Destination blockchain (e.g., ETHEREUM, MATIC_POS)',
  `from_token` varchar(50) NOT NULL COMMENT 'Source token symbol (e.g., USDC, USDT)',
  `to_token` varchar(50) NOT NULL COMMENT 'Destination token symbol (e.g., USDC, USDT)',
  `depositor_address` varchar(255) NOT NULL COMMENT 'Address that will send funds to smart deposit',
  `destination_address` varchar(255) NOT NULL COMMENT 'Final recipient address',
  `smart_deposit_address` varchar(255) NULL COMMENT 'Rhino smart deposit address for user to send funds',
  `estimated_duration` int(11) NULL COMMENT 'Estimated duration in minutes',
  `rhino_quote_id` varchar(255) NULL COMMENT 'Rhino quote ID for tracking',
  `operation_type` enum('bridge', 'swap') NOT NULL COMMENT 'Type of operation',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `quote_id_unique` (`quote_id`),
  KEY `idx_quote_id` (`quote_id`),
  KEY `idx_from_chain` (`from_chain`),
  KEY `idx_to_chain` (`to_chain`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_smart_deposit_address` (`smart_deposit_address`),
  KEY `idx_rhino_quote_id` (`rhino_quote_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Detailed information for bridge/swap quotes';

