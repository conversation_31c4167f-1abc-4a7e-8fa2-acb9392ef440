-- Add depositTxHash column to bridge_swap_quote_details table
-- This column will store the transaction hash of the user's deposit transaction
-- Used by the polling service to match exact transactions in Rhino bridge history

ALTER TABLE `bridge_swap_quote_details` 
ADD COLUMN `depositTxHash` VARCHAR(255) NULL 
COMMENT 'Transaction hash of user deposit to smart contract address' 
AFTER `rhino_quote_id`;

-- Add index for efficient lookups by depositTxHash
ALTER TABLE `bridge_swap_quote_details` 
ADD INDEX `idx_deposit_tx_hash` (`depositTxHash`);
