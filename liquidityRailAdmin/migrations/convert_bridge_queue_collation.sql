-- Convert existing bridge_queue table collation to match quotes table
-- This is a safer approach that preserves existing data

-- First, drop the foreign key constraint if it exists
ALTER TABLE `bridge_queue` DROP FOREIGN KEY IF EXISTS `fk_bridge_queue_quote`;

-- Convert table and columns to use utf8mb4_0900_ai_ci collation to match quotes table
ALTER TABLE `bridge_queue` 
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- Explicitly set collation for string columns to ensure consistency
ALTER TABLE `bridge_queue` 
MODIFY COLUMN `quote_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'Reference to quotes.transId',
MODIFY COLUMN `smart_deposit_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'Rhino smart deposit address to monitor',
MODIFY COLUMN `deposit_chain` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'Chain where deposit should occur (e.g., ETHEREUM, BINANCE)',
MODIFY COLUMN `status` enum('QUEUED', 'PROCESSING', 'COMPLETED', 'FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'QUEUED' COMMENT 'Queue item status',
MODIFY COLUMN `reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Reason for current status or last failure';

-- Re-add the foreign key constraint
ALTER TABLE `bridge_queue` 
ADD CONSTRAINT `fk_bridge_queue_quote` 
FOREIGN KEY (`quote_id`) REFERENCES `quotes` (`transId`) 
ON DELETE CASCADE ON UPDATE CASCADE;
