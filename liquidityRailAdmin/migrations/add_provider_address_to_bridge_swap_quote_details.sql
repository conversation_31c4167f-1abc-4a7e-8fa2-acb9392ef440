-- Add provider_address column to bridge_swap_quote_details table
-- This column will store the actual provider off-ramp address that funds will be sent to
-- Used when a bridge is required before off-ramp to track the provider's receiving address

ALTER TABLE `bridge_swap_quote_details` 
ADD COLUMN `provider_address` VARCHAR(255) NULL 
COMMENT 'Provider off-ramp address that receives bridged funds' 
AFTER `smart_deposit_address`;

-- Add index for efficient lookups by provider_address
ALTER TABLE `bridge_swap_quote_details` 
ADD INDEX `idx_provider_address` (`provider_address`);

