-- Create Quidax off-ramp service (service_id 1005) using RAMP API
INSERT INTO services (service_id, service_code, service_name, country, currency, provider_type) 
VALUES (1005, 'QUIDAX_NGN_OFFRAMP', 'Quidax NGN Off-Ramp (RAMP API)', 'NIGERIA', 'NGN', 'bank');

-- Create service provider link for Quidax off-ramp
INSERT INTO service_providers (service_id, provider_id, min_amount, max_amount, fee, block_fee_usdt, fee_type)
SELECT s.service_id, p.provider_id, 1000, ********, 0.00, 0, 'percentage'
FROM providers p, services s 
WHERE p.name = 'Quidax' AND s.service_code = 'QUIDAX_NGN_OFFRAMP';
