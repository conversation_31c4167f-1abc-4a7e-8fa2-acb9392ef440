/**
 * Test MudaProvider - LR Provider Integration
 * Tests communication between liquidityRailAdmin and trading service
 */

import { MudaProvider } from './src/helpers/MudaProvider';
import dotenv from 'dotenv';

dotenv.config();

const provider = new MudaProvider(process.env.MUTA_API_KEY || "test-key");
const PROVIDER_ID = '10'; // Trading service provider

// Test data
let quoteId = '';
let referenceId = '';

async function runTests() {
    console.log('🧪 Testing MudaProvider Integration\n');

    try {
        // TEST 1: Get Quote
        console.log('1️⃣  Getting Quote...');
        const quoteData = {
            amount: 100,
            currency: 'UGX',
            asset_code: 'USDT',
            chain: 'BSC',
            provider_id: 10
        };
        
        const quote = await provider.getQuote(PROVIDER_ID, quoteData);
        if (!quote) throw new Error('Failed to get quote');
        
        console.log(`   ✓ Quote: ${quote.quoteId}`);
        console.log(`   ✓ Rate: ${quote.quotedPrice} | Amount: ${quote.toAmount} ${quote.to}\n`);
        
        quoteId = quote.quoteId;

        // TEST 2: Create Payment Intent
        console.log('2️⃣  Creating Payment Intent...');
        referenceId = `REF_${Date.now()}`;
        
        const paymentData = {
            reference_id: referenceId,
            amount: 100,
            from_currency: 'USDT',
            to_currency: 'UGX',
            chain: 'BSC',
            transaction_type: 'off_ramp' as const,
            payment_method: {
                type: 'mobile_money' as const,
                currency: 'UGX',
                phone_number: '+************',
                country_code: 'UG',
                network: 'MTN',
                account_name: 'Test User'
            }
        };

        const payment = await provider.createPaymentIntent(PROVIDER_ID, paymentData);
        console.log(`   ✓ Reference: ${referenceId}`);
        console.log(`   ✓ Status: ${payment.status}\n`);

        // Wait for DB write
        await new Promise(resolve => setTimeout(resolve, 1000));

        // TEST 3: Get Transaction
        console.log('3️⃣  Getting Transaction...');
        const transaction = await provider.getTransaction(PROVIDER_ID, referenceId);
        console.log(`   ✓ Status: ${transaction.data.status}`);
        console.log(`   ✓ Reference: ${transaction.data.reference_id}\n`);

        // TEST 4: Send Webhook (crypto_received)
        console.log('4️⃣  Sending crypto_received Webhook...');
        const cryptoWebhook = JSON.stringify({
            eventType: 'crypto_received',
            transaction_id: referenceId,
            reference_id: referenceId,
            status: 'SUCCESSFUL',
            data: {
                amount: '100',
                chain: 'BSC',
                asset_code: 'USDT',
                hash: '0xabc123...',
                from_address: '0x1234...',
                to_address: '0x5678...',
                contract_address: '******************************************',
                fee: '0'
            }
        });

        const webhookResult = await provider.sendwebhook(PROVIDER_ID, cryptoWebhook, 'crypto_received');
        console.log(`   ✓ Webhook sent\n`);

        // TEST 5: Send Webhook (fiat_sent)
        console.log('5️⃣  Sending fiat_sent Webhook...');
        const fiatWebhook = JSON.stringify({
            eventType: 'fiat_sent',
            transaction_id: referenceId,
            reference_id: referenceId,
            status: 'SUCCESSFUL',
            data: {
                currency: 'UGX',
                amount: 380000,
                amount_delivered: 380000,
                fee: 0,
                external_reference_id: `MP_${Date.now()}`,
                payment_type: 'mobile_money',
                payment_method_id: '+************'
            }
        });

        const fiatWebhookResult = await provider.sendwebhook(PROVIDER_ID, fiatWebhook, 'fiat_sent');
        console.log(`   ✓ Webhook sent\n`);

        // Summary
        console.log('✅ All tests passed!');
        console.log(`\n📋 Summary:`);
        console.log(`   • Quote ID: ${quoteId}`);
        console.log(`   • Reference: ${referenceId}`);
        console.log(`   • Tests: 5/5 passed\n`);

    } catch (error: any) {
        console.error('\n❌ Test failed:', error.message);
        if (error.response?.data) {
            console.error('Response:', JSON.stringify(error.response.data, null, 2));
        }
        process.exit(1);
    }
}

// Run tests
runTests();

