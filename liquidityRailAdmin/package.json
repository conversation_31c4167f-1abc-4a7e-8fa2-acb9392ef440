{"name": "rail", "version": "1.0.0", "description": "", "main": "app.ts", "scripts": {"start": "ts-node src/app.ts", "test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon --exec ts-node src/app.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/bcrypt": "^6.0.0", "amqplib": "^0.10.9", "aws-sdk": "^2.1536.0", "axios": "^1.6.3", "bcrypt": "^6.0.0", "body-parser": "^1.20.2", "cngn-typescript-library": "^1.0.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.5.1", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mysql2": "^2.2.5", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "nodemailer": "^6.9.8", "redis": "^4.7.0", "request": "^2.88.2", "sequelize": "^6.35.2", "tesseract.js": "^5.0.5", "uuid": "^9.0.1"}, "devDependencies": {"@types/amqplib": "^0.10.7", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-fileupload": "^1.5.1", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.17.46", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@types/request": "^2.48.12", "@types/sequelize": "^4.28.19", "@types/uuid": "^9.0.7", "i": "^0.3.7", "nodemon": "^3.0.2", "npm": "^10.4.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}