import axios, { AxiosRequestHeaders } from "axios";
import { getItem, setItem } from "./connectRedis";

class MUDAPay {
  private mainURL: string;
  private mainRequestHeader: AxiosRequestHeaders;
  private LR_CLIENT_ID: string;
  constructor() {
    this.mainURL = process.env.MUDA_URL || "";
    this.LR_CLIENT_ID = process.env.LR_CLIENT_ID || "";
    // Removed the static Authorization header since JWT is now obtained from Redis
    this.mainRequestHeader = new axios.AxiosHeaders({
      Accept: "application/json",
      "Content-Type": "application/json",
    });

    console.log("mainRequestHeader", this.mainRequestHeader);
  }

  // Helper method to obtain headers with JWT from Redis
  private async getAuthHeaders(): Promise<AxiosRequestHeaders> {
    const token = await getItem("muda_rail_token");
    const headers = new axios.AxiosHeaders(this.mainRequestHeader);
    headers.set('Authorization', token ? `Bearer ${token}` : '');
    return headers;
  }

  // Handle successful responses
  private handleResponse(response: any) {
    return response.data;
  }

  // Handle errors uniformly
  private handleError(error: any, method: string, endpoint: string) {
    console.error(`[${method} ERROR] - ${this.mainURL}${endpoint}`, error);
    throw error;
  }

  // Reusable POST Request Method with JWT handling
  private async postRequest(endpoint: string, data: any) {
    let url = `${this.mainURL}/${endpoint}`;
    if (endpoint.includes("https://")) {
      url = endpoint;
    }
    console.log("postRequest", url);
    let headers = await this.getAuthHeaders();

    console.log(`[POST REQUEST] - ${url}`, { data, headers });

    try {
      const response = await axios.post(url, data, { headers });
      console.log(`[POST RESPONSE] - ${url}`, response.data);
      return this.handleResponse(response);
    } catch (error: any) {
      if (error.response && error.response.status === 401) {
        console.log("Received 402 status code. Fetching new JWT token and retrying...");
        const newToken = await this.getJWT();
        headers.set('Authorization', `Bearer ${newToken}`);

        try {
          const response = await axios.post(url, data, { headers });
          console.log(`[POST RESPONSE - RETRY] - ${url}`, response.data);
          return this.handleResponse(response);
        } catch (retryError) {
          return this.handleError(retryError, "POST (retry)", endpoint);
        }
      }
      return this.handleError(error, "POST", endpoint);
    }
  }

  // Reusable GET Request Method with JWT handling
  private async getRequest(endpoint: string) {
    const url = `${this.mainURL}${endpoint}`;
    let headers = await this.getAuthHeaders();

    console.log(`[GET REQUEST] - ${url}`, { headers });

    try {
      const response = await axios.get(url, { headers });
      console.log(`[GET RESPONSE] - ${url}`, response.data);
      return this.handleResponse(response);
    } catch (error: any) {
      if (error.response && error.response.status === 403) {
        console.log("Received 402 status code. Fetching new JWT token and retrying...");
        const newToken = await this.getJWT();
        headers.set('Authorization', `Bearer ${newToken}`);

        try {
          const response = await axios.get(url, { headers });
          console.log(`[GET RESPONSE - RETRY] - ${url}`, response.data);
          return this.handleResponse(response);
        } catch (retryError) {
          return this.handleError(retryError, "GET (retry)", endpoint);
        }
      }
      return this.handleError(error, "GET", endpoint);
    }
  }

  // Retrieves a transaction by its ID
  public async getTransactionById(transactionId: string) {
    const endpoint = `/payment/transactionReferenceId/${transactionId}`;
    return this.getRequest(endpoint);
  }

  public async makePayout(
    referenceId: string,
    amount: number,
    accountNumber: string
  ) {
    const endpoint = "payment/direct-payout";
    const data = {
      reference_id: referenceId,
      amount,
      trans_type: "PUSH",
      currency: "UGX",
      product_id: 10011,
      account_number: accountNumber.replace("+", ""),
    };
    return this.postRequest(endpoint, JSON.stringify(data));
  }

  async getConversionRate(endpoint: string, data: any) {
    try {
      const { amount, from, to } = data;
      console.log("data date conversion", endpoint, JSON.stringify(data));
      const response = await this.postRequest(endpoint, JSON.stringify(data));
      console.log(`getConversionRate`, response)
      const rate = parseFloat(response.data.rate);
      const convertedAmount = amount * rate;
      console.log(`Exchange rate from ${from} to ${to}: ${rate}`);
      console.log(`Converted amount: ${amount} ${from} = ${convertedAmount} ${to}`);
      return convertedAmount;
    } catch (error) {
      console.error('Error fetching exchange rate:', error);
      return 0
    }
  }

  /**
   * Get rate from trading service (no address generation)
   */
  public async getTradingServiceRate(data: any) {
    try {
      const tradingServiceUrl = process.env.TRADING_SERVICE_URL
      const endpoint = `${tradingServiceUrl}/trading/price`;
      const requestData = {
        baseAsset: data.asset_code || 'USDT',
        counterAsset: data.currency || 'UGX',
        amount: data.amount?.toString() || '100',
        orderType: 'sell',
        slippage: '1',
        clientId: 'RATE_CHECK'
      };
      console.log("requestData", requestData)

      const response = await axios.post(endpoint, requestData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.INTERNAL_API_KEY || 'internal-key'}`
        }
      });
      console.log("response", response.data)
      if (response.data.status === 200) {
        const marketPrice = parseFloat(response.data.data.price || response.data.data.averagePrice || '0');
        const estimatedReceiveAmount = parseFloat(data.amount.toString()) * marketPrice;

        return {
          provider: "trading",
          providerQuoteId: `trading_${Date.now()}`,
          from: data.asset_code,
          providerId: 10,
          to: data.currency,
          fiatAmount: estimatedReceiveAmount,
          toAmount: estimatedReceiveAmount,
          cryptoAmount: data.amount,
          fee: 0,
          quotedPrice: marketPrice,
          expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
        };
      } else {
        throw new Error(response.data.message || 'Failed to get rate from trading service');
      }
    } catch (error: any) {
      console.error('❌ Error calling trading service for rate:', error);
      throw error;
    }
  }

  /**
   * Create pending rail order in trading service (generates deposit address)
   */
  public async generateLRWallerAddress(chain: string, referenceId: string, clientId: string, asset: string, amount: number) {
    const data = { chain: chain, referenceId: referenceId, clientId: this.LR_CLIENT_ID, asset: asset, amount: amount }
    const walletServiceUrl = process.env.WALLET_URL || ''
    const depositEndpoint = `${walletServiceUrl}/payment/generate-deposit-address`;
    try {
      console.log('📡 Calling wallet service for deposit address:', depositEndpoint);

      const response = await axios.post(depositEndpoint, data, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.INTERNAL_API_KEY || 'internal-key'}`
        }
      });

      return response.data;
    } catch (error: any) {
      console.error('❌ Error generating deposit address:', error);
      return { status: 500, message: 'Error generating deposit address', data: null };
    }
  }



  public async createTradingServiceOrder(data: any) {
    try {
      const tradingServiceUrl = process.env.TRADING_SERVICE_URL
      const endpoint = `${tradingServiceUrl}/trading/rail-orders`;

      const response = await axios.post(endpoint, data, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.INTERNAL_API_KEY || 'internal-key'}`
        }
      });

      if (response.data.status === 200) {
        return {
          address: response.data.data.address,
          expiresAt: response.data.data.expiresAt,
          referenceId: response.data.data.referenceId
        };
      } else {
        throw new Error(response.data.message || 'Failed to create trading service order');
      }
    } catch (error: any) {
      console.error('❌ Error creating trading service order:', error);
      throw error;
    }
  }

  // Retrieves a JWT, stores it in Redis, and returns the token
  public async getJWT() {
    console.log("Fetching JWT token...");
    // Ensure the URL is correct (add a slash if necessary)
    const url = `${this.mainURL}/clients/oauth/token`;
    const body = {
      secret_key: process.env.MUDA_SECRET_KEY,
      api_key: process.env.MUDA_API_KEY,
    };

    try {
      const response = await axios.post(url, body, { headers: this.mainRequestHeader });
      const { access_token, expires_in } = response.data.data;

      await setItem("muda_rail_token", access_token);
      console.log("JWT token saved to Redis");

      return access_token;
    } catch (error) {
      console.error("Error fetching JWT token:");
      return null;
    }
  }
}

export default new MUDAPay();
