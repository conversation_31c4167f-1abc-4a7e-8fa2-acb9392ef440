// types/rhinoExact.ts
// Exact types based on Rhino API responses

// Smart Deposit Types (POST /bridge/deposit-addresses)
export interface RhinoSmartDepositRequest {
  depositChains: string[];
  destinationChain: string;
  destinationAddress: string;
  tokenOut?: string;
  reusePolicy?: 'create-new' | 'reuse-existing';
}

export interface RhinoSupportedToken {
  symbol: string;
  address: string;
  maxDepositLimitUsd: number;
  minDepositLimitUsd: number;
}

export interface RhinoSmartDepositResponse {
  depositChain: string;
  depositAddress: string;
  destinationChain: string;
  destinationAddress: string;
  supportedTokens: RhinoSupportedToken[];
  isActive: boolean;
  tokenOut?: string;
}

// Bridge Config Types (GET /bridge/configs)
export interface RhinoBridgeTokenConfig {
  token: string;
  address: string;
  decimals: number;
  maxDepositLimit?: number;
  maxWithdrawLimit?: number;
}

export interface RhinoBridgeChainConfig {
  name: string;
  type: "EVM" | "SOL" | "STK" | "TON" | "TRON";
  networkId: string;
  contractAddress: string;
  multicallContractAddress?: string;
  confirmationBlocks: number;
  avgBlockTime: number;
  nativeTokenName: string;
  nativeTokenDecimals: number;
  nativeTokenSafeguard: number;
  blockExplorer: string;
  rpc: string;
  site: string;
  status: "enabled" | "disabled";
  tokens: Record<string, RhinoBridgeTokenConfig>;
  category?: string;
  gasBoostEnabled: boolean;
  enabledDepositAddress: boolean;
  paradexSignerChainId?: string;
  badge?: {
    type: string;
    translationKey: string;
  };
}

export interface RhinoBridgeConfigResponse {
  [chainId: string]: RhinoBridgeChainConfig;
}

// Swap Config Types (GET /swap/configs)
export interface RhinoSwapTokenConfig {
  chain: string;
  tokenAddress: string;
  name: string;
  symbol: string;
  decimals: number;
  iconUrl: string;
  aggregators: string[];
  status: {
    state: "LISTED" | "DELISTED";
    reason: string[];
  };
}

export interface RhinoSwapConfigResponse {
  [chainId: string]: RhinoSwapTokenConfig[];
}

// Authentication Types
export interface RhinoAuthRequest {
  apiKey: string;
}

export interface RhinoAuthResponse {
  jwt: string;
  userId: string;
}

// Bridge Quote Types (POST /bridge/quote)
export interface RhinoBridgeQuoteRequest {
  fromChain: string;
  toChain: string;
  token: string;
  amount: string;
  mode: "pay" | "receive";
  recipient?: string;
  type: "bridge";
}

export interface RhinoBridgeQuoteResponse {
  id: string;
  fromChain: string;
  toChain: string;
  token: string;
  amount: string;
  receiveAmount: string;
  gasEstimate: string;
  timeEstimate: string;
  fees: RhinoFee[];
  expiresAt: number;
  mode: "pay" | "receive";
}

// Swap Quote Types (POST /swap/quote) 
export interface RhinoSwapQuoteRequest {
  fromChain: string;
  toChain: string;
  tokenIn: string;
  tokenOut: string;
  amount: string;
  mode: "pay";  // Only pay mode supported for swaps
  recipient?: string;
  slippage?: number;
  type: "bridgeSwap";
}

export interface RhinoSwapQuoteResponse {
  id: string;
  fromChain: string;
  toChain: string;
  tokenIn: string;
  tokenOut: string;
  amount: string;
  receiveAmount: string;
  minReceiveAmount: string;
  bridgePayAmount: string;
  bridgePayAmountUsd: string;
  minReceiveAmountUsd: string;
  receiveAmountUsd: string;
  payAmountUsd: string;
  gasEstimate: string;
  gasEstimateUsd: string;
  timeEstimate: string;
  fees: RhinoFee[];
  expiresAt: number;
  slippage: number;
  usdPriceTokenIn: string;
  usdPriceTokenOut: string;
}

export interface RhinoFee {
  type: "network" | "protocol" | "partner";
  amount: string;
  amountUsd?: string;
  description: string;
}

// Union types for requests and responses
export type RhinoQuoteRequest = RhinoBridgeQuoteRequest | RhinoSwapQuoteRequest;
export type RhinoQuoteResponse = RhinoBridgeQuoteResponse | RhinoSwapQuoteResponse;

// Commit Types (POST /bridge/commit)
export interface RhinoCommitRequest {
  quoteId: string;
  recipient: string;
}

export interface RhinoCommitResponse {
  quoteId: string;
  depositAddress?: string;
  transactionData?: {
    to: string;
    data: string;
    value: string;
    gasLimit: string;
  };
  expiresAt: number;
}

// Status Types (GET /bridge/status/{quoteId})
export interface RhinoStatusResponse {
  quoteId: string;
  status: "PENDING" | "ACCEPTED" | "EXECUTED" | "CANCELLED";
  fromChain: string;
  toChain: string;
  fromTxHash?: string;
  toTxHash?: string;
  createdAt: number;
  updatedAt: number;
  completedAt?: number;
  errorMessage?: string;
  refund?: {
    chain: string;
    token: string;
    amount: string;
    txHash: string;
  };
}

// Request Detection Helpers
export function isSwapRequest(request: any): request is RhinoSwapQuoteRequest {
  return request.type === "bridgeSwap" ||
         (request.tokenIn && request.tokenOut && request.fromChain !== request.toChain);
}

export function isBridgeRequest(request: any): request is RhinoBridgeQuoteRequest {
  return request.type === "bridge" ||
         (request.token && !request.tokenIn && !request.tokenOut);
}

// Unified Request Interface for Frontend
export interface UnifiedBridgeSwapRequest {
  // Common fields
  fromChain: string;
  toChain: string;
  amount: string;
  recipient: string;
  depositor?: string; // Optional: Address that will send funds to smart deposit

  // Bridge-specific (when fromToken === toToken)
  token?: string;
  mode?: "pay" | "receive";

  // Swap-specific (when fromToken !== toToken)
  fromToken?: string;
  toToken?: string;
  slippage?: number;

  // Internal field to force operation type
  operationType?: "bridge" | "swap" | "auto";
}

// Unified Response Interface for Frontend
export interface UnifiedBridgeSwapResponse {
  id: string;
  fromChain: string;
  toChain: string;
  operationType: "bridge" | "swap";

  // Input details
  inputToken: string;
  inputAmount: string;
  inputAmountUsd?: string;

  // Output details
  outputToken: string;
  outputAmount: string;
  minOutputAmount?: string;
  outputAmountUsd?: string;

  // Additional details
  gasEstimate: string;
  gasEstimateUsd?: string;
  timeEstimate: string;
  fees: RhinoFee[];
  expiresAt: number;
  expiresIn: number;

  // Swap-specific
  slippage?: number;
  priceImpact?: string;
}

// Error Types
export interface RhinoErrorResponse {
  error: {
    code: string;
    message: string;
    details?: any;
  };
}


export const RHINO_CHAINS = {
  // EVM Chains
  ETHEREUM: 'ETHEREUM',
  ARBITRUM: 'ARBITRUM',
  OPTIMISM: 'OPTIMISM',
  BASE: 'BASE',
  AVALANCHE: 'AVALANCHE',
  BINANCE: 'BINANCE', // BSC
  MATIC_POS: 'MATIC_POS', // Polygon
  ZKSYNC: 'ZKSYNC',
  LINEA: 'LINEA',
  SCROLL: 'SCROLL',
  MANTLE: 'MANTLE',
  CELO: 'CELO',
  GNOSIS: 'GNOSIS',
  INK: 'INK',
  KAIA: 'KAIA',
  KATANA: 'KATANA',
  MORPH: 'MORPH',
  OPBNB: 'OPBNB',
  PLUME2: 'PLUME2',
  SONIC: 'SONIC',
  TAIKO: 'TAIKO',
  UNICHAIN: 'UNICHAIN',

  // Non-EVM Chains
  SOLANA: 'SOLANA',
  STARKNET: 'STARKNET',
  TON: 'TON',
  TRON: 'TRON',
  PARADEX: 'PARADEX',

  // Currently Disabled Chains (status: "disabled")
  ABSTRACT: 'ABSTRACT',
  BERACHAIN: 'BERACHAIN',
  BLAST: 'BLAST',
  EVEDEX: 'EVEDEX',
  MANTA: 'MANTA',
  MODE: 'MODE',
  SONEIUM: 'SONEIUM',
  STORY: 'STORY',
  XLAYER: 'XLAYER',
  ZIRCUIT: 'ZIRCUIT',
  ZKEVM: 'ZKEVM',
} as const;

// Mapping from our internal chain names to Rhino chain identifiers
export const CHAIN_NAME_MAPPING: Record<string, string> = {
  'ethereum': RHINO_CHAINS.ETHEREUM,
  'polygon': RHINO_CHAINS.MATIC_POS,
  'arbitrum': RHINO_CHAINS.ARBITRUM,
  'optimism': RHINO_CHAINS.OPTIMISM,
  'base': RHINO_CHAINS.BASE,
  'avalanche': RHINO_CHAINS.AVALANCHE,
  'bsc': RHINO_CHAINS.BINANCE,
  'binance': RHINO_CHAINS.BINANCE,
  'zksync': RHINO_CHAINS.ZKSYNC,
  'linea': RHINO_CHAINS.LINEA,
  'scroll': RHINO_CHAINS.SCROLL,
  'mantle': RHINO_CHAINS.MANTLE,
  'celo': RHINO_CHAINS.CELO,
  'gnosis': RHINO_CHAINS.GNOSIS,
  'solana': RHINO_CHAINS.SOLANA,
  'starknet': RHINO_CHAINS.STARKNET,
  'ton': RHINO_CHAINS.TON,
  'tron': RHINO_CHAINS.TRON,
};

/**
 * Convert internal chain name to Rhino chain identifier
 */
export function mapToRhinoChain(chainName: string): string {
  const normalizedChain = chainName.toLowerCase();
  const rhinoChain = CHAIN_NAME_MAPPING[normalizedChain];

  if (!rhinoChain) {
    throw new Error(`Unsupported chain for Rhino: ${chainName}. Supported chains: ${Object.keys(CHAIN_NAME_MAPPING).join(', ')}`);
  }

  return rhinoChain;
}