import BaseModel from "./base.model";
import { get } from "./httpRequest";
import { v4 as uuidv4 } from 'uuid';
import EmailSender from './email.helper'
import Flutterwave from "./Flutterwave";
import axios from 'axios';
import CNGNUtility from "./CNGNUtility";
import { Network } from "cngn-typescript-library";
import Quidax from "./Quidax";
import HoneyCoin from "./HoneyCoin"
import { RateResponse } from "../interfaces/rate.interfaces";
import MudaPayment from "./MudaPayment";

const flw = new Flutterwave();
const mailer = new EmailSender()
const honeyCoin = new HoneyCoin();

export default class Model extends BaseModel {


    saveApiLog(body: any, ip: string) {
        const clientId = body.clientId || "NA"
        const userId = body.userId || "NA"
        this.insertData("api_logs", { client_id: clientId, user_id: userId, body: JSON.stringify(body), ip: ip })
        return true;
    }

    async getPaymentMethodById(payId: string) {
        return await this.selectDataQuerySafe("payment_methods", { payment_method_id: payId });
    }

    async generateSendAmount(send_asset: string, send_amount: string) {
        try {

            // Check if transaction with same amount and asset exists with PENDING status
            const existingTransaction: any = await this.callQuerySafe(
                "SELECT * FROM quotes WHERE send_asset = ? AND send_amount = ? AND status = 'PENDING'",
                [send_asset, send_amount]
            );

            if (existingTransaction.length > 0) {
                // Generate two random values between 0.0001 and 0.0009
                const randomValue1 = (Math.random() * 0.08 + 0.01).toFixed(3);
                const randomValue2 = (Math.random() * 0.08 + 0.01).toFixed(3);

                // Add random values to original amount
                const newAmount = (parseFloat(send_amount) + parseFloat(randomValue1) + parseFloat(randomValue2)).toFixed(4);
                return newAmount;
            }

            return send_amount;
        } catch (error) {
            console.error("Error in generateSendAmount:", error);
            return send_amount;
        }
    };


    async getConversionRate(amount: any, from: string, to: string = 'USD', provider_id: string) {
        const provider: any = await this.callQuerySafe("SELECT * FROM providers WHERE provider_id = ?", [provider_id])

        if (provider.length == 0) {
            return 0
        }

        const url = provider[0].rates_endpoint

        if (from.toUpperCase() == "CNGN") {
            from = "NGN"
        }
        const requestData = {
            amount: amount,
            from: from,
            to: to,
        };

        console.log(`requestData`, requestData)

     return await MudaPayment.getConversionRate(url, requestData);
    }


    async getConversionRateOld(amount: any, symbol: string, convertTo = 'USD') {

        const COINMARKETCAP_API_URL = 'https://pro-api.coinmarketcap.com/v1/tools/price-conversion';
        const COINMARKETCAP_API_KEY = '5dfb4f46-f6d4-436a-9448-fbd41cc16ab1';
        try {
            const response = await axios.get(COINMARKETCAP_API_URL, {
                headers: {
                    'X-CMC_PRO_API_KEY': COINMARKETCAP_API_KEY
                },
                params: {
                    amount,
                    symbol,
                    convert: convertTo
                }
            });

            // Assuming the API returns data in a specific format - you might need to adjust the path based on the actual response
            const conversionRate = response.data.data.quote[convertTo].price;
            console.log(`Conversion Rate: ${conversionRate}`);
            return conversionRate;
        } catch (error: any) {
            console.error('Error fetching conversion rate:', error.message);
            throw error;
        }
    }


    async searchprovider(service_id: string, to_currency: string) {
        const query = `select * from service_providers p inner join  services  s on p.service_id = s.service_id where s.service_id=? OR  (s.service_code=? AND s.currency=?)`
        console.log(`query1234`, query)
        return await this.callQuerySafe(query, [service_id, service_id, to_currency])
    }

    async validateProviderCurrency(provider_id: string, to_currency: string) {
        const query = `select * from service_providers p inner join  services  s on p.service_id = s.service_id where p.provider_id=? AND s.currency=?`
        console.log(`query1234`, query)
        return await this.callQuerySafe(query, [provider_id, to_currency])
    }

    public async LogOperation(quote_id: string, provider: string, action: string, data: any) {
        await this.insertData('quote_audit_log', {
            quote_id,
            provider,
            action,
            data: typeof data === 'object' ? JSON.stringify(data) : data
        })
    }

    async getprovider(id: string, service_id: string) {

        return await this.callQuerySafe(`select * from service_providers p inner join  provider_addresses a on p.provider_id = a.provider_id where p.provider_id=? AND  p.service_id=?`, [id, service_id])
        // return await this.callQuery(`select * from providers p inner join  provider_addresses a on p.provider_id = a.provider_id INNER join services s ON p.provider_id = s.company_id where p.provider_id='${id}' AND  s.service_id='${service_id}'`)
        // return await this.callQuery(`select * from providers p inner join  provider_addresses a on p.provider_id = a.provider_id where id='${id}'`)
    }

    makeResponse(status: number, message: string, data: any = null) {
        return {
            status,
            message,
            data: data !== null ? data : undefined
        };
    }

    getRandomString() {
        const uuid = uuidv4();
        return uuid.replace(/-/g, '');
    }
    async getDocumentTypes() {
        return await this.selectDataQuerySafe("document_type", {});
    }

    async getDocumentType(docId: string) {
        return await this.selectDataQuerySafe("document_type", { doc_code: docId });
    }



    async getDefaultDocumentTypes() {
        return await this.selectDataQuerySafe("document_type", { is_default: 'yes' });
    }


    async getUserCompanies(user_id: string) {
        return await this.callRawQuery(`SELECT * FROM company_members m INNER JOIN company c ON m.company_id = c.company_id where m.user_id ='${user_id}' `);
    }

    async getUserCompany(company_id: string, user_id: string) {
        return await this.callRawQuery(`SELECT * FROM company_members m INNER JOIN company c ON m.company_id = c.company_id where  m.company_id = '${company_id}' AND m.user_id ='${user_id}' `);
    }



    async getUserByEmail(email: string) {
        return await this.callRawQuery(`SELECT * FROM company_accounts WHERE email = ?`, [email]);
    }

    async getUserById(userId: string) {
        return await this.callRawQuery(`SELECT * FROM company_accounts where user_id='${userId}' `);
    }

    async getLoggedInUser(staff_id: string, password: string) {
        return await this.callRawQuery(`SELECT * FROM users u inner join company_members c ON u.user_id = c.user_id where staff_id = '${staff_id}' AND u.password='${password}' `);
    }

    async getDocument(doc_id: string) {
        return await this.callRawQuery(`SELECT * FROM documents d INNER JOIN company c ON d.owner_id = c.company_id INNER JOIN document_type t ON d.doc_type= t.doc_code where d.doc_id = '${doc_id}'`);
    }


    async ShareLog(request_id: string) {
        return await this.callRawQuery(`SELECT * FROM document_share_log_docs s INNER JOIN documents d ON s.doc_id=d.doc_id INNER JOIN company c ON d.owner_id = c.company_id INNER JOIN document_type t ON d.doc_type= t.doc_code where s.request_id = '${request_id}'`);
    }



    async CompanysharedDocuments(company_id: string, company_rec_id: any) {
        const rs = await this.callRawQuery(`SELECT * FROM document_share_log_docs d inner join document_share_log v ON d.request_id=v.request_id where (v.sender_id = '${company_id}' AND v.receiver_id = '${company_rec_id}') OR ( v.sender_id = '${company_rec_id}' AND v.receiver_id = '${company_id}')   `);
        const response = []
        for (let i = 0; i < rs.length; i++) {
            const docId = rs[i]['doc_id']
            const docInfo = await this.getDocument(docId)
            const rcDoc = docInfo[0];
            const rsp = {
                ...rs[i],
                ...rcDoc
            }
            response.push(rsp)
        }
        return response
    }


    async requestLog(request_id: string) {
        // Fetch the log documents based on the request_id
        const rs = await this.callRawQuery(`SELECT * FROM document_share_log_docs s INNER JOIN document_type d ON s.doc_type_id=d.doc_code where s.request_id = '${request_id}'`);
        const sender = await this.getShareSender(request_id);
        const senderInfo = sender[0] || {}; // Ensure there's a fallback to prevent errors if no sender is found
        const response = rs.map(element => {
            const obj = { ...element, ...senderInfo };
            return obj;
        });

        return response;
    }


    async getShareSender(request_id: string) {
        return await this.callRawQuery(`SELECT * FROM document_share_log v  INNER JOIN company c on  v.sender_id  = c.company_id where v.request_id = '${request_id}' `);
    }


    async getInformationUser(staff_id: string) {
        return await this.callRawQuery(`SELECT * FROM users u INNER JOIN company_members c ON u.user_id = c.user_id INNER JOIN company y ON c.company_id=y.company_id  where c.staff_id = '${staff_id}'`);
    }

    async verificationRequests(owner_id: string) {
        return await this.callRawQuery(`SELECT *, count(*) as number_of_documents FROM verification_requests v INNER JOIN documents d ON v.doc_id=d.doc_id INNER JOIN company c ON d.owner_id = c.company_id INNER JOIN document_type t ON d.doc_type= t.doc_code where d.owner_id = '${owner_id}' AND v.status='pendingVerification' group by v.request_id`);
    }


    async DocumentsIVerifiy(owner_id: string) {
        return await this.callRawQuery(`SELECT * FROM verifiers v INNER JOIN document_type t ON v.doc_id= t.doc_code INNER JOIN countries c ON v.country_id = c.id where v.company_id = '${owner_id}' AND v.status !='deleted'`);
    }


    async approvalRequests(owner_id: string, recId: string) {
        return await this.callRawQuery(`SELECT *, count(*) as number_of_documents FROM document_share_log v INNER JOIN documents d ON v.doc_id=d.doc_id INNER JOIN company c ON d.owner_id = c.company_id INNER JOIN document_type t ON d.doc_type= t.doc_code where v.receiver_id = '${owner_id}' OR  v.sender_id = '${owner_id}' OR  v.receiver_email='${recId}' group by v.request_id`);
    }


    async getCompanyProfile(company_id: string) {

        return await this.callRawQuery(`SELECT * from company c INNER JOIN users u ON c.contact_user_id = u.user_id  INNER JOIN countries t ON c.country_code=t.id WHERE company_id = '${company_id}' OR domain='${company_id}' `);
    }






    async SingleRequest(request_id: string) {
        return await this.callRawQuery(`SELECT * FROM verification_requests v INNER JOIN documents d ON v.doc_id=d.doc_id INNER JOIN company c ON d.owner_id = c.company_id INNER JOIN document_type t ON d.doc_type= t.doc_code where v.request_id = '${request_id}'`);
    }






    async convertUsdToCurrency(usdAmount: any, targetCurrency: any) {
        console.log("SWAPPER", usdAmount, targetCurrency)
        const ratesObj = await this.selectDataQuerySafe("rate_cache", {});
        const rate_object = ratesObj[0]['rate_object']
        const ratesJSON = JSON.parse(rate_object)
        const rates = ratesJSON.rates;
        // console.log("RATESINFO", rates)

        const rate = rates[targetCurrency];
        console.log("SWAPPER==>2", rate)
        let rateAmount = 0;

        if (!rate) {
            console.error("Invalid currency code or rate not available.");
            // return 0;
        } else {
            usdAmount * rate;
        }
        rateAmount = usdAmount * rate;
        console.log("SWAPPER==>3", rateAmount)
        return rateAmount;

    }

    async sendEmail(operation: string, email: string, name = "", otp = "", tableData: any = [], code: string = '') {
        try {
            const messageBody = await this.selectDataQuerySafe("notification_templates", { operation });
            if (messageBody.length == 0) {
                return this.makeResponse(404, "operation not found");
            }

            // Start of the unordered list
            let listHtml = "<ul>";
            // Assuming tableData is an array of objects
            tableData.forEach((item: any) => {
                listHtml += `<li>${item}</li>`;
            });
            listHtml += "</ul>";

            const message = messageBody[0]['body'];
            const subject = messageBody[0]['title'];

            const new_message = this.constructSMSMessage(message, name, otp, listHtml, code);
            mailer.sendMail(email, subject, subject, new_message);
            this.saveNotification(subject, email, message);
            return true;

        } catch (error) {
            return this.makeResponse(203, "Error fetching company");
        }
    }




    constructSMSMessage(template: string, name: string, otp: string, listHtml: any, code: string): string {
        const data: any = {
            name,
            otp,
            code,
            listHtml
        };

        for (const key in data) {
            if (Object.prototype.hasOwnProperty.call(data, key)) {
                template = template.replace(new RegExp(`{${key}}`, 'g'), data[key]);
            }
        }

        return template;
    }


    async fetchCompanyById(companyId: string) {
        return await this.selectDataQuerySafe("company", { company_id: companyId });

    }

    async getCompanyById(companyId: string) {

        try {
            const companies = await this.selectDataQuerySafe("company", { company_id: companyId });
            if (companies.length > 0) {
                return this.makeResponse(100, "Company fetched successfully", companies[0]);
            } else {
                return this.makeResponse(404, "Company not found");
            }
        } catch (error) {
            console.error("Error in getCompanyById:", error);
            return this.makeResponse(203, "Error fetching company");
        }
    }
    async getUserByStaffId(staffId: string, companyId: string) {
        const companies: any = await this.callQuerySafe(`select * from company_members m INNER JOIN company c ON m.company_id = c.company_id INNER JOIN users u ON m.user_id = u.user_id where m.staff_id = ? AND m.company_id = ?`, [staffId, companyId]);
        return companies;
    }

    generateRandom4DigitNumber() {
        const min = 100000;
        const max = 999999;

        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    // Utility function to validate if the domain is valid
    validateDomain(domain: string) {
        // Regular expression for validating a basic domain format (e.g., example.com)
        // This regex will check for a general pattern like "example.com", without protocols, subdomains, or paths
        const domainRegex = /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

        // Clean the domain by removing protocols, www, and paths
        let cleanDomain = domain.replace(/^(?:https?:\/\/)?(?:www\.)?/, '').split('/')[0];

        // Validate the cleaned domain against the regex
        return domainRegex.test(cleanDomain);
    }


    validateAndCleanDomain(domain: string) {
        let cleanDomain = domain.replace(/^(?:https?:\/\/)?(?:www\.)?/, '').split('/')[0];
        return cleanDomain;
    }

    // Utility function to check if the email's domain matches the company's domain
    doesEmailDomainMatch(email: any, domain: any) {
        const emailDomain = email.split('@')[1];
        return emailDomain === domain;
    }
    async getDocumentByCompanyId(companyId: any, docId: string) {
        return await this.callRawQuery(`SELECT * FROM documents where owner_id = '${companyId}' AND doc_type='${docId}' AND doc_status!='expired' `);
    }

    async getOTP(email: string, user_id: string, otpType: string = 'otp') {
        const user: any = await this.selectDataQuerySafe("user_otp", { user_id });
        let otp = this.generateRandom4DigitNumber().toString();

        if (otpType == 'code') {
            otp = this.getRandomString();
        }
        const userInfo = {
            user_id,
            email,
            otp
        }
        if (user.length == 0) {
            const insertedUser = await this.insertData('user_otp', userInfo);
        } else {
            await this.updateData('user_otp', `user_id = '${user_id}'`, userInfo);

        }
        return otp
    }



    async AddConnection(company_id: string, rec_company_id: string) {
        const userInfo = {
            company_a_id: company_id,
            company_b_id: rec_company_id,
        }
        const insertedUser = await this.insertData('connections', userInfo);

        return true
    }


    async createWallet(company_id: string, currency: string = 'USD') {
        const walletExists = await this.selectDataQuerySafe("company_wallets", { company_id });
        if (walletExists.length > 0) {
            return walletExists[0]['wallet_id'];
        }

        const wallet_id = this.generateRandom4DigitNumber().toString();
        const userInfo = {
            wallet_id,
            company_id,
            currency,
            running_balance: 0,
        }
        await this.insertData('company_wallets', userInfo);
        return wallet_id
    }



    async updateWalletBalance(drWallet: string, crWallet: string, amount: string, opType: string = 'transfer') {
        // Convert amount to a number for calculations
        const transferAmount = parseFloat(amount);
        if (isNaN(transferAmount) || transferAmount <= 0) {
            return "Invalid transfer amount.";
        }

        let drOldBalance = 0;
        if (opType == 'transfer') {
            // Check if the debit wallet exists and has sufficient balance
            const drWalletExists = await this.selectDataQuerySafe("company_wallets", { wallet_id: drWallet });
            if (drWalletExists.length === 0) {
                return "Debit wallet not found.";
            }
            drOldBalance = parseFloat(drWalletExists[0]['running_balance']);
            if (drOldBalance < transferAmount) {
                return "Insufficient funds in debit wallet.";
            }
        }

        // Check if the credit wallet exists
        const crWalletExists = await this.selectDataQuerySafe("company_wallets", { wallet_id: crWallet });
        if (crWalletExists.length === 0) {
            return "Credit wallet not found.";
        }
        const crOldBalance = parseFloat(crWalletExists[0]['running_balance']);

        // Calculate new balances
        const crNewBalance = crOldBalance + transferAmount;

        if (opType == 'transfer') {
            const drNewBalance = drOldBalance - transferAmount;
            await this.updateData('company_wallets', `wallet_id = '${drWallet}'`, { running_balance: drNewBalance.toString() });
        }
        await this.updateData('company_wallets', `wallet_id = '${crWallet}'`, { running_balance: crNewBalance.toString() });

        return "success";
    }






    async createPendingTransaction(company_id: any, user_id: string, payment_method_id: any, usd_amount: any, amount: any, currency: string, description: any, reference_id: any) {
        try {




            const name = "RAIL"
            const email = "<EMAIL>"
            const phone = ""

            const rsp: any = await flw.InitiateTransactionRequest(reference_id, amount, email, phone, name, currency);
            console.log("FlutterwaveTransactions", rsp)
            const rspData = JSON.parse(rsp);
            let respMessage = rspData['status'];

            if (respMessage == "success") {
                const hosted_link = rspData['data']['link'];
                respMessage = hosted_link;


                const newTransaction = {
                    company_id,
                    payment_link: hosted_link,
                    amount,
                    user_id,
                    currency,
                    usd_amount,
                    status: 'pending',
                    description,
                    reference_id
                };

                const insertedTransaction = await this.insertData('transactions', newTransaction);
                return this.makeResponse(100, "Pending transaction created successfully", newTransaction);

            } else {
                return this.makeResponse(203, "Pending creation Failed", []);

            }

        } catch (error) {
            console.error("Error in createPendingTransaction:", error);
            return this.makeResponse(203, "Error creating transaction");
        }
    }

    async countries() {
        const rs: any = await this.callQuerySafe(`select * from countries `, []);
        return rs;
    }
    async getCountryById(id: any) {
        const rs: any = await this.callQuerySafe(`select * from countries where id=? `, [id]);
        return rs;
    }




    // Function to update transaction status based on webhook confirmation
    async updateTransactionStatus(transaction_id: string, status: string) {
        try {
            if (!['completed', 'failed'].includes(status)) {
                return this.makeResponse(400, "Invalid status value");
            }

            // Check if transaction_id exists
            const transactionExists = await this.selectDataQuerySafe("transactions", { transaction_id });
            if (transactionExists.length === 0) {
                return this.makeResponse(404, "Transaction not found");
            }

            await this.updateData('transactions', `transaction_id = '${transaction_id}'`, { status: status });
            return this.makeResponse(100, "Transaction status updated successfully");
        } catch (error) {
            console.error("Error in updateTransactionStatus:", error);
            return this.makeResponse(203, "Error updating transaction status");
        }


    }

    async getchain(send_asset: string) {
        const chainnfo = await this.selectDataQuerySafe(`accepted_assets`, { asset_code: send_asset })
        if (chainnfo.length == 0) {
            return ""
        }

        return chainnfo[0]['chain']
    }



    async getMudaAddress(chain: string) {
        const mudaAddress: any = await this.callQuerySafe(`select * from muda_addresses where chain=?`, [chain])
        if (mudaAddress.length > 0) {
            return mudaAddress[0].address
        }
        return false
    }


    async generate_address(provider_id: string, send_asset: string, chain: string, reference_id: string) {
        console.log(`provider_id`, provider_id, send_asset, chain)


        if (chain.toLocaleLowerCase() == "bantu" && send_asset == "CNGN") {
            const swapper = await this.swapCNGN()
            console.log(`LIVE_SWAPPER`, swapper)
            if (swapper.status == 200) {
                const address = swapper.data.receivableAddress
                const transactionId = swapper.data.transactionId
                const par = {
                    keypair: address,
                    privateKey: "",
                    memo: transactionId
                };
                return par
            }
        }

        // muda provider 
        if (chain.toLocaleLowerCase() == "eth" ) {
            const walletResponse = await MudaPayment.generateLRWallerAddress(
                chain,
                reference_id,
                'LR_RAIL',
                send_asset,
                0
            );

            if (walletResponse && walletResponse.status == 200) {
                const par = {
                    keypair: walletResponse.data.deposit_address,
                    privateKey: "",
                    memo: "na"
                };
                return par
            } else {
                console.error('Wallet service error:', walletResponse);
                return false;
            }
        }

        const exists: any = await this.callQuerySafe(`select * from provider_addresses where provider_id=? AND asset=? and chain=? and status='active'`, [provider_id, send_asset, chain])
        console.log(`exists====>`, exists)
        if (exists.length > 0) {
            const par = {
                keypair: exists[0].address,
                privateKey: "",
                memo: exists[0].memo,
            };
            return par

        }
        return false;

    }


    async swapCNGN() {
        const bnbAddress: any = await this.generate_address("1", "CNGN", "bsc","");
        console.log(`bnbAddress`, bnbAddress)
        if (bnbAddress == false) {
            return false
        }
        const destinationAddress = bnbAddress.keypair
        const destinationNetwork = Network.bsc
        const originNetwork = Network.xbn
        const callbackUrl = process.env.RAIL_CALLBACK
        const swapper: any = await new CNGNUtility().swapAsset(destinationNetwork, destinationAddress, originNetwork, callbackUrl)
        console.log(`swapper`, swapper)
        return swapper
    }


    
    async getQuidaxQuote(from: string, to: string, amount: number) {
        const QuidaxQuote = await await Quidax.makeCryptoFiatSwap('me', from.toLocaleLowerCase(), to.toLocaleLowerCase(), amount);
        console.log(`QuidaxQuote`, QuidaxQuote)
        const status = QuidaxQuote.data.status;
        const data = QuidaxQuote.data.data;
        if (status == 'success') {
            const rateInfo: RateResponse = {
                provider: "quidax",
                providerQuoteId: data.id,
                providerId: 4,
                from: data.from_currency,
                to: data.to_currency,
                fiatAmount: data.to_amount,
                toAmount: data.from_amount,
                cryptoAmount: data.from_amount,
                fee: 0,
                quoteId: "",
                expiresAt: data.expires_at,
                quotedPrice: data.quoted_price
            }
            console.log(`rateInfo`, rateInfo)
            return rateInfo;
        }
        return false
    }

    /**
     * Map asset codes to their correct Quidax networks
     */
    public getQuidaxNetworkMapping(assetCode: string): string {
        const assetNetworkMap: { [key: string]: string } = {
            // USDT mappings
            'USDT': 'trc20',        // Default USDT
            'USDT_BSC': 'bep20',    // USDT on BSC
            'USDT_BASE': 'base',    // USDT on Base

            // USDC mappings
            'USDC': 'erc20',        // Default USDC (Ethereum)
            'USDC_BSC': 'bep20',    // USDC on BSC
            'USDC_BASE': 'base',    // USDC on Base
        };

        // Get network from mapping, fallback to erc20 if not found
        return assetNetworkMap[assetCode.toUpperCase()] || 'erc20';
    }

    /**
     * Extract base currency from asset code (e.g., "USDC_BSC" -> "usdc")
     */
    public extractBaseCurrency(assetCode: string): string {
        // Remove chain suffix and convert to lowercase
        const baseCurrency = assetCode.split('_')[0].toLowerCase();
        return baseCurrency;
    }

    async getQuidaxOffRampQuote(fromCurrency: string, toCurrency: string, amount: number) {
        try {
            console.log(`Getting Quidax off-ramp quote: ${fromCurrency} -> ${toCurrency}, amount: ${amount}`);

            // Determine the correct network and base currency for the asset
            const network = this.getQuidaxNetworkMapping(fromCurrency);
            const baseCurrency = this.extractBaseCurrency(fromCurrency);
            console.log(`Mapped ${fromCurrency} -> base: ${baseCurrency}, network: ${network}`);

            // Get sell quote from Quidax (crypto to fiat)
            const quoteResponse = await Quidax.getSellQuote(
                baseCurrency, // Use base currency without chain suffix
                toCurrency.toLowerCase(),
                amount,
                network
            );

            console.log(`QuidaxOffRampQuote`, quoteResponse);

            if (quoteResponse?.data?.status === "ok" && quoteResponse?.data?.data) {
                const data = quoteResponse.data.data;
                const fee = parseFloat(data.fee || "0");
                const toAmount = parseFloat(data.to_amount || "0");

                // Calculate exchange rate (fiat amount / crypto amount)
                const exchangeRate = amount > 0 ? toAmount / amount : 0;

                // Set default expiration (30 minutes from now)
                const defaultExpiresAt = new Date(Date.now() + 30 * 60 * 1000).toISOString();

                const rateInfo: RateResponse = {
                    provider: "quidax",
                    providerQuoteId: data.quote_id || this.getRandomString(),
                    providerId: 4,
                    from: fromCurrency,
                    to: toCurrency,
                    fiatAmount: toAmount, // Amount user will receive in fiat
                    toAmount: toAmount,
                    cryptoAmount: amount, // Amount user is selling in crypto
                    fee: fee,
                    quoteId: "",
                    expiresAt: defaultExpiresAt,
                    quotedPrice: exchangeRate
                };
                console.log(`OffRampRateInfo`, rateInfo);
                return rateInfo;
            }

            console.error("Failed to get Quidax off-ramp quote:", quoteResponse);
            return {
                error: true,
                message: quoteResponse?.data?.message || quoteResponse?.message || "Failed to get off-ramp quote",
                data: quoteResponse
            };
        } catch (error) {
            console.error("Error getting Quidax off-ramp quote:", error);
            return false;
        }
    }

    async getQuidaxOnRampQuote(fromCurrency: string, toCurrency: string, amount: number) {
        try {
            console.log(`Getting Quidax on-ramp quote: ${fromCurrency} -> ${toCurrency}, amount: ${amount}`);

            // Determine the correct network and base currency for the asset
            const network = this.getQuidaxNetworkMapping(toCurrency);
            const baseCurrency = this.extractBaseCurrency(toCurrency);
            console.log(`Mapped ${toCurrency} -> base: ${baseCurrency}, network: ${network}`);

            // Get purchase quote from Quidax
            const quoteResponse = await Quidax.getPurchaseQuote(
                fromCurrency.toLowerCase(),
                baseCurrency, // Use base currency without chain suffix
                amount,
                network
            );

            console.log(`QuidaxOnRampQuote`, quoteResponse);

            if (quoteResponse?.data?.status === 'ok') {
                const data = quoteResponse.data.data;
                // Calculate accurate exchange rate: Fiat Amount / Crypto Amount
                const cryptoAmount = parseFloat(data.to_amount);
                const exchangeRate = cryptoAmount > 0 ? amount / cryptoAmount : 0;

                // Set default expiration time (30 minutes from now) in UTC timezone to match Quidax API
                const defaultExpiresAt = new Date(Date.now() + 30 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ');

                const rateInfo: RateResponse = {
                    provider: "quidax",
                    providerQuoteId: data.quote_id || this.getRandomString(),
                    providerId: 4,
                    from: fromCurrency,
                    to: toCurrency,
                    fiatAmount: amount,
                    toAmount: cryptoAmount,
                    cryptoAmount: cryptoAmount,
                    fee: parseFloat(data.fee || "0"),
                    quoteId: "",
                    expiresAt: defaultExpiresAt,
                    quotedPrice: exchangeRate
                };
                console.log(`OnRampRateInfo`, rateInfo);
                return rateInfo;
            }

            console.error("Failed to get Quidax on-ramp quote:", quoteResponse);
            // Return error details instead of just false
            return {
                error: true,
                message: quoteResponse?.data?.message || quoteResponse?.message || "Failed to get quote",
                data: quoteResponse
            };
        } catch (error) {
            console.error("Error getting Quidax on-ramp quote:", error);
            return false;
        }
    }


    async saveNotification(title: string, company_id: string, message: any) {
        const newUser = {
            title,
            company_id,
            message
        };
        return await this.insertData('notifications', newUser);
    }

    async getDocVerifiers(c: string) {
        return await this.callQuerySafe(`select * from verifiers where doc_id=?`, [c]);
    }



    async GetVerificationPrice(doc_id: any, country_id: any) {
        let price: any = await this.callQuerySafe("SELECT * FROM doc_prices WHERE doc_id = ? AND country_id = ?", [doc_id, country_id]);
        // let price: any = await this.selectDataQuery("doc_prices", `doc_id = '${doc_id}' AND country_id= '${country_id}'`);
        if (price.length == 0) {
            return "0"
        }
        return price[0]['verification_price'];
    }

    async getPaginationMetadata(tableName: string, page: number, LIMIT: number): Promise<any> {
        const countQuery = `SELECT COUNT(*) as total FROM ${tableName}`;
        const countResult = await this.callRawQuery(countQuery);
        const total = countResult[0]?.total || 0;
        const totalPages = Math.ceil(total / LIMIT);

        return {
            current_page: page,
            next_page: page < totalPages ? page + 1 : null,
            previous_page: page > 1 ? page - 1 : null,
            total_pages: totalPages,
            total_items: total,
            items_per_page: LIMIT
        };
    }

    async getPaginatedData(tableName: string, data: any, selectColumns: string = '*', orderName: string = 'created_on'): Promise<any> {
        const page = data?.page || 1;
        const LIMIT = data?.limit || 14;
        const offset = (page - 1) * LIMIT;

        // Build WHERE clause for search and date range
        let whereClause = '';
        const conditions: string[] = [];

        // Handle search
        if (data?.search) {
            const searchColumns: string[] = data.searchColumns || ['name', 'email', 'description'];
            const searchConditions = searchColumns.map((col: string) => `${col} LIKE '%${data.search}%'`);
            conditions.push(`(${searchConditions.join(' OR ')})`);
        }

        // Handle date range for created_on
        if (data?.startDate || data?.endDate) {
            const dateConditions: string[] = [];

            if (data?.startDate) {
                dateConditions.push(`DATE(created_on) >= DATE('${data.startDate}')`);
            }
            if (data?.endDate) {
                dateConditions.push(`DATE(created_on) <= DATE('${data.endDate}')`);
            }

            if (dateConditions.length > 0) {
                conditions.push(`(${dateConditions.join(' AND ')})`);
            }
        }

        // Combine all conditions
        if (conditions.length > 0) {
            whereClause = `WHERE ${conditions.join(' AND ')}`;
        }

        // Get total count with filters
        const countQuery = `SELECT COUNT(*) as total FROM ${tableName} ${whereClause}`;
        const countResult = await this.callRawQuery(countQuery);
        const total = countResult[0]?.total || 0;
        const totalPages = Math.ceil(total / LIMIT);

        // Get paginated data with filters
        const query = `SELECT ${selectColumns} FROM ${tableName} ${whereClause} ORDER BY ${orderName} DESC LIMIT ${LIMIT} OFFSET ${offset}`;
        const items = await this.callRawQuery(query);

        const pagination = {
            current_page: page,
            next_page: page < totalPages ? page + 1 : null,
            previous_page: page > 1 ? page - 1 : null,
            total_pages: totalPages,
            total_items: total,
            items_per_page: LIMIT
        };

        return { items, pagination };
    }



    async changeTimeStampToDate(expiresAt: any) {
        const expiryDate = new Date(expiresAt)
        return expiryDate.toISOString().slice(0, 19).replace('T', ' ');
    }



    async accountNumberValidation(type: string, currency: string, data: any) {

        if (type === "bank") {

            if (currency === "KES") {

            }
        } else if (type === "mobile_money") {

            if (currency === "KES") {
                // check if data.phone_number starts with a +
                if (!data.phone_number.startsWith("+")) {
                    data.phone_number = "+" + data.phone_number
                }
                const validatePhoneNumber: any = await honeyCoin.validatePhoneNumber(currency, "KE", data.phone_number)
                if (!validatePhoneNumber?.success) {
                    return { status: false, message: validatePhoneNumber.message || "Invalid phone number", data: validatePhoneNumber?.data }
                }
            }
        }

        return { status: true, message: "Account number is valid" }
    }

    /**
     * Generate a secure password using bcrypt
     * @param password - Plain text password
     * @param saltRounds - Number of salt rounds (default: 12)
     * @returns Promise<string> - Hashed password
     */
    async generatePassword(password: string, saltRounds: number = 12): Promise<string> {
        try {
            const bcrypt = require('bcrypt');
            const hashedPassword = await bcrypt.hash(password, saltRounds);
            return hashedPassword;
        } catch (error) {
            console.error('Error generating password hash:', error);
            throw new Error('Failed to generate password hash');
        }
    }

    /**
     * Verify a password against a bcrypt hash
     * @param password - Plain text password to verify
     * @param hash - Bcrypt hash to verify against
     * @returns Promise<boolean> - True if password matches
     */
    async verifyPassword(password: string, hash: string): Promise<boolean> {
        try {
            const bcrypt = require('bcrypt');
            return await bcrypt.compare(password, hash);
        } catch (error) {
            console.error('Error verifying password:', error);
            return false;
        }
    }


    async updatePayinTransaction(trans_id: string, pay_in_status:"PROCESSING_PAYOUT"|"INITIATED_PAYMENT"|"RECEIVED"|"SUCCESSFUL"|"FAILED"|"EXPIRED"|"CANCELLED"|"ONHOLD", status: "PENDING"|"SUCCESSFUL"|"FAILED"|"EXPIRED"|"CANCELLED"|"ONHOLD", message: string, transaction: any) {
        await this.updateData('quotes', `transId = '${trans_id}'`, { pay_in_status, message });
    }

    async updateFinalTransaction(trans_id: string, status: "AWAITING_REVERSAL"|"PROCESSING_PAYOUT"|"INITIATED_PAYMENT"|"RECEIVED"|"SUCCESSFUL"|"FAILED"|"EXPIRED"|"CANCELLED"|"ONHOLD", message: string, transaction: any) {
        await this.updateData('quotes', `transId = '${trans_id}'`, { status, message });
    }

    validatePasswordPolicy(password: string): {
        isValid: boolean;
        errors: string[];
        strength: 'weak' | 'medium' | 'strong';
        score: number;
    } {
        const errors: string[] = [];
        let score = 0;

        // Check minimum length (8 characters minimum, 12+ recommended)
        if (password.length < 8) {
            errors.push('Password must be at least 8 characters long');
        } else if (password.length >= 12) {
            score += 2;
        } else {
            score += 1;
        }

        // Check for uppercase letters
        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        } else {
            score += 1;
        }

        // Check for lowercase letters
        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        } else {
            score += 1;
        }

        // Check for numbers
        if (!/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        } else {
            score += 1;
        }

        // Check for special characters
        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push('Password must contain at least one special character');
        } else {
            score += 1;
        }

        // Check against common weak passwords
        const weakPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey',
            'dragon', 'master', 'hello', 'freedom', 'whatever',
            'qwerty123', 'trustno1', 'jordan', 'harley', 'ranger',
            'iwantu', 'jennifer', 'hunter', 'buster', 'soccer',
            'baseball', 'teens', 'silver', 'golden', 'andrea',
            'michelle', 'love', 'joshua', 'andrew', 'lovers',
            'matthew', 'summer', 'eagle', 'computer', 'ncc1701',
            'guitar', 'jackson', 'chelsea', 'black', 'diamond',
            'nascar', 'cowboy', 'elephant', 'coffee', 'bulldog',
            'slayer', 'bandit', 'cherry', 'orange', 'reddog',
            'three', 'gandalf', 'pink', 'yellow', 'camaro',
            'secret', 'dick', 'test', 'mike', 'charles'
        ];

        if (weakPasswords.includes(password.toLowerCase())) {
            errors.push('Password is too common. Please choose a more unique password');
            score -= 2;
        }

        // Determine strength based on score
        let strength: 'weak' | 'medium' | 'strong';
        if (score < 3) {
            strength = 'weak';
        } else if (score < 5) {
            strength = 'medium';
        } else {
            strength = 'strong';
        }

        return {
            isValid: errors.length === 0,
            errors,
            strength,
            score
        };
    }

    /**
     * Generate a secure random password
     * @param length - Length of password (default: 12)
     * @param includeSpecialChars - Whether to include special characters (default: true)
     * @returns string - Generated password
     */
    generateSecurePassword(length: number = 12, includeSpecialChars: boolean = true): string {
        const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const lowercase = 'abcdefghijklmnopqrstuvwxyz';
        const numbers = '0123456789';
        const special = '!@#$%^&*()_+-=[]{}|;:,.<>?';

        let chars = uppercase + lowercase + numbers;
        if (includeSpecialChars) {
            chars += special;
        }

        let password = '';

        // Ensure at least one character from each required category
        password += uppercase[Math.floor(Math.random() * uppercase.length)];
        password += lowercase[Math.floor(Math.random() * lowercase.length)];
        password += numbers[Math.floor(Math.random() * numbers.length)];
        if (includeSpecialChars) {
            password += special[Math.floor(Math.random() * special.length)];
        }

        // Fill the rest with random characters
        for (let i = password.length; i < length; i++) {
            password += chars[Math.floor(Math.random() * chars.length)];
        }

        // Shuffle the password
        return password.split('').sort(() => Math.random() - 0.5).join('');
    }

    /**
     * Track failed login attempts and implement rate limiting
     * @param identifier - User identifier (email, username, etc.)
     * @param maxAttempts - Maximum allowed attempts (default: 5)
     * @param lockoutDuration - Lockout duration in minutes (default: 15)
     * @returns Object with lockout status and remaining attempts
     */
    async trackFailedLoginAttempt(identifier: string, maxAttempts: number = 5, lockoutDuration: number = 15): Promise<{
        isLocked: boolean;
        remainingAttempts: number;
        lockoutExpiresAt?: Date;
        message?: string;
    }> {
        try {
            const now = new Date();

            // Check if there's an existing record for this identifier
            const existingRecord: any = await this.selectDataQuerySafe("login_attempts", { identifier });

            if (existingRecord.length === 0) {
                // First failed attempt
                await this.insertData("login_attempts", {
                    identifier,
                    failed_attempts: 1,
                    last_attempt_at: now,
                    is_locked: false,
                    created_at: now,
                    updated_at: now
                });

                return {
                    isLocked: false,
                    remainingAttempts: maxAttempts - 1
                };
            }

            const record = existingRecord[0];
            const lastAttempt = new Date(record.last_attempt_at);
            const timeSinceLastAttempt = (now.getTime() - lastAttempt.getTime()) / (1000 * 60); // minutes

            // If account is locked, check if lockout period has expired
            if (record.is_locked && record.lockout_expires_at) {
                const lockoutExpires = new Date(record.lockout_expires_at);
                if (now < lockoutExpires) {
                    return {
                        isLocked: true,
                        remainingAttempts: 0,
                        lockoutExpiresAt: lockoutExpires,
                        message: `Account is locked. Try again after ${lockoutExpires.toLocaleString()}`
                    };
                } else {
                    // Lockout expired, reset attempts
                    await this.updateData("login_attempts", `identifier = '${identifier}'`, {
                        failed_attempts: 1,
                        last_attempt_at: now,
                        is_locked: false,
                        lockout_expires_at: null,
                        updated_at: now
                    });

                    return {
                        isLocked: false,
                        remainingAttempts: maxAttempts - 1
                    };
                }
            }

            // Check if enough time has passed to reset attempts (optional: reset after 1 hour)
            if (timeSinceLastAttempt > 60) {
                await this.updateData("login_attempts", `identifier = '${identifier}'`, {
                    failed_attempts: 1,
                    last_attempt_at: now,
                    is_locked: false,
                    lockout_expires_at: null,
                    updated_at: now
                });

                return {
                    isLocked: false,
                    remainingAttempts: maxAttempts - 1
                };
            }

            // Increment failed attempts
            const newAttemptCount = record.failed_attempts + 1;
            const remainingAttempts = Math.max(0, maxAttempts - newAttemptCount);

            if (newAttemptCount >= maxAttempts) {
                // Lock the account
                const lockoutExpires = new Date(now.getTime() + (lockoutDuration * 60 * 1000));
                await this.updateData("login_attempts", `identifier = '${identifier}'`, {
                    failed_attempts: newAttemptCount,
                    last_attempt_at: now,
                    is_locked: true,
                    lockout_expires_at: lockoutExpires,
                    updated_at: now
                });

                return {
                    isLocked: true,
                    remainingAttempts: 0,
                    lockoutExpiresAt: lockoutExpires,
                    message: `Account locked due to too many failed attempts. Try again after ${lockoutExpires.toLocaleString()}`
                };
            } else {
                // Update attempt count
                await this.updateData("login_attempts", `identifier = '${identifier}'`, {
                    failed_attempts: newAttemptCount,
                    last_attempt_at: now,
                    updated_at: now
                });

                return {
                    isLocked: false,
                    remainingAttempts,
                    message: remainingAttempts === 0 ? 'Account will be locked on next failed attempt' : undefined
                };
            }
        } catch (error) {
            console.error('Error tracking failed login attempt:', error);
            // Return safe defaults
            return {
                isLocked: false,
                remainingAttempts: maxAttempts - 1
            };
        }
    }

    /**
     * Reset failed login attempts for a user (call on successful login)
     * @param identifier - User identifier
     */
    async resetFailedLoginAttempts(identifier: string): Promise<void> {
        try {
            await this.updateData("login_attempts", `identifier = '${identifier}'`, {
                failed_attempts: 0,
                is_locked: false,
                lockout_expires_at: null,
                updated_at: new Date()
            });
        } catch (error) {
            console.error('Error resetting failed login attempts:', error);
        }
    }

    /**
     * Secure error response - prevents verbose error information from being exposed
     * @param statusCode - HTTP status code
     * @param message - User-friendly error message
     * @param data - Optional data to include (never include error objects)
     * @returns Formatted response object
     */
    makeSecureResponse(statusCode: number, message: string, data?: any) {
        // Log the full error details internally for debugging
        if (data && typeof data === 'object' && data.error) {
            console.error('Internal error details:', data.error);
            // Remove sensitive error information from response
            delete data.error;
        }

        return this.makeResponse(statusCode, message, data);
    }

    /**
     * Handle errors securely without exposing internal details
     * @param error - The error object
     * @param context - Context where the error occurred
     * @param userMessage - User-friendly error message
     * @returns Secure error response
     */
    handleErrorSecurely(error: any, context: string, userMessage: string = "An error occurred") {
        // Log the full error internally for debugging
        console.error(`Error in ${context}:`, error);

        // Return a secure response without exposing error details
        return this.makeResponse(500, userMessage);
    }

}