import * as cron from 'node-cron';
import Accounts from '../models/accounts';
import MudaPayment from './MudaPayment';
import PollingBridgeSwapService from '../services/polling.bridgeSwap.service';
import PollingPayoutsService from '../services/polling.payouts.service';
import BridgeQueueService from '../services/bridgeQueue.service';
const pollingBridgeSwapService = new PollingBridgeSwapService();
class CronService {
    constructor() {
        console.log("cron initiated==>")
        this.scheduleEveryThirtySeconds();
        this.Every45Seconds();
        this.scheduleEveryTwelveHours();
        this.scheduleBridgeSwapPolling();
        this.scheduleBridgeQueueProcessing();
        this.scheduleDailyCleanup();
        this.schedueleveryminute
        MudaPayment.getJWT();


    }
    private schedueleveryminute() {
        cron.schedule('*/1 * * * *', this.everyMinuteTask);
    }

    private scheduleEveryTwelveHours() {
        cron.schedule('0 */12 * * *', this.everyTwelveHoursTask);
    }
    /**
     * Schedule bridge/swap quote polling every 5 minutes
     */
    private scheduleBridgeSwapPolling() {
        // Run every 5 minutes: 0 */5 * * * *
        cron.schedule('0 */5 * * * *', () => {
            this.bridgeSwapPollingTask();
        });

        console.log('✅ Bridge/swap polling scheduled to run every 5 minutes');
    }

    /**
     * Task to check pending bridge/swap quotes
     */
    private bridgeSwapPollingTask = async () => {
        try {
            console.log('🔄 Starting scheduled bridge/swap quote polling...');
            await pollingBridgeSwapService.checkPendingBridgeSwapQuotes();
            console.log('✅ Scheduled bridge/swap quote polling completed');
        } catch (error: any) {
            console.error('❌ Error in scheduled bridge/swap polling:', error.message);
        }
    };

    /**
     * Schedule bridge queue processing every 2 minutes
     */
    private scheduleBridgeQueueProcessing() {
        // Run every 2 minutes: 0 */2 * * * *
        cron.schedule('0 */2 * * * *', () => {
            this.bridgeQueueProcessingTask();
        });

        console.log('✅ Bridge queue processing scheduled to run every 2 minutes');
    }

    /**
     * Task to process bridge queue
     */
    private bridgeQueueProcessingTask = async () => {
        try {
            console.log('🔄 Starting scheduled bridge queue processing...');
            await BridgeQueueService.processBridgeQueue();
            console.log('✅ Scheduled bridge queue processing completed');
        } catch (error: any) {
            console.error('❌ Error in scheduled bridge queue processing:', error.message);
        }
    };

    /**
     * Schedule daily cleanup at 2 AM
     */
    private scheduleDailyCleanup() {
        // Run daily at 2 AM: 0 2 * * *
        cron.schedule('0 2 * * *', () => {
            this.dailyCleanupTask();
        });

        console.log('✅ Daily cleanup scheduled to run at 2 AM');
    }

    /**
     * Task to clean up old queue items
     */
    private dailyCleanupTask = async () => {
        try {
            console.log('🧹 Starting daily cleanup...');
            await BridgeQueueService.cleanupOldQueueItems();
            console.log('✅ Daily cleanup completed');
        } catch (error: any) {
            console.error('❌ Error in daily cleanup:', error.message);
        }
    };
    private everyMinuteTask() {
        console.log('Task running every minute, check pending payouts');
        new PollingPayoutsService().checkPendingDirectPayouts();
    }

    private everyTwelveHoursTask() {
        console.log('Task running every 12 hours, get muda key');
        // Add your logic to get muda key here
        MudaPayment.getJWT();
    }

    private Every45Seconds() {
        cron.schedule('*/45 * * * * *', this.scheduleEvery45Seconds);
    }


    private scheduleEveryThirtySeconds() {
        cron.schedule('*/59 * * * * *', this.everyThirtySecondsTask);
    }


    private scheduleEvery45Seconds() {
        console.log('Task running every 59 seconds, check pending deposits');
        new Accounts().expirePendingQuotes()
    }

    private everyThirtySecondsTask() {
        console.log('Task running every 45 seconds, check pending deposits');
        new Accounts().getQuotestatus()
    }
}

export default CronService;
