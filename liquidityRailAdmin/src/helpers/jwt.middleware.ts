import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from 'dotenv';
import { setItem } from './connectRedis';
config();
const JWT_SECRET: any = process.env.JWT_SECRET;

export class JWTMiddleware {
    static verifyToken(req: Request, res: Response, next: NextFunction) {
        const authHeader = req.headers.authorization;

        if (!authHeader) {
            return res.status(401).send({ message: "No authorization header provided." });
        }

        const parts = authHeader.split(' ');

        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            return res.status(401).send({ message: "Token format is 'Bearer <token>'." });
        }

        const token = parts[1];

        jwt.verify(token, JWT_SECRET, (err: any, decoded: any) => {
            if (err) {
                return res.status(401).send({ message: "Unauthorized." });
            }

            
            // req.body.user_id = decoded.id; //staff ID
            req.body.user_id = decoded.user_id;
            // Only set company_id from JWT if not already present in request body
            if (!req.body.company_id) {
                req.body.company_id = decoded.company_id || decoded.clientId;
            }

            next();
        });
    }
}
