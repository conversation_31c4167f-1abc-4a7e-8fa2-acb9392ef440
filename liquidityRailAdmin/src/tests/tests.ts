// test.ts

import kotaniPay from "../helpers/kotani";
import CNGNUtility from "../helpers/CNGNUtility";
import { Network } from "cngn-typescript-library";
import MudaPayment from "../helpers/MudaPayment";
import Accounts from "../models/accounts";
import Quidax from "../helpers/Quidax";
import { WebhookSender } from "../helpers/webhookSender";
import HoneyCoin from "../helpers/HoneyCoin";
import { MudaProvider } from "../helpers/MudaProvider";
import quidax from "../helpers/Quidax";


 
export async function runTests() {
    // createPaymentIntent();
     generateLRWallerAddress();
  }

  async function generateLRWallerAddress() {
    const walletResponse = await MudaPayment.generateLRWallerAddress(
    "basc",
    "9939393",
    'LR_RAIL',
    "usdc",
    0
);
console.log("walletResponse",walletResponse)

if (walletResponse && walletResponse.status == 200) {
    const par = {
        keypair: walletResponse.data.address,
        privateKey: "",
        memo: "na"
    };
    return par
} else {
    console.error('Wallet service error:', walletResponse);
    return false;
}
}


  async function quidaxTest() {
    const rec = ""
    const quote = await quidax.createWithdraw({
      user: "me",
      currency: "usdc",
      amount: "1",
      transaction_note: "qw_1234567890",
      narration: "test",
      fund_uid: rec,
      fund_uid2: "",
      reference: `TEST_${Date.now()}`,
      network: "bep20"
    });
    console.log(quote);
  }
