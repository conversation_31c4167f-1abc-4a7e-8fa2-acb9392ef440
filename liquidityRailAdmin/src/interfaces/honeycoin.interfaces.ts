// export interface HoneyCoinPaymentInformation {
//     permanent: boolean;
//     accountNumber: string;
//     bankName: string;
//     createdAt: string;
//     expiresAt: string;
//     note: string;
//     amount: string;
//     bankLogo?: string;
// }
export interface HoneyCoinPaymentInformation {
    public_id?: string,
    account_name?: string,
    account_number?: string,
    bank_name?: string,
    bank_code?: string,
    reference?: string,
    amount?: string,
    amount_expected?: string,
    created_at?: string,
    updated_at?: string,
    expires_at?: string;
    processor_fee?: string,
    vat?: string;
    note?: string;
    bank_logo?: string;
}

// HoneyCoin Transaction Response
export interface HoneyCoinTransactionResponse {
    success: boolean;
    data?: {
        transactionId: string;
        status: string;
        type: string;
        externalReference: string;
        method: string;
        depositAddress?: string;
        txId?: string;
        virtualAccount?: HoneyCoinPaymentInformation;
    };
    message?: string;
}

// HoneyCoin OnRamp Response
export interface HoneyCoinOnRampResponse {
    success: boolean;
    data?: {
        transactionId: string;
        status: string;
        depositAddress: string;
        amount: string;
        currency: string;
        expiresAt: string;
        virtualAccount?: HoneyCoinPaymentInformation;
    };
    message?: string;
}

// HoneyCoin OffRamp Response
export interface HoneyCoinOffRampResponse {
    success: boolean;
    data?: {
        transactionId: string;
        status: string;
        amount: string;
        currency: string;
        payoutMethod: string;
        externalReference: string;
    };
    message?: string;
}
