// Quidax API Interfaces

export interface QuidaxCustomer {
    email: string;
    first_name: string;
    last_name: string;
}

export interface QuidaxWalletAddress {
    address: string;
    network: string; // "bep20", "trc20", etc.
}
export interface ChainInfo {
    chain: string;
    network: string;
    asset_code: string;
    address: string;
    memo: string;
}
    
export interface QuidaxOnRampInitiateRequest {
    merchant_reference: string;
    from_currency: string;
    to_currency: string;
    from_amount: string;
    customer: QuidaxCustomer;
    wallet_address: QuidaxWalletAddress;
}

export interface QuidaxOnRampInitiateResponse {
    status: string;
    message: string;
    data: {
        public_id: string;
        reference: string;
        merchant_reference: string;
        from_currency: string;
        to_currency: string;
        from_amount: string;
        to_amount: string;
        status: string;
        created_at: string;
        updated_at: string;
        blockchain_fee: string;
    };
}

export interface QuidaxOnRampConfirmResponse {
    status: string;
    message: string;
    data: {
        public_id: string;
        account_name: string;
        account_number: string;
        bank_name: string;
        reference: string;
        amount: string;
        amount_expected: string;
        created_at: string;
        updated_at: string;
        processor_fee: string;
        vat: string;
    };
}

export interface QuidaxFiatDeposit {
    fee: string;
    amount: string;
    status: string;
    currency: string;
    public_id: string;
    created_at: string;
    updated_at: string;
}

export interface QuidaxCryptoPayout {
    amount: string;
    status: string;
    address: string;
    network: string;
    currency: string;
    public_id: string;
    created_at: string;
    updated_at: string;
    processor_fee: number;
    transaction_hash: string;
}

export interface QuidaxOnRampWebhookData {
    event: string; // "buy_transaction.successful", "buy_transaction.processing", "buy_transaction.failed"
    data: {
        mode: string; // "buy"
        status: string; // "completed", "processing", "needs_attention"
        network: string; // "trc20", "bep20", etc.
        public_id: string;
        to_amount: string;
        created_at: string;
        updated_at: string;
        from_amount: string;
        to_currency: string;
        fiat_deposit: QuidaxFiatDeposit;
        crypto_payout: QuidaxCryptoPayout | null;
        from_currency: string;
        blockchain_fee: string;
        merchant_reference: string;
    };
}

export interface ProcessedQuidaxWebhook {
    event: string;
    merchant_reference: string;
    status: 'completed' | 'processing' | 'failed' | 'unknown';
    mode?: string;
    network?: string;
    crypto_amount?: string;
    crypto_currency?: string;
    fiat_amount?: string;
    fiat_currency?: string;
    transaction_hash?: string;
    quidax_reference?: string;
    blockchain_fee?: string;
    processor_fee?: number;
    fiat_deposit?: QuidaxFiatDeposit;
    crypto_payout?: QuidaxCryptoPayout | null;
    reason?: string;
    data?: any;
}

export interface QuidaxPurchaseQuoteResponse {
    status: string;
    message: string;
    data: {
        quote_id: string;
        from_currency: string;
        to_currency: string;
        from_amount: string;
        to_amount: string;
        exchange_rate: string;
        fee: string;
        expires_at: string;
        network: string;
    };
}

export interface QuidaxPurchaseLimitsResponse {
    status: string;
    message: string;
    data: {
        currency: string;
        min_amount: string;
        max_amount: string;
        daily_limit: string;
        monthly_limit: string;
    };
}

export interface PaymentInformation {
    public_id: string,
    account_name: string,
    account_number: string,
    bank_name: string,
    bank_code: string,
    reference: string,
    amount: string,
    amount_expected: string,
    created_at: string,
    updated_at: string,
    processor_fee: string,
    vat: string
}
