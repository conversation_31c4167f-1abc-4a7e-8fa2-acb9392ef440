export interface SaveQuoteRequest {
    provider_service_id?: string;
    source: string;
    reference_id?: string;
    sending_address?: string;
    company_id: number;
    send_amount: number;
    receive_currency: string;
    payment_method_id: string;
    book_quote_id?: string;
    send_asset?: string;
    service_id?: string;
    provider_id?: string;
    transaction_type?: 'off_ramp' | 'on_ramp' | 'bridge' | 'swap';
    asset_code?: string;
    receiver_address?: string;
    network?: string;
    account_number?: string;
    chain?: string;
    account_name?: string;
    refund_address?: string;
    // Rhino bridge/swap specific fields
    from_chain?: string;
    to_chain?: string;
    from_token?: string;
    to_token?: string;
    operation_type?: 'bridge' | 'swap';
    destination_address?: string;
}

export interface quoteRequest {
    amount: number;
    currency: string;
    asset_code: string;
    chain?: string;
    provider_id: number;
}

export interface QuoteValidationResult {
    isValid: boolean;
    error?: string;
    data?: any;
}

export interface ProviderServiceInfo {
    service_id: string;
    provider_id: string;
    max_amount: number;
    min_amount: number;
    service_code: string;
}

export interface AddressInfo {
    memo?: string;
    keypair?: string;
    address: string;
}

export interface QuoteTransactionData {
    company_id: number;
    provider_id: string;
    transId: string;
    send_asset: string;
    client_reference_id?: string;
    send_amount: number;
    receive_currency: string;
    receive_amount: number;
    ex_rate: number;
    account_number: string;
    service_id: string;
    payable_amount: number;
    sending_address: string;
    fee: number;
    fee_currency: string;
    receiver_address: string;
    status: string;
    provider_ref_id?: string;
    provider_address?: string;
    provider_memo?: string;
    expires_at: string;
    payment_method_id: string;
}
export const onrampLifecycle = [
    "PENDING",
    "INITIATED",
    "RECEIVED",
    "SUCCESSFUL",
    "FAILED",
    "EXPIRED",
    "CANCELLED",
    "ONHOLD",
    "PROCESSING_PAYOUT",
]

export const offrampLifecycle = [
    "PENDING",
    "INITIATED",
    "RECEIVED",
    "SUCCESSFUL",
    "FAILED",
    "EXPIRED",
    "CANCELLED",
    "ONHOLD"
]

// Rhino bridge/swap specific interfaces
export interface RhinoBridgeSwapRequest {
    company_id: number;
    provider_id: number; // Required: Provider ID from providers table
    service_id: number; // Required: Service ID from services table
    operation_type: 'bridge' | 'swap';
    from_chain: string;
    to_chain: string;
    from_token: string;
    to_token: string;
    send_amount: number;
    destination_address: string;
    depositor_address: string; // Required: Address that will send funds to smart deposit
    reference_id: string; // Required: Client reference ID in format REF9076505380
    source?: string;
}

export interface RhinoQuoteResponse {
    quote_id: string;
    status: string;
    operation_type: 'bridge' | 'swap';
    from_chain: string;
    to_chain: string;
    from_token: string;
    to_token: string;
    send_amount: number;
    receive_amount: number;
    exchange_rate: number;
    fee: number;
    smart_deposit_address?: string;
    destination_address: string;
    expires_at: string;
    rhino_quote_id?: string;
}

export const bridgeSwapLifecycle = [
    "PENDING",
    "QUOTE_GENERATED",
    "QUOTE_CONFIRMED",
    "DEPOSIT_PENDING",
    "PROCESSING",
    "COMPLETED",
    "FAILED",
    "EXPIRED",
    "CANCELLED"
]

