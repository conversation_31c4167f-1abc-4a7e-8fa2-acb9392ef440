import Model from "../helpers/model";
import rhinoClient from "../helpers/Rhino";
import { get } from "../helpers/httpRequest";
import { mapToRhinoChain } from "../helpers/rhinoTypes";

// Interface for polling response format
interface PollingResponse {
    statusCode: number;    // 200, 400, 202, 203
    transStatus: string;   // "SUCCESS", "FAILED", "PENDING", "ONHOLD"
    description: string;   // Human-readable description
    data: any;            // Original response data
}

// Interface for Rhino deposit address history response
interface RhinoDepositHistoryResponse {
    depositAddress: string;
    depositChain: string;
    bridges: Array<{
        _id: string;
        tokenSymbol: string;
        tokenAddress: string;
        amount: string;
        amountUsd: number;
        amountWei: string;
        history: {
            state: "EXECUTED" | "PENDING" | "FAILED" | "CANCELLED";
            _tag: string;
            _id: string;
            chainIn: string;
            chainOut: string;
            amountIn: string;
            amountInUsd: number;
            amountOut: string;
            amountOutUsd: number;
            recipient: string;
            depositor: string;
            createdAt: string;
            commitmentDate: string;
            depositTxHash: string;
            withdrawTxHash?: string;
            withdrawCommittedAt?: string;
        };
        _tag: string;
    }>;
}

class PollingBridgeSwapService extends Model {
    constructor() {
        super();
    }

    /**
     * Main method to check all pending bridge/swap quotes
     * This should be called by the cron job every 6 minutes
     */
    public async checkPendingBridgeSwapQuotes(): Promise<void> {
        try {
            console.log('🔹 Starting bridge/swap quote polling check...');

            // Get all quotes with status 'QUOTE_CONFIRMED' that have depositTxHash
            // Join with bridge_swap_quote_details to get the necessary information
            const pendingQuotes: any = await this.callRawQuery(
                `SELECT q.*, bsd.from_chain, bsd.to_chain, bsd.from_token, bsd.to_token,
                        bsd.depositor_address, bsd.destination_address, bsd.smart_deposit_address,
                        bsd.estimated_duration, bsd.rhino_quote_id, bsd.operation_type, bsd.depositTxHash
                 FROM quotes q
                 INNER JOIN bridge_swap_quote_details bsd ON q.transId = bsd.quote_id
                 WHERE q.status = 'QUOTE_CONFIRMED'
                 AND q.pay_in_status IN ('PENDING_DEPOSIT', 'PENDING')
                 AND bsd.operation_type IN ('bridge', 'swap')
                 AND bsd.smart_deposit_address IS NOT NULL
                 AND bsd.smart_deposit_address != ''
                 AND bsd.depositTxHash IS NOT NULL
                 AND bsd.depositTxHash != ''
                 ORDER BY q.created_on ASC`
            );

            console.log(`🔹 Found ${pendingQuotes.length} confirmed bridge/swap quotes with depositTxHash to check`);

            for (const quote of pendingQuotes) {
                try {
                    await this.checkSingleBridgeSwapQuote(quote);
                } catch (error: any) {
                    console.error(`❌ Error checking quote ${quote.transId}:`, error.message);
                    // Continue with other quotes even if one fails
                }
            }

            console.log('✅ Bridge/swap quote polling check completed');
        } catch (error: any) {
            console.error('❌ Error in checkPendingBridgeSwapQuotes:', error.message);
        }
    }

    /**
     * Check a single bridge/swap quote status
     */
    private async checkSingleBridgeSwapQuote(quote: any): Promise<void> {
        const {
            transId,
            client_reference_id,
            from_chain,
            to_chain,
            from_token,
            to_token,
            smart_deposit_address,
            operation_type,
            depositTxHash
        } = quote;

        console.log(`🔹 Checking quote ${transId} (${client_reference_id}) - ${operation_type} ${from_token} ${from_chain} → ${to_token} ${to_chain}`);
        console.log(`🔹 Looking for depositTxHash: ${depositTxHash}`);

        // Use the smart deposit address from the details table
        const depositAddress = smart_deposit_address;
        const depositChain = mapToRhinoChain(from_chain); // Map to Rhino format

        console.log(`🔹 Chain mapping: ${from_chain} → ${depositChain}`);

        if (!depositAddress || !depositChain || !depositTxHash) {
            console.error(`❌ Missing required data for quote ${transId}`);
            console.error(`❌ Deposit address: ${depositAddress}, Chain: ${depositChain}, TxHash: ${depositTxHash}`);
            return;
        }

        console.log(`🔹 Checking deposit address ${depositAddress} on chain ${depositChain} for txHash ${depositTxHash}`);

        // Check the deposit address history with Rhino
        const pollingResponse = await this.checkRhinoDepositStatus(depositAddress, depositChain, depositTxHash, transId);

        // Update quote status based on response
        await this.updateQuoteStatus(quote, pollingResponse);
    }

    /**
     * Check Rhino deposit address status and history
     * NEW LOGIC: Match by exact depositTxHash, check for EXECUTED state and accepted tag
     */
    private async checkRhinoDepositStatus(
        depositAddress: string,
        depositChain: string,
        depositTxHash: string,
        transId: string
    ): Promise<PollingResponse> {
        try {
            console.log(`🔹 Checking Rhino deposit status for ${depositAddress} on ${depositChain} with txHash ${depositTxHash}`);

            // Use the Rhino client to get deposit address history
            const response = await rhinoClient.getDepositAddressHistory(depositAddress, depositChain);

            if (!response.success) {
                console.error(`❌ Failed to get deposit address history:`, response);
                return this.mapPollingResponse(500, "Failed to check deposit status", response);
            }

            const historyData: RhinoDepositHistoryResponse = response.data;
            console.log(`🔹 Deposit history response:`, JSON.stringify(historyData, null, 2));

            // NEW LOGIC: Check bridges array for exact depositTxHash match
            if (!historyData.bridges || historyData.bridges.length === 0) {
                // Empty bridges array = no deposit made yet (still waiting)
                console.log(`🔹 No deposit detected yet for address ${depositAddress} - still pending`);
                return this.mapPollingResponse(202, "No deposit detected yet - still pending", historyData);
            }

            // Find the bridge with exact depositTxHash match
            const matchingBridge = historyData.bridges.find(bridge =>
                bridge.history &&
                bridge.history.depositTxHash &&
                bridge.history.depositTxHash.toLowerCase() === depositTxHash.toLowerCase()
            );

            if (!matchingBridge) {
                // No bridge found with matching depositTxHash - still processing
                console.log(`🔹 No bridge found with depositTxHash ${depositTxHash} for quote ${transId} - still processing`);
                return this.mapPollingResponse(202, "Transaction still processing", historyData);
            }

            const bridgeState = matchingBridge.history.state;
            const bridgeTag = matchingBridge._tag;

            console.log(`🔹 Found matching bridge - state: ${bridgeState}, tag: ${bridgeTag} for quote ${transId}`);

            if (bridgeState === "EXECUTED" && bridgeTag === "accepted") {
                // Bridge completed successfully - both state and tag must be correct
                console.log(`✅ Bridge executed successfully for quote ${transId} (state: EXECUTED, tag: accepted, txHash: ${depositTxHash})`);
                return this.mapPollingResponse(200, "Bridge executed successfully", historyData);
            } else {
                // Any other state (PENDING, etc.) - still processing
                console.log(`🔹 Bridge still processing (state: ${bridgeState}, tag: ${bridgeTag}) for quote ${transId}`);
                return this.mapPollingResponse(202, `Bridge ${bridgeState.toLowerCase()}`, historyData);
            }

        } catch (error: any) {
            console.error(`❌ Error checking Rhino deposit status:`, error);
            return this.mapPollingResponse(500, `Error checking deposit status: ${error.message}`);
        }
    }

    /**
     * Update quote status based on polling response
     */
    private async updateQuoteStatus(quote: any, pollingResponse: PollingResponse): Promise<void> {
        const { transId } = quote;
        const { statusCode, description } = pollingResponse;

        try {
            let updateData: any = {};
            let logMessage = "";

            if (statusCode === 200) {
                // Bridge executed successfully - exact depositTxHash match found with EXECUTED state and accepted tag
                updateData = {
                    status: 'CRYPTO_RECEIVED',
                    pay_in_status: 'SUCCESSFUL',
                    reason: 'Bridge executed successfully',
                };
                logMessage = `✅ Bridge executed successfully for quote ${transId}`;
                console.log(logMessage);

            } else if (statusCode === 202) {
                // Still pending - either no deposit detected yet or transaction still processing
                logMessage = `🔹 Bridge still pending for quote ${transId}: ${description}`;
                console.log(logMessage);
                // Don't update status - leave it as QUOTE_CONFIRMED until it's successful
                return;

            } else {
                // Error status
                updateData = {
                    status: 'FAILED',
                    pay_in_status: 'FAILED',
                    reason: `Polling error: ${description}`,
                };
                logMessage = `❌ Polling error for quote ${transId}: ${description}`;
                console.log(logMessage);
            }

            // Update the quote in database only for success or error cases
            if (Object.keys(updateData).length > 0) {
                await this.updateData('quotes', `transId = '${transId}'`, updateData);
                console.log(`✅ Updated quote ${transId} status to ${updateData.status}`);
            }

        } catch (error: any) {
            console.error(`❌ Error updating quote ${transId} status:`, error.message);
        }
    }

    /**
     * Map response to standard polling response format
     */
    private mapPollingResponse(statusCode: number, description: string, data: any = null): PollingResponse {
        let transStatus: string;

        if (statusCode === 200) {
            transStatus = "SUCCESS";
        } else if (statusCode === 400) {
            transStatus = "FAILED";
        } else if (statusCode === 202) {
            transStatus = "PENDING";
        } else {
            transStatus = "ONHOLD";
        }

        return {
            statusCode,
            transStatus,
            description,
            data
        };
    }
}

export default PollingBridgeSwapService;