import Model from '../helpers/model';
import Rhino from '../helpers/Rhino';

interface BridgeQueueItem {
    quote_id: string;
    smart_deposit_address: string;
    deposit_chain: string;
    created_at: Date;
    status: 'QUEUED' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
    retry_count: number;
    last_checked_at?: Date;
    reason?: string;
}

interface RhinoDepositHistoryResponse {
    depositAddress: string;
    depositChain: string;
    bridges: Array<{
        _id: string;
        tokenSymbol: string;
        tokenAddress: string;
        amount: string;
        amountUsd: number;
        amountWei: string;
        sender: string;
        txHash: string;
        reason?: string;
        createdAt: string;
        history?: {
            state: "EXECUTED";
            _tag: string;
            _id: string;
            chainIn: string;
            chainOut: string;
            amountIn: string;
            amountInUsd: number;
            amountOut: string;
            amountOutUsd: number;
            recipient: string;
            depositor: string;
            createdAt: string;
            commitmentDate: string;
            depositTxHash: string;
            withdrawTxHash?: string;
            withdrawCommittedAt?: string;
        };
        _tag: "accepted" | "rejected";
    }>;
}

class BridgeQueueService extends Model {
    constructor() {
        super();
    }

    /**
     * Add a bridge quote to the queue
     */
    async addToQueue(quoteId: string, smartDepositAddress: string, depositChain: string): Promise<boolean> {
        try {
            console.log(`🔄 Adding bridge quote ${quoteId} to queue`);
            
            const queueItem: Partial<BridgeQueueItem> = {
                quote_id: quoteId,
                smart_deposit_address: smartDepositAddress,
                deposit_chain: depositChain,
                status: 'QUEUED',
                retry_count: 0,
                created_at: new Date()
            };

            const result = await this.insertData('bridge_queue', queueItem);
            
            if (result) {
                console.log(`✅ Bridge quote ${quoteId} added to queue successfully`);
                return true;
            } else {
                console.error(`❌ Failed to add bridge quote ${quoteId} to queue`);
                return false;
            }
        } catch (error: any) {
            console.error(`❌ Error adding bridge quote ${quoteId} to queue:`, error.message);
            return false;
        }
    }

    /**
     * Process bridge queue - check quotes that are ready (2+ minutes old)
     */
    async processBridgeQueue(): Promise<void> {
        try {
            console.log('🔄 Processing bridge queue...');

            // Get queued items that are at least 2 minutes old
            const queuedItems: any = await this.callRawQuery(`
                SELECT bq.*, q.transId, q.status as quote_status, q.created_on as quote_created_on
                FROM bridge_queue bq
                INNER JOIN quotes q ON bq.quote_id = q.transId
                WHERE bq.status = 'QUEUED'
                AND bq.created_at <= DATE_SUB(NOW(), INTERVAL 2 MINUTE)
                AND bq.retry_count < 5
                ORDER BY bq.created_at ASC
                LIMIT 10
            `);

            console.log(`🔄 Found ${queuedItems.length} bridge quotes ready for processing`);

            for (const item of queuedItems) {
                try {
                    await this.processSingleBridgeQueueItem(item);
                } catch (error: any) {
                    console.error(`❌ Error processing bridge queue item ${item.quote_id}:`, error.message);
                    
                    // Update retry count and status
                    await this.updateData('bridge_queue', `quote_id = '${item.quote_id}'`, {
                        retry_count: item.retry_count + 1,
                        status: item.retry_count >= 4 ? 'FAILED' : 'QUEUED',
                        last_checked_at: new Date(),
                        reason: error.message
                    });
                }
            }

            console.log('✅ Bridge queue processing completed');
        } catch (error: any) {
            console.error('❌ Error in processBridgeQueue:', error.message);
        }
    }

    /**
     * Process a single bridge queue item
     */
    private async processSingleBridgeQueueItem(item: any): Promise<void> {
        const { quote_id, smart_deposit_address, deposit_chain } = item;
        
        console.log(`🔄 Processing bridge queue item: ${quote_id}`);

        // Mark as processing
        await this.updateData('bridge_queue', `quote_id = '${quote_id}'`, {
            status: 'PROCESSING',
            last_checked_at: new Date()
        });

        // Check Rhino deposit address history
        const historyResponse = await Rhino.getDepositAddressHistory(smart_deposit_address, deposit_chain);

        if (!historyResponse.success) {
            console.error(`❌ Failed to get deposit history for ${quote_id}:`, historyResponse.error);
            
            // Update queue item with error
            await this.updateData('bridge_queue', `quote_id = '${quote_id}'`, {
                status: 'QUEUED', // Keep in queue for retry
                retry_count: item.retry_count + 1,
                reason: historyResponse.error || 'Failed to get deposit history'
            });
            return;
        }

        const historyData = historyResponse.data as RhinoDepositHistoryResponse;
        console.log(`🔄 Got deposit history for ${quote_id}, found ${historyData.bridges?.length || 0} bridges`);

        // Check if any bridge was rejected
        const rejectedBridge = historyData.bridges?.find(bridge => bridge._tag === 'rejected');
        if (rejectedBridge) {
            console.log(`❌ Bridge rejected for quote ${quote_id}:`, rejectedBridge.reason);
            
            // Update quote status to PENDING_REVERSAL
            await this.updateData('quotes', `transId = '${quote_id}'`, {
                status: 'PENDING_REVERSAL',
                reason: `Bridge rejected: ${rejectedBridge.reason || 'Unknown reason'}`
            });

            // Remove from queue
            await this.updateData('bridge_queue', `quote_id = '${quote_id}'`, {
                status: 'COMPLETED',
                reason: `Bridge rejected: ${rejectedBridge.reason || 'Unknown reason'}`
            });
            
            console.log(`✅ Quote ${quote_id} marked as PENDING_REVERSAL due to bridge rejection`);
            return;
        }

        // Check if any bridge was accepted and executed
        const executedBridge = historyData.bridges?.find(bridge => 
            bridge._tag === 'accepted' && 
            bridge.history?.state === 'EXECUTED'
        );

        if (executedBridge) {
            console.log(`✅ Bridge executed successfully for quote ${quote_id}`);
            
            // Quote should remain in PENDING status but remove from queue
            await this.updateData('bridge_queue', `quote_id = '${quote_id}'`, {
                status: 'COMPLETED',
                reason: 'Bridge executed successfully'
            });
            
            console.log(`✅ Quote ${quote_id} removed from bridge queue - bridge executed successfully`);
            return;
        }

        // No rejected or executed bridges found - keep in queue for next check
        console.log(`🔄 No final status found for quote ${quote_id} - keeping in queue`);
        
        await this.updateData('bridge_queue', `quote_id = '${quote_id}'`, {
            status: 'QUEUED',
            retry_count: item.retry_count + 1,
            last_checked_at: new Date(),
            reason: 'Bridge still processing'
        });
    }

    /**
     * Clean up old completed/failed queue items (older than 24 hours)
     */
    async cleanupOldQueueItems(): Promise<void> {
        try {
            console.log('🧹 Cleaning up old bridge queue items...');
            
            await this.callRawQuery(`
                DELETE FROM bridge_queue
                WHERE status IN ('COMPLETED', 'FAILED')
                AND created_at <= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            `);
            
            console.log(`🧹 Cleaned up old bridge queue items`);
        } catch (error: any) {
            console.error('❌ Error cleaning up bridge queue:', error.message);
        }
    }

}

export default new BridgeQueueService();
