
import Model from "../helpers/model";
import { get } from "../helpers/httpRequest";
import MudaPayment from "../helpers/MudaPayment";
import { WebhookSender } from "../helpers/webhookSender";

// Interface for polling response format
interface PollingResponse {
    statusCode: number;    // 200, 400, 202, 203
    transStatus: string;   // "SUCCESS", "FAILED", "PENDING", "ONHOLD"
    description: string;   // Human-readable description
    data: any;            // Original response data
}



class PollingPayoutsService extends Model {

    public async checkPendingDirectPayouts() {
        const pendingTransactions: any = await this.callRawQuery(
            "SELECT * FROM quotes WHERE (pay_in_status = 'SUCCESSFUL' and status = 'PENDING') AND transaction_type='off_ramp' AND created_on <= DATE_SUB(NOW(), INTERVAL 1 MINUTE)"
        );
        for (const transaction of pendingTransactions) {
            const provider_id = transaction.provider_id;
            const receive_currency = transaction.receive_currency;
            this.handlePollingPayout(provider_id, receive_currency, transaction.transId);
        }

        console.log(`Found ${pendingTransactions.length} pending direct payouts to check`);
    }

    public async handlePollingPayout(provider_id: string, receive_currency: string, transactionId: string) {
        if (provider_id == "1" && receive_currency == "UGX") {
            const result = await this.mudaPaymentLog(transactionId);
        }
    }


    public async mudaPaymentLog(transactionId: string) {
        const transaction = await MudaPayment.getTransactionById(transactionId);
        console.log("transaction", transaction);
        const status = transaction.status;
        if (status == 200) {
            console.log("transaction.data", transaction.data);
            const pay_out_status = transaction.data.status;
            if (pay_out_status == "SUCCESS") {
                this.updateFinalTransaction(transactionId, 'SUCCESSFUL', 'SUCCESSFUL', "");
                await WebhookSender.send(transactionId);
            } else if (pay_out_status == "FAILED") {
                this.updateFinalTransaction(transactionId, 'AWAITING_REVERSAL', 'FAILED', "");
                await WebhookSender.send(transactionId);
            }
        } else {
            this.updateFinalTransaction(transactionId, 'FAILED', 'FAILED', "");
            await WebhookSender.send(transactionId);
        }
    }
}
export default PollingPayoutsService;

