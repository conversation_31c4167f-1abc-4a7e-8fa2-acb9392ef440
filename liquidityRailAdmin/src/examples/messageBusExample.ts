// /**
//  * Simple Message Bus Usage Examples for Liquidity Rail Admin
//  * Demonstrates how to use the MessageBus for basic transaction events
//  */

// import MessageBus, { 
//   createTransactionEvent,
//   TransactionEvent
// } from '../utils/messageBus';
// import { RABBITMQ_CONFIG } from '../config/messageBus.config';

// /**
//  * Example 1: Basic Message Bus Setup and Connection
//  */
// export async function basicMessageBusExample() {
//   console.log('=== Basic Message Bus Example ===');
  
//   // Initialize message bus with configuration
//   const messageBus = new MessageBus(RABBITMQ_CONFIG);
  
//   try {
//     // Connect to RabbitMQ
//     await messageBus.connect();
//     console.log('✅ Connected to RabbitMQ');
    
//     // Set up event listeners
//     messageBus.on('connected', () => console.log('🎉 Message bus connected'));
//     messageBus.on('disconnected', () => console.log('🔌 Message bus disconnected'));
//     messageBus.on('event_published', (event: TransactionEvent) => console.log('📤 Event published:', event.transactionId));
    
//     // Close connection
//     await messageBus.close();
//     console.log('✅ Connection closed');
    
//   } catch (error) {
//     console.error('❌ Error:', error);
//   }
// }

// /**
//  * Example 2: Publishing Transaction Events
//  */
// export async function publishTransactionEventsExample() {
//   console.log('\n=== Publishing Transaction Events Example ===');
  
//   const messageBus = new MessageBus(RABBITMQ_CONFIG);
  
//   try {
//     await messageBus.connect();
//     console.log('✅ Connected to RabbitMQ');
    
//     // Create and publish transaction events
//     const pendingEvent = createTransactionEvent('tx123', 'pending', 100.0);
//     await messageBus.publishTransaction(pendingEvent);
//     console.log('✅ Pending transaction event published');
    
//     const completedEvent = createTransactionEvent('tx123', 'completed', 100.0);
//     await messageBus.publishTransaction(completedEvent);
//     console.log('✅ Completed transaction event published');
    
//     const failedEvent = createTransactionEvent('tx124', 'failed', 50.0);
//     await messageBus.publishTransaction(failedEvent);
//     console.log('✅ Failed transaction event published');
    
//     await messageBus.close();
    
//   } catch (error) {
//     console.error('❌ Error:', error);
//   }
// }

// /**
//  * Example 3: Consuming Messages from Queue
//  */
// export async function consumeMessagesExample() {
//   console.log('\n=== Consuming Messages Example ===');
  
//   const messageBus = new MessageBus(RABBITMQ_CONFIG);
  
//   try {
//     await messageBus.connect();
//     console.log('✅ Connected to RabbitMQ');
    
//     // Consume messages from queue
//     await messageBus.consumeMessages((msg) => {
//       try {
//         const eventData = JSON.parse(msg.content.toString());
//         console.log('📥 Received transaction event:', {
//           transactionId: eventData.transactionId,
//           status: eventData.status,
//           amount: eventData.amount,
//           timestamp: eventData.timestamp
//         });
        
//         // Process the event (e.g., update database, send notifications)
//         processTransactionEvent(eventData);
        
//         // Acknowledge the message
//         messageBus.ackMessage(msg);
        
//       } catch (error) {
//         console.error('❌ Error processing message:', error);
//         // Reject the message and don't requeue
//         messageBus.nackMessage(msg, false);
//       }
//     });
    
//     console.log('✅ Started consuming messages from queue');
//     console.log('⏳ Waiting for messages... (Press Ctrl+C to stop)');
    
//     // Keep the process running to consume messages
//     process.on('SIGINT', async () => {
//       console.log('\n🛑 Stopping message consumption...');
//       await messageBus.close();
//       process.exit(0);
//     });
    
//   } catch (error) {
//     console.error('❌ Error:', error);
//   }
// }

// // Helper function for processing events
// function processTransactionEvent(eventData: TransactionEvent) {
//   console.log('🔄 Processing transaction:', eventData.transactionId);
//   // Implement your business logic here:
//   // - Update database
//   // - Send notifications
//   // - Log for monitoring
// }

// /**
//  * Main function to run all examples
//  */
// export async function runAllExamples() {
//   try {
//     await basicMessageBusExample();
//     await publishTransactionEventsExample();
    
//     console.log('\n🎉 All examples completed successfully!');
//     console.log('\nTo test message consumption, run:');
//     console.log('await consumeMessagesExample();');
    
//   } catch (error) {
//     console.error('❌ Error running examples:', error);
//   }
// }

// // Export for use in other files
// export default {
//   basicMessageBusExample,
//   publishTransactionEventsExample,
//   consumeMessagesExample,
//   runAllExamples
// };
