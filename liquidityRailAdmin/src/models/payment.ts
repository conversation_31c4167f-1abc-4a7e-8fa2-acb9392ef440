import Model from "../helpers/model";
import CompanyServices from '../models/accounts';

const companyServices = new CompanyServices();
class Payment extends Model {

    constructor() {
        super();
    }

    async getCompanyTransactions(data: any) {
        const { companyId, page } = data;
        const limit = 30;
        try {
            const offset = (page - 1) * limit;

            const query = `
            SELECT * FROM quotes 
            WHERE company_id = '${companyId}' 
            ORDER BY created_at DESC 
            LIMIT ${limit} OFFSET ${offset}
        `;


            const transactions: any = await this.callQuerySafe(query, [])


            const total = transactions.length;
            const totalPages = Math.ceil(total / limit);

            return {
                success: true,
                data: {
                    transactions,
                    pagination: {
                        currentPage: page,
                        totalPages,
                        totalItems: total,
                        itemsPerPage: limit
                    }
                }
            };
        } catch (error) {
            console.error('Error fetching company transactions:', error);
            return {
                success: false,
                message: 'Failed to fetch transactions'
            };
        }
    }


    async getQuote(data: any) {
        const { quoteId } = data;
        const quote = await this.selectDataQuerySafe("quote_log", { quote_id: quoteId });
        if (quote.length === 0) {
            return this.makeResponse(404, "Quote not found");
        }
        return this.makeResponse(200, "success", quote[0]);
    }
    
    async requestPayment(data: any) {
        try {

            const getQuote = await this.getQuote(data.quoteId)
            const quoteId = getQuote.data.quote_id
            const quoteInfo = getQuote.data
            const {
                id,
                provider,
                providerQuoteId,
                providerId,
                fee,
                from_currency,
                to_currency,
                fiatAmount,
                provider_service_id,
                toAmount } = quoteInfo

            const send_amount = toAmount
            const receive_currency = to_currency
            const provider_id = providerId
            const ex_rate = fiatAmount
            const receiver_address = id
            const send_asset = from_currency

            const sending_address = "exchange"
            const source = "exchange"

            const { chain, company_id, payment_method_id } = data
            const transId = this.getRandomString()
            let evmAddress = ""
            let privateKey = ""


            const providerInfo: any = await this.callQuerySafe("SELECT * FROM service_providers WHERE provider_service_id = ?", [provider_service_id])
            if (providerInfo.length == 0) {
                return this.makeResponse(203, "Invalid provider service id");
            }
            const service_id = providerInfo[0].service_id
            const max_amount = providerInfo[0].max_amount
            const min_amount = providerInfo[0].min_amount

            if (send_amount < min_amount) {
                return this.makeResponse(203, `Min quote amount is ${min_amount}`);
            }
            if (send_amount > max_amount) {
                return this.makeResponse(203, `Maximum quote amount is ${max_amount}`);
            }

            const paymentMethod: any = await this.getPaymentMethodById(payment_method_id)


            if (paymentMethod.length == 0) {
                return this.makeResponse(203, "Enter a valid account number");

            }
            const { type, phone_number, country_code, network, account_name, account_number: pm_account_number, bank_code: pm_bank_code, bank_name: pm_bank_name } = paymentMethod[0];
            let account_number = phone_number;
            let user_account_name = "";
            let bank_code = "";
            let bank_name = "";

            if (type === "bank") {
                account_number    = pm_account_number;
                bank_code         = pm_account_number;
                bank_code         = pm_bank_code;
                bank_name         = pm_bank_name;
                user_account_name = account_name;
            }


            if (send_asset == "USDC") {
                //  evmAddress = getprovider[0]['address'];
            }


            let externalRefId = providerQuoteId
            const addressInfo: any = await this.generate_address(provider_id, send_asset, chain,transId)
            const { keypair, memo } = addressInfo

            if (keypair) {
                evmAddress = keypair
                externalRefId = memo
            } else {
                return this.makeResponse(203, "Failed to generate address");
            }

            // Generate new send amount if needed
            const new_send_amount = await this.generateSendAmount(send_asset, send_amount);
            //  const new_send_amount = source == "wallet" ? send_amount : await this.generateSendAmount(send_asset, send_amount);
            const newTransaction = {
                company_id,
                provider_id,
                transId,
                send_asset,
                send_amount: new_send_amount,
                receive_currency,
                receive_amount: fiatAmount,
                ex_rate,
                account_number,
                service_id,
                payable_amount: fiatAmount,
                bank_name: bank_name,
                bank_code: bank_code,
                account_name: user_account_name,
                sending_address,
                fee: fee,
                fee_currency: send_asset,
                receiver_address: evmAddress,
                status: 'PENDING',
                provider_ref_id: externalRefId,
                provider_address: evmAddress,
                provider_memo: "",
                payment_method_id
            };
            console.log(`newTransaction`, newTransaction)


            const paymentInfo = {
                provider_address: evmAddress,
                provider_memo: "",
                provider_ref_id: externalRefId,
                payment_method_id,
                trans_id: transId,
                status: "PENDING",
                quote_id: quoteId,
                send_asset,
                send_amount: new_send_amount,
                receive_currency,
                receive_amount: fiatAmount,
                ex_rate,
                account_number: account_number,
                account_name: user_account_name,
                service_id: service_id
            }

            const insertedTransaction = await this.insertData('quotes', newTransaction);
            if (insertedTransaction == false) {
                return this.makeResponse(300, "Transaction not saved");

            }
            console.log('Inserted Transaction ID:', insertedTransaction);

            return this.makeResponse(200, "Transaction records saved",paymentInfo);

        } catch (error: any) {
            console.error('Error inserting transaction:', error);
        }
        return this.makeResponse(400, "Error generating an invoice");
    }

    async generateInvoice(data: any) {
        try {
            console.log(data)
            const { company_id, amount, currency, paymentReference, description, callbackUrl, successRedirectUrl, cancelRedirectUrl } = data;

            // Validate amount
            if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
                return this.makeResponse(400, "Invalid amount. Amount must be a positive number.");
            }

            // Validate currency
            if (!currency || currency.trim() === '') {
                return this.makeResponse(400, "Currency is required and cannot be empty.");
            }

            // Validate callback URL
            if (!callbackUrl || callbackUrl.trim() === '') {
                return this.makeResponse(400, "Callback URL is required and cannot be empty.");
            }

            const quoteId = this.getRandomString()
            const status = "PENDING";
            const NEW_QUOTE = { quoteId, company_id, amount, currency, paymentReference, description, callbackUrl, successRedirectUrl, cancelRedirectUrl, status };
            const INSERTED_QUOTE = await this.insertData('quotes', NEW_QUOTE);
            return this.makeResponse(200, "Payment quotation added successfully", { "quoteId": quoteId });
        } catch (error) {
            console.error('Error generating payment quote:', error);
            return {
                success: false,
                message: 'Failed to generate payment quote'
            };
        }
    }


}

export default Payment;