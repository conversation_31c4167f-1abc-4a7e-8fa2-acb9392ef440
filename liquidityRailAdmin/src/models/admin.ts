import { QueryInterface } from "sequelize";
import Model from "../helpers/model";
import axios from 'axios';

class Accounts extends Model {


  async updateproviderAddress(data: any) {
    try {
        const { asset, company_id, address, memo } = data;
        
        // Input validation
        if (!asset || !company_id || !address) {
            return this.makeResponse(400, "Missing required fields");
        }
        
        const newAsset = { provider_id: company_id, asset, memo, address };
        const exists: any = await this.selectDataQuerySafe("provider_addresses", { 
            provider_id: company_id, 
            asset, 
            address, 
            status: 'active' 
        });
        
        if (exists.length == 0) {
            return this.makeResponse(203, "Address doesn't exists");
        }
        const id = exists[0].id
        //    const updatedService = await this.updateData('provider_addresses', `id = '${id}'`, newAsset);
        const updatedService = await this.insertData('provider_addresses', newAsset);
        return this.makeResponse(200, "Service updated successfully", updatedService);
    } catch (error) {
        console.error("Error in updateService:", error);
        return this.makeResponse(203, "Error updating");
    }
}
  async deactivateAddress(data: any) {
    try {
        const { asset, company_id, address } = data;
        const newAsset = { status: 'inactive' };
        const exists: any = await this.callQuerySafe(`select * from provider_addresses where provider_id=? AND asset=? and address=? and status='active'`, [company_id, asset, address])
        if (exists.length == 0) {
            return this.makeResponse(203, "Address doesn't exists");
        }
        const id = exists[0].id
        const updatedService = await this.updateData('provider_addresses', `id = '${id}'`, newAsset);
        return this.makeResponse(200, "Service updated successfully", updatedService);
    } catch (error) {
        console.error("Error in updateService:", error);
        return this.makeResponse(203, "Error updating");
    }
}

  async getAllTransactionCharges(data: any): Promise<any> {
    const PAGE: number = Number(data?.page) || 1;
    const LIMIT = data?.limit || 14;
    const OFFSET = (PAGE - 1) * LIMIT;
    const SEARCH = data?.search?.value || '';

    let whereClause = '';
    const conditions = [];

    if (SEARCH) {
      conditions.push(`(
        chain_id  LIKE '%${SEARCH}%' OR
        charge_type LIKE '%${SEARCH}%'
      )`);
    }

    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`;
    }

    const QUERY = `
      SELECT fee.*, c.chain_code, c.chain
      FROM  lr_fees fee
      inner join chains c on fee.chain_id = c.id
      ${whereClause}
      ORDER BY fee.id DESC
      LIMIT ${LIMIT} OFFSET ${OFFSET}
    `;
    const COUNT_QUERY = `
      SELECT COUNT(*) as total 
      FROM  lr_fees
      inner join chains c on lr_fees.chain_id = c.id
      ${whereClause}
    `;

    const [items, countResult] = await Promise.all([
      this.callRawQuery(QUERY),
      this.callRawQuery(COUNT_QUERY)
    ]);

    const total = countResult[0]?.total || 0;
    const TOTAL_PAGES = Math.ceil(total / LIMIT);

    const REPORTS = {
      items,
      pagination: {
        current_page: PAGE,
        next_page: PAGE < TOTAL_PAGES ? PAGE + 1 : null,
        previous_page: PAGE > 1 ? PAGE - 1 : null,
        total_pages: TOTAL_PAGES,
        total_items: total,
        items_per_page: LIMIT
      }
    };

    return this.makeResponse(200, "Transactions charges fetched successfully", REPORTS);
  }


  async updateTransactionCharges(id: any, data: any): Promise<any> {
   
  try{

    const lrFees: any[] = await this.selectDataQuerySafe("lr_fees", { id });
    if(lrFees.length === 0){
      return this.makeResponse(203, "charge fee not known");
    }

    const CHARGE_TYPE = ['flat', 'percentage']
    if (!CHARGE_TYPE.includes(data.charge_type)) {
      return this.makeResponse(203, "charge type not supported");
    }

    const UPDATES: any = {
                          charge_type: data.charge_type,
                          muda_charge: data.muda_charge,
                          gas_fee: data.gas_fee,
                          // updated_by: data.userId,
                          updated_on: new Date().toISOString()
                      };
   await this.updateData('lr_fees', `id = '${id}'`, UPDATES);
   return this.makeResponse(200, "charge fee updated successfully");
  
  } catch (err: any) {
    
    console.log("Error processing request", err)
    return this.makeResponse(203, "Error processing request");
  }
 
}


  async getAllQuotesProfits(data: any): Promise<any> {
    // Remove all keys with empty values from data
    const cleanData = Object.entries(data || {}).reduce((acc, [key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        if (Array.isArray(value)) {
          if (value.length > 0 && value.some(v => v !== null && v !== undefined && v !== '')) {
            acc[key] = value.filter(v => v !== null && v !== undefined && v !== '');
          }
        } else {
          acc[key] = value;
        }
      }
      return acc;
    }, {} as any);

    const PAGE: number = Number(cleanData?.page) || 1;
    const LIMIT = cleanData?.limit || 8;
    const OFFSET = (PAGE - 1) * LIMIT;
    const SEARCH = cleanData?.search;
    const CURRENCY = cleanData?.currency;
    const PAY_IN_STATUS = cleanData?.pay_in_status ? (Array.isArray(cleanData.pay_in_status) ? cleanData.pay_in_status : [cleanData.pay_in_status]) : ['SUCCESSFUL', 'PENDING'];
    const STATUS = cleanData?.status || [];
    const PROVIDER_ID = cleanData?.provider_id || [];
    const SEND_ASSET = cleanData?.send_asset || cleanData?.currency || "";
    const RECEIVE_CURRENCY = cleanData?.receive_currency || [];
    const START_DATE = cleanData?.start_date || '';
    const END_DATE = cleanData?.end_date || '';
    const SORT_KEY = cleanData?.sort_key || '';
    const SORT_ORDER = cleanData?.sort_order || 'DESC';

    let whereClause = '';
    const conditions = [];

    if (SEARCH) {
      conditions.push(`(
                          q.transId LIKE '%${SEARCH}%' OR 
                          q.send_asset LIKE '%${SEARCH}%' OR 
                          q.receive_currency LIKE '%${SEARCH}%' OR
                          q.provider_address LIKE '%${SEARCH}%' OR
                          q.account_number LIKE '%${SEARCH}%' OR
                          q.hash LIKE '%${SEARCH}%' OR
                          q.company_id LIKE '%${SEARCH}%' OR
                          q.sending_address LIKE '%${SEARCH}%' OR 
                          q.receiver_address LIKE '%${SEARCH}%'
                        )`);
    }

    if (START_DATE && START_DATE !== '' && START_DATE !== null && START_DATE !== undefined) {
      conditions.push(`DATE(q.created_on) >= '${START_DATE}'`);
    }

    if (END_DATE && END_DATE !== '' && END_DATE !== null) {
      conditions.push(`DATE(q.created_on) <= '${END_DATE}'`);
    }

    if (Array.isArray(SEND_ASSET) && SEND_ASSET.length > 0) {
      const sendAssetConditions = SEND_ASSET.map(asset => `q.send_asset = '${asset}'`).join(' OR ');
      conditions.push(`(${sendAssetConditions})`);
    } else if (SEND_ASSET && SEND_ASSET !== '' && SEND_ASSET.length > 0) {
      conditions.push(`q.send_asset = '${SEND_ASSET}'`);
    }

    if (Array.isArray(RECEIVE_CURRENCY) && RECEIVE_CURRENCY.length > 0) {
      const receiveCurrencyConditions = RECEIVE_CURRENCY.map(currency => `q.receive_currency = '${currency}'`).join(' OR ');
      conditions.push(`(${receiveCurrencyConditions})`);
    } else if (RECEIVE_CURRENCY && RECEIVE_CURRENCY !== '' && RECEIVE_CURRENCY.length > 0) {
      conditions.push(`q.receive_currency = '${RECEIVE_CURRENCY}'`);
    }

    conditions.push(`q.status = 'SUCCESSFUL'`);

    if (Array.isArray(PROVIDER_ID) && PROVIDER_ID.length > 0) {
      const providerConditions = PROVIDER_ID.map(id => `q.provider_id = '${id}'`).join(' OR ');
      conditions.push(`(${providerConditions})`);
    } else if (PROVIDER_ID && PROVIDER_ID !== '' && PROVIDER_ID.length > 0) {
      conditions.push(`q.provider_id = '${PROVIDER_ID}'`);
    }

    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`;
    }

    let orderClause = '';
    if (SORT_KEY) {
      const validSortKeys = ['created_on', 'transId', 'send_asset', 'receive_currency', 'pay_in_status', 'status'];
      const validSortOrders = ['ASC', 'DESC'];

      if (validSortKeys.includes(SORT_KEY)) {
        const order = validSortOrders.includes(SORT_ORDER.toUpperCase()) ? SORT_ORDER.toUpperCase() : 'DESC';
        orderClause = `ORDER BY ${SORT_KEY} ${order}`;
      }
    } else {
      orderClause = 'ORDER BY q.created_on DESC';
    }

    const query = `
      SELECT q.*, ml.id as mudafeelog_id, 
      ml.muda_fee as muda_fee, ml.thirdparty_fee as thirdparty_fee, 
      ml.thirdparty_quote as thirdparty_quote, ml.thirdparty_rate as thirdparty_rate, 
      ml.blockchain_fee as blockchain_fee, ml.blockchain_fee_asset as blockchain_fee_asset,
      ml.rate as fee_log_rate, ml.created_at as fee_log_created_at
      FROM quotes q
      LEFT JOIN muda_fee_log ml ON q.transId = ml.quote_id
      ${whereClause}
      ${orderClause}
      LIMIT ${LIMIT} OFFSET ${OFFSET}
    `;

    const countQuery = `
      SELECT COUNT(*) as total FROM quotes q
      ${whereClause}
    `;

    const [items, countResult] = await Promise.all([
      this.callRawQuery(query),
      this.callRawQuery(countQuery)
    ]);

    const total = countResult[0]?.total || 0;
    const totalPages = total / LIMIT // Math.ceil();

    const reports = {
      items,
      pagination: {
        current_page: PAGE,
        next_page: PAGE < totalPages ? PAGE + 1 : null,
        previous_page: PAGE > 1 ? PAGE - 1 : null,
        total_pages: totalPages,
        total_items: total,
        items_per_page: LIMIT
      }
    };
    return this.makeResponse(200, "Profits reports fetched successfully", reports);
  }






  /**
   * Check if transaction quote exists for auto-swap
   */
  async checkTransactionQuoteExists(data: {
    clientId: string;
    fromAsset: string;
    toAsset: string;
  }): Promise<boolean> {
    try {
      const { clientId, fromAsset, toAsset } = data;

      // Check if there's an active quote for this client and asset pair
      const query = `
        SELECT COUNT(*) as count 
        FROM quotes 
        WHERE client_reference_id = ? 
        AND send_asset = ? 
        AND receive_currency = ? 
        AND status IN ('PENDING', 'INITIATED', 'RECEIVED')
        AND expires_at > NOW()
      `;

      const result = await this.callRawQuery(query, [clientId, fromAsset, toAsset]);
      const count = result[0]?.count || 0;

      console.log(`🔍 Quote check for ${clientId}: ${fromAsset} -> ${toAsset}: ${count > 0 ? 'EXISTS' : 'NOT FOUND'}`);
      
      return count > 0;

    } catch (error: any) {
      console.error('❌ Error checking transaction quote:', error);
      return false;
    }
  }

  async getAllQuotes(data: any): Promise<any> {
    // Remove all keys with empty values from data
    const cleanData = Object.entries(data || {}).reduce((acc, [key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        if (Array.isArray(value)) {
          if (value.length > 0 && value.some(v => v !== null && v !== undefined && v !== '')) {
            acc[key] = value.filter(v => v !== null && v !== undefined && v !== '');
          }
        } else {
          acc[key] = value;
        }
      }
      return acc;
    }, {} as any);

    const PAGE: number = Number(cleanData?.page) || 1;
    const LIMIT = cleanData?.limit || 14;
    const OFFSET = (PAGE - 1) * LIMIT;
    const SEARCH = cleanData?.search;
    const CURRENCY = cleanData?.currency;
    const PAY_IN_STATUS = cleanData?.pay_in_status ? (Array.isArray(cleanData.pay_in_status) ? cleanData.pay_in_status : [cleanData.pay_in_status]) : ['SUCCESSFUL', 'PENDING'];
    const STATUS = cleanData?.status || [];
    const PROVIDER_ID = cleanData?.provider_id || [];
    const SEND_ASSET = cleanData?.send_asset || [];
    const RECEIVE_CURRENCY = cleanData?.receive_currency || [];
    const START_DATE = cleanData?.start_date || '';
    const END_DATE = cleanData?.end_date || '';
    const SORT_KEY = cleanData?.sort_key || '';
    const SORT_ORDER = cleanData?.sort_order || 'DESC';

    let whereClause = '';
    const conditions = [];

    if (SEARCH) {
      conditions.push(`(
        p.transId LIKE '%${SEARCH}%' OR 
        p.provider_address LIKE '%${SEARCH}%' OR
        p.account_number LIKE '%${SEARCH}%' OR
        p.hash LIKE '%${SEARCH}%' OR
        p.company_id LIKE '%${SEARCH}%' OR
        p.sending_address LIKE '%${SEARCH}%' OR 
        p.receiver_address LIKE '%${SEARCH}%'
      )`);
    }

    // Always include pay_in_status filter for SUCCESSFUL or PENDING by default
    if (Array.isArray(PAY_IN_STATUS) && PAY_IN_STATUS.length > 0) {
      const statusConditions = PAY_IN_STATUS.map(status => `p.pay_in_status = '${status}'`).join(' OR ');
      conditions.push(`(${statusConditions})`);
    } else if (PAY_IN_STATUS) {
      conditions.push(`p.pay_in_status = '${PAY_IN_STATUS}'`);
    } else {
      // Default to SUCCESSFUL and PENDING if no pay_in_status provided
      conditions.push(`(p.pay_in_status = 'SUCCESSFUL' OR p.pay_in_status = 'PENDING')`);
    }

    if (START_DATE && START_DATE !== '' && START_DATE !== null && START_DATE !== undefined) {
      conditions.push(`DATE(p.created_on) >= '${START_DATE}'`);
    }

    if (END_DATE && END_DATE !== '' && END_DATE !== null) {
      conditions.push(`DATE(p.created_on) <= '${END_DATE}'`);
    }

    if (Array.isArray(SEND_ASSET) && SEND_ASSET.length > 0) {
      const sendAssetConditions = SEND_ASSET.map(asset => `p.send_asset = '${asset}'`).join(' OR ');
      conditions.push(`(${sendAssetConditions})`);

    } else if (SEND_ASSET && SEND_ASSET !== '' && SEND_ASSET.length > 0) {
      conditions.push(`p.send_asset = '${SEND_ASSET}'`);
    }

    if (Array.isArray(RECEIVE_CURRENCY) && RECEIVE_CURRENCY.length > 0) {
      const receiveCurrencyConditions = RECEIVE_CURRENCY.map(currency => `p.receive_currency = '${currency}'`).join(' OR ');
      conditions.push(`(${receiveCurrencyConditions})`);

    } else if (RECEIVE_CURRENCY && RECEIVE_CURRENCY !== '' && RECEIVE_CURRENCY.length > 0) {
      conditions.push(`p.receive_currency = '${RECEIVE_CURRENCY}'`);
    }

    if (Array.isArray(STATUS) && STATUS.length > 0) {
      const statusConditions = STATUS.map(status => `p.status = '${status}'`).join(' OR ');
      conditions.push(`(${statusConditions})`);

    } else if (STATUS && STATUS !== '' && STATUS.length > 0) {
      conditions.push(`p.status = '${STATUS}'`);
    }

    if (Array.isArray(PROVIDER_ID) && PROVIDER_ID.length > 0) {
      const providerConditions = PROVIDER_ID.map(id => `p.provider_id = '${id}'`).join(' OR ');
      conditions.push(`(${providerConditions})`);
    } else if (PROVIDER_ID && PROVIDER_ID !== '' && PROVIDER_ID.length > 0) {

      conditions.push(`p.provider_id = '${PROVIDER_ID}'`);
    }

    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`;
    }

    let orderClause = '';
    if (SORT_KEY) {
      const validSortKeys = ['created_at', 'transId', 'send_asset', 'receive_currency', 'pay_in_status', 'status'];
      const validSortOrders = ['ASC', 'DESC'];

      if (validSortKeys.includes(SORT_KEY)) {
        const order = validSortOrders.includes(SORT_ORDER.toUpperCase()) ? SORT_ORDER.toUpperCase() : 'DESC';
        orderClause = `ORDER BY p.${SORT_KEY} ${order}`;
      }
    } else {
      orderClause = 'ORDER BY p.created_ON DESC';
    }

    const query = `
      SELECT
        p.*,
        p.id AS quote_id,
        an.*,
        an.id AS payment_mtd_id,
        sp.*, 
        serv.*
      FROM quotes p 
      LEFT JOIN payment_methods an ON p.payment_method_id = an.payment_method_id
      LEFT JOIN service_providers sp ON p.provider_id = sp.provider_service_id
      LEFT JOIN services serv ON p.service_id = serv.service_id
      ${whereClause}
      ${orderClause}
      LIMIT ${LIMIT} OFFSET ${OFFSET}
    `;
    const countQuery = `
      SELECT COUNT(*) as total
      FROM quotes p 
      LEFT JOIN payment_methods an ON p.payment_method_id = an.payment_method_id
      LEFT JOIN service_providers sp ON p.provider_id = sp.provider_service_id
      LEFT JOIN services serv ON p.service_id = serv.service_id
      ${whereClause}
    `;  //, SUM(p.fee) as fees 

    const [items, countResult] = await Promise.all([
      this.callRawQuery(query),
      this.callRawQuery(countQuery)
    ]);

    const total = countResult[0]?.total || 0;
    const totalPages = Math.ceil(total / LIMIT);

    const groupedItems = items.map((item: any) => {
      const {
        quote_id, payment_method_id, kotani_customer_key, type, currency, phone_number,
        country_code, network, account_name, bank_name, bank_code,
        account_number, bank_address, bank_phone_number, bank_country,
        sort_code, swift_code, created_at: payment_created_at,
        updated_at: payment_updated_at,
        provider_service_id, service_id, provider_id, min_amount, max_amount,
        service_code, service_name, country, provider_type,
        ...quoteData
      } = item;

      return {
        ...quoteData,
        id: quote_id,
        payment_method: {
          id: payment_method_id,
          kotani_customer_key,
          type,
          currency,
          phone_number,
          country_code,
          network,
          account_name,
          bank_name,
          bank_code,
          account_number,
          bank_address,
          bank_phone_number,
          bank_country,
          sort_code,
          swift_code,
          created_at: payment_created_at,
          updated_at: payment_updated_at
        },
        service_provider: {
          provider_service_id,
          service_id,
          provider_id,
          min_amount,
          max_amount,
          service: {
            service_code,
            service_name,
            country,
            provider_type
          }
        }
      };
    });

    const reports = {
      items: groupedItems,
      pagination: {
        current_page: PAGE,
        next_page: PAGE < totalPages ? PAGE + 1 : null,
        previous_page: PAGE > 1 ? PAGE - 1 : null,
        total_pages: totalPages,
        total_items: total,
        items_per_page: LIMIT
      }
    };

    return this.makeResponse(200, "Transactions reports fetched successfully", reports);
  }

  async getAllTransactionFees(data: any): Promise<any> {
    // Remove all keys with empty values from data
    const cleanData = Object.entries(data || {}).reduce((acc, [key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        if (Array.isArray(value)) {
          if (value.length > 0 && value.some(v => v !== null && v !== undefined && v !== '')) {
            acc[key] = value.filter(v => v !== null && v !== undefined && v !== '');
          }
        } else {
          acc[key] = value;
        }
      }
      return acc;
    }, {} as any);

    const PAGE: number = Number(cleanData?.page) || 1;
    const LIMIT = cleanData?.limit || 14;
    const OFFSET = (PAGE - 1) * LIMIT;
    const SEARCH = cleanData?.search;
    const CURRENCY = cleanData?.currency;
    const PAY_IN_STATUS = cleanData?.pay_in_status ? (Array.isArray(cleanData.pay_in_status) ? cleanData.pay_in_status : [cleanData.pay_in_status]) : ['SUCCESSFUL', 'PENDING'];
    const STATUS = cleanData?.status || [];
    const PROVIDER_ID = cleanData?.provider_id || [];
    const SEND_ASSET = cleanData?.send_asset || [];
    const RECEIVE_CURRENCY = cleanData?.receive_currency || [];
    const START_DATE = cleanData?.start_date || '';
    const END_DATE = cleanData?.end_date || '';
    const SORT_KEY = cleanData?.sort_key || '';
    const SORT_ORDER = cleanData?.sort_order || 'DESC';

    let whereClause = '';
    const conditions = [];

    if (SEARCH) {
      conditions.push(`(
        transId LIKE '%${SEARCH}%' OR 
        send_asset LIKE '%${SEARCH}%' OR 
        receive_currency LIKE '%${SEARCH}%' OR
        provider_address LIKE '%${SEARCH}%' OR
        account_number LIKE '%${SEARCH}%' OR
        hash LIKE '%${SEARCH}%' OR
        company_id LIKE '%${SEARCH}%' OR
        sending_address LIKE '%${SEARCH}%' OR 
        receiver_address LIKE '%${SEARCH}%'
      )`);
    }

    // Always include pay_in_status filter for SUCCESSFUL or PENDING by default
    if (Array.isArray(PAY_IN_STATUS) && PAY_IN_STATUS.length > 0) {
      const statusConditions = PAY_IN_STATUS.map(status => `pay_in_status = '${status}'`).join(' OR ');
      conditions.push(`(${statusConditions})`);
    } else if (PAY_IN_STATUS) {
      conditions.push(`pay_in_status = '${PAY_IN_STATUS}'`);
    } else {
      // Default to SUCCESSFUL and PENDING if no pay_in_status provided
      conditions.push(`(pay_in_status = 'SUCCESSFUL' OR pay_in_status = 'PENDING')`);
    }

    if (START_DATE && START_DATE !== '' && START_DATE !== null && START_DATE !== undefined) {
      conditions.push(`DATE(created_on) >= '${START_DATE}'`);
    }

    if (END_DATE && END_DATE !== '' && END_DATE !== null) {
      conditions.push(`DATE(created_on) <= '${END_DATE}'`);
    }

    if (Array.isArray(SEND_ASSET) && SEND_ASSET.length > 0) {
      const sendAssetConditions = SEND_ASSET.map(asset => `send_asset = '${asset}'`).join(' OR ');
      conditions.push(`(${sendAssetConditions})`);
    } else if (SEND_ASSET && SEND_ASSET !== '' && SEND_ASSET.length > 0) {
      conditions.push(`send_asset = '${SEND_ASSET}'`);
    }

    if (Array.isArray(RECEIVE_CURRENCY) && RECEIVE_CURRENCY.length > 0) {
      const receiveCurrencyConditions = RECEIVE_CURRENCY.map(currency => `receive_currency = '${currency}'`).join(' OR ');
      conditions.push(`(${receiveCurrencyConditions})`);
    } else if (RECEIVE_CURRENCY && RECEIVE_CURRENCY !== '' && RECEIVE_CURRENCY.length > 0) {
      conditions.push(`receive_currency = '${RECEIVE_CURRENCY}'`);
    }

    if (Array.isArray(STATUS) && STATUS.length > 0) {
      const statusConditions = STATUS.map(status => `status = '${status}'`).join(' OR ');
      conditions.push(`(${statusConditions})`);
    } else if (STATUS && STATUS !== '' && STATUS.length > 0) {
      conditions.push(`status = '${STATUS}'`);
    }

    if (Array.isArray(PROVIDER_ID) && PROVIDER_ID.length > 0) {
      const providerConditions = PROVIDER_ID.map(id => `provider_id = '${id}'`).join(' OR ');
      conditions.push(`(${providerConditions})`);
    } else if (PROVIDER_ID && PROVIDER_ID !== '' && PROVIDER_ID.length > 0) {
      conditions.push(`provider_id = '${PROVIDER_ID}'`);
    }

    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`;
    }

    let orderClause = '';
    if (SORT_KEY) {
      const validSortKeys = ['created_on', 'transId', 'send_asset', 'receive_currency', 'pay_in_status', 'status'];
      const validSortOrders = ['ASC', 'DESC'];

      if (validSortKeys.includes(SORT_KEY)) {
        const order = validSortOrders.includes(SORT_ORDER.toUpperCase()) ? SORT_ORDER.toUpperCase() : 'DESC';
        orderClause = `ORDER BY ${SORT_KEY} ${order}`;
      }
    } else {
      orderClause = 'ORDER BY created_on DESC';
    }

    const query = `
      SELECT * FROM quotes
      ${whereClause}
      ${orderClause}
      LIMIT ${LIMIT} OFFSET ${OFFSET}
    `;
    const countQuery = `
      SELECT COUNT(*) as total FROM quotes
      ${whereClause}
    `;

    const [items, countResult] = await Promise.all([
      this.callRawQuery(query),
      this.callRawQuery(countQuery)
    ]);

    const total = countResult[0]?.total || 0;
    const totalPages = Math.ceil(total / LIMIT);

    const reports = {
      items,
      pagination: {
        current_page: PAGE,
        next_page: PAGE < totalPages ? PAGE + 1 : null,
        previous_page: PAGE > 1 ? PAGE - 1 : null,
        total_pages: totalPages,
        total_items: total,
        items_per_page: LIMIT
      }
    };

    return this.makeResponse(200, "Transaction fees reports fetched successfully", reports);
  }

  async getAllClients(data: any): Promise<any> {
    const PAGE: number = Number(data?.page) || 1;
    const LIMIT = data?.limit || 14;
    const OFFSET = (PAGE - 1) * LIMIT;
    const SEARCH = data?.search?.value || '';
    const CURRENCY = data?.currency || '';

    let whereClause = '';
    const conditions = [];

    if (SEARCH) {
      conditions.push(`(
        name LIKE '%${SEARCH}%' OR 
        business_name LIKE '%${SEARCH}%' OR 
        email LIKE '%${SEARCH}%' OR
        phone LIKE '%${SEARCH}%'
      )`);
    }

    if (CURRENCY) {
      conditions.push(`currencies LIKE '%${CURRENCY}%'`);
    }

    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`;
    }

    const COLUMNS = 'company_id, name, business_name, email, phone, currencies, assets, country, user_type, kyc_status, email_verified';
    const query = `
      SELECT ${COLUMNS}
      FROM company_accounts
      ${whereClause}
      ORDER BY company_id DESC
      LIMIT ${LIMIT} OFFSET ${OFFSET}
    `;
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM company_accounts
      ${whereClause}
    `;

    const [items, countResult] = await Promise.all([
      this.callRawQuery(query),
      this.callRawQuery(countQuery)
    ]);

    const total = countResult[0]?.total || 0;
    const totalPages = Math.ceil(total / LIMIT);

    const reports = {
      items,
      pagination: {
        current_page: PAGE,
        next_page: PAGE < totalPages ? PAGE + 1 : null,
        previous_page: PAGE > 1 ? PAGE - 1 : null,
        total_pages: totalPages,
        total_items: total,
        items_per_page: LIMIT
      }
    };

    return this.makeResponse(200, "Client reports fetched successfully", reports);
  }

  



  async getAllClientsTrades(data: any): Promise<any> {
    const PAGE: number = Number(data?.page) || 1;
    const LIMIT = data?.limit || 18;
    const OFFSET = (PAGE - 1) * LIMIT;
    const SEARCH = data?.search?.value || '';
    const CURRENCY = data?.currency || '';
    const START_DATE = data?.start_date || '';
    const END_DATE = data?.end_date || '';

    let whereClause = '';
    const conditions = [];
    let whereClause2 = '';
    const conditions2 = [];


    if (START_DATE && START_DATE !== '' && START_DATE !== null && START_DATE !== undefined) {
      conditions2.push(`DATE(created_on) >= '${START_DATE}'`);
    }

    if (END_DATE && END_DATE !== '' && END_DATE !== null) {
      conditions2.push(`DATE(created_on) <= '${END_DATE}'`);
    }


    if (SEARCH) {
      conditions.push(`(
        name LIKE '%${SEARCH}%' OR 
        business_name LIKE '%${SEARCH}%' OR 
        email LIKE '%${SEARCH}%' OR
        phone LIKE '%${SEARCH}%'
      )`);
    }

    if (CURRENCY) {
      conditions.push(`currencies LIKE '%${CURRENCY}%'`);
    }

    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`;
    }

    if (conditions2.length > 0) {
      whereClause2 = ` AND ${conditions2.join(' AND ')}`;
    }

    const COLUMNS = 'company_id, name, business_name, email, phone, currencies, assets, country, user_type, kyc_status, email_verified';
    const query = `
      SELECT ${COLUMNS},
      (
        SELECT 
          GROUP_CONCAT(
            CONCAT(
              '{"currency":"', t.send_asset, '","total_amount":', t.total, '}'
            )
          )
        FROM (
          SELECT send_asset, SUM(send_amount) AS total
          FROM quotes
          WHERE company_id = c.company_id ${whereClause2} AND status = 'SUCCESSFUL'
          GROUP BY send_asset
        ) t
      ) AS trade_summary
      FROM company_accounts c
      ${whereClause}
      ORDER BY c.company_id DESC
      LIMIT ${LIMIT} OFFSET ${OFFSET}
    `;


    const countQuery = `
      SELECT COUNT(*) as total 
      FROM company_accounts
      ${whereClause}
    `;

    const [items, countResult] = await Promise.all([
      this.callRawQuery(query),
      this.callRawQuery(countQuery)
    ]);

    const total = countResult[0]?.total || 0;
    const totalPages = Math.ceil(total / LIMIT);

    const reports = {
      items,
      pagination: {
        current_page: PAGE,
        next_page: PAGE < totalPages ? PAGE + 1 : null,
        previous_page: PAGE > 1 ? PAGE - 1 : null,
        total_pages: totalPages,
        total_items: total,
        items_per_page: LIMIT
      }
    };
    return this.makeResponse(200, "Client trades grouped by send assets fetched successfully", reports);
  }

  async getProviderCharges(data: any): Promise<any> {
    const { clientId } = data;


    const result = await this.callQuerySafe("SELECT * FROM lr_fees inner join chains c on lr_fees.chain_id = c.id", []);
    return this.makeResponse(200, "Provider charges fetched successfully", result);
  }

  async updateProviderCharge(data: any): Promise<any> {

    const { id, muda_charge, userId, charge_type, chain_id, gas_fee, created_on } = data;

    /*
    const  userId = "" //check if user is Super Admin
    if(userId !== "Super Admin"){
      return this.makeResponse(400, "You are not authorized to update this charge");
    }
*/


    const ALLOWED_FIELDS = ['muda_charge', 'charge_type', 'chain_id', 'gas_fee'];
  

    const UPDATE_INFO = {
      muda_charge,
      charge_type,
      gas_fee,
      updated_by: userId,
      updated_on: new Date().toISOString()
    }

    const result = await this.updateData("lr_fees", `id = ${id} `, UPDATE_INFO);

    return this.makeResponse(200, "Provider charge updated successfully", result);
  }
  async getAllProviders(data: any): Promise<any> {
    const PAGE: number = Number(data?.page) || 1;
    const LIMIT = data?.limit || 14;
    const OFFSET = (PAGE - 1) * LIMIT;
    const SEARCH = data?.search?.value || '';

    let whereClause = '';
    const conditions = [];

    if (SEARCH) {
      conditions.push(`(
        name LIKE '%${SEARCH}%' OR 
        approval_status LIKE '%${SEARCH}%' OR
        rates_endpoint LIKE '%${SEARCH}%'
      )`);
    }

    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`;
    }

    const query = `
      SELECT *
      FROM providers
      ${whereClause}
      ORDER BY provider_id DESC
      LIMIT ${LIMIT} OFFSET ${OFFSET}
    `;
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM providers
      ${whereClause}
    `;

    const [items, countResult] = await Promise.all([
      this.callRawQuery(query),
      this.callRawQuery(countQuery)
    ]);

    const total = countResult[0]?.total || 0;
    const TOTAL_PAGES = Math.ceil(total / LIMIT);

    const REPORTS = {
      items,
      pagination: {
        current_page: PAGE,
        next_page: PAGE < TOTAL_PAGES ? PAGE + 1 : null,
        previous_page: PAGE > 1 ? PAGE - 1 : null,
        total_pages: TOTAL_PAGES,
        total_items: total,
        items_per_page: LIMIT
      }
    };

    return this.makeResponse(200, "Providers report fetched successfully", REPORTS);
  }




  async getRailsServices(data: any): Promise<any> {
    const PAGE: number = Number(data?.page) || 1;
    const LIMIT = data?.limit || 14;
    const OFFSET = (PAGE - 1) * LIMIT;
    const SEARCH = data?.search?.value || '';
    const CURRENCY = data?.currency || '';

    let whereClause = '';
    const conditions = [];

    if (SEARCH) {
      conditions.push(`(
        s.service_name LIKE '%${SEARCH}%' OR 
        s.service_code LIKE '%${SEARCH}%'
      )`);
    }

    if (CURRENCY) {
      conditions.push(`s.currency = '${CURRENCY}'`);
    }

    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`;
    }

    const query = `
      SELECT p.*, s.* 
      FROM service_providers p 
      INNER JOIN services s ON p.service_id = s.service_id
      ${whereClause}
      ORDER BY p.provider_service_id DESC
      LIMIT ${LIMIT} OFFSET ${OFFSET}
    `;
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM service_providers p 
      INNER JOIN services s ON p.service_id = s.service_id
      ${whereClause}
    `;

    const [items, countResult] = await Promise.all([
      this.callRawQuery(query),
      this.callRawQuery(countQuery)
    ]);

    const total = countResult[0]?.total || 0;
    const TOTAL_PAGES = Math.ceil(total / LIMIT);

    const REPORTS = {
      items,
      pagination: {
        current_page: PAGE,
        next_page: PAGE < TOTAL_PAGES ? PAGE + 1 : null,
        previous_page: PAGE > 1 ? PAGE - 1 : null,
        total_pages: TOTAL_PAGES,

        total_items: total,
        items_per_page: LIMIT
      }
    };

    return this.makeResponse(200, "Services report fetched successfully", REPORTS);
  }

  async getProviderServices(data: any): Promise<any> {

    const PAGE: number = Number(data?.page) || 1;
    const LIMIT = data?.limit || 14;
    const OFFSET = (PAGE - 1) * LIMIT;
    const SEARCH = data?.search?.value || '';

    let whereClause = '';
    const conditions = [];

    if (SEARCH) {
      conditions.push(`(
        s.service_name LIKE '%${SEARCH}%' OR 
        s.rates_endpoint LIKE '%${SEARCH}%' OR 
        s.approval_status LIKE '%${SEARCH}%'
      )`);
    }

    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`;
    }

    const query = `
      SELECT p.*
      FROM providers p 
      ${whereClause}
      ORDER BY p.provider_id  DESC
      LIMIT ${LIMIT} OFFSET ${OFFSET}
    `;
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM providers p 
      ${whereClause}
    `;

    const [items, countResult] = await Promise.all([
      this.callRawQuery(query),
      this.callRawQuery(countQuery)
    ]);

    const total = countResult[0]?.total || 0;
    const TOTAL_PAGES = Math.ceil(total / LIMIT);

    const REPORTS = {
      items,
      pagination: {
        current_page: PAGE,
        next_page: PAGE < TOTAL_PAGES ? PAGE + 1 : null,
        previous_page: PAGE > 1 ? PAGE - 1 : null,
        total_pages: TOTAL_PAGES,
        total_items: total,
        items_per_page: LIMIT
      }
    };

    return this.makeResponse(200, "Services report fetched successfully", REPORTS);
  }

  async getProviderServices_(data: any): Promise<any> {
    
    const PAGE: number = Number(data?.page) || 1;
    const LIMIT = data?.limit || 14;
    const OFFSET = (PAGE - 1) * LIMIT;
    const SEARCH = data?.search?.value || '';

    let whereClause = '';
    const conditions = [];

    // if (SEARCH) {
    //   conditions.push(`(
    //     s.service_name LIKE '%${SEARCH}%' OR 
    //     s.service_code LIKE '%${SEARCH}%'
    //   )`);
    // }

    conditions.push(`s.provider_id = '${data?.clientId}'`);

    if (SEARCH) {
      conditions.push(`(
        s.service_name LIKE '%${SEARCH}%' OR 
        s.rates_endpoint LIKE '%${SEARCH}%' OR 
        s.approval_status LIKE '%${SEARCH}%'
      )`);
    }
    
    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`;
    }

    const query = `
      SELECT p.*
      FROM providers p 
      ${whereClause}
      ORDER BY p.provider_id  DESC
      LIMIT ${LIMIT} OFFSET ${OFFSET}
    `;
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM providers p 
      ${whereClause}
    `;

    const [items, countResult] = await Promise.all([
      this.callRawQuery(query),
      this.callRawQuery(countQuery)
    ]);

    const total = countResult[0]?.total || 0;
    const TOTAL_PAGES = Math.ceil(total / LIMIT);

    const REPORTS = {
      items,
      pagination: {
        current_page: PAGE,
        next_page: PAGE < TOTAL_PAGES ? PAGE + 1 : null,
        previous_page: PAGE > 1 ? PAGE - 1 : null,
        total_pages: TOTAL_PAGES,
        total_items: total,
        items_per_page: LIMIT
      }
    };


    return this.makeResponse(200, "Services provider fees  successfully", REPORTS);
  }

  async getProviderFees(data: any): Promise<any> {
    
    const PAGE: number = Number(data?.page) || 1;
    const LIMIT = data?.query?.limit || 14;
    const OFFSET = (PAGE - 1) * LIMIT;
    const SEARCH = data?.query?.search || '';
    let whereClause = '';
    const conditions = [];
    const QUERY = `SELECT p.* FROM service_providers p LEFT JOIN services s ON p.service_id = s.service_id WHERE p.provider_id = '${data?.body?.clientId}' ORDER BY p.provider_service_id DESC`;
    const items = await this.callRawQuery(QUERY);

    const REPORTS: any = {
      items
    };

    return this.makeResponse(200, `provider fees listing`, REPORTS);
  }


  async getProviderAFee(data: any): Promise<any> {
    
    const PAGE: number = Number(data?.page) || 1;
    const LIMIT = data?.query?.limit || 14;
    const OFFSET = (PAGE - 1) * LIMIT;
    const SEARCH = data?.query?.search || '';
    let whereClause = '';
    const conditions = [];
    const QUERY = `SELECT p.* FROM service_providers p LEFT JOIN services s ON p.service_id = s.service_id WHERE p.provider_id = '${data?.body?.clientId}' AND p.provider_service_id = '${data?.body?.providerFeeId}' LIMIT 1`;
    const items = await this.callRawQuery(QUERY);
    return this.makeResponse(200, "Services provider fee successfully", items[0]);
  }

}

export default Accounts;