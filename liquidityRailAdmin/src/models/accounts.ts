import Model from "../helpers/model";
import jwt from 'jsonwebtoken';
import crypto, { hash } from 'crypto';
import request from 'request';
import axios from 'axios';
import dotenv from 'dotenv';

import EmailSender from '../helpers/email.helper'
import kotaniPay from "../helpers/kotani";
import CNGNUtility from "../helpers/CNGNUtility";
import { Network } from "cngn-typescript-library";
import Ogateway from "../helpers/Ogateway";
import Quidax, { QuidaxWithdrawData } from "../helpers/Quidax";
import MudaPayment from "../helpers/MudaPayment";
import HoneyCoin from "../helpers/HoneyCoin"
import BlockchainVerifier from '../helpers/blockchainVerifier';
import { CreatePaymentIntentData, MudaProvider } from "../helpers/MudaProvider";
import { PasswordValidator } from '../utils/passwordValidator';
import BridgeQueueService from '../services/bridgeQueue.service';
import {
    WebhookPayload,
    CryptoReceivedWebhook,
    FiatSentWebhook,
    FiatReceivedWebhook,
    GeneralStatusWebhook,
    CryptoWebhookData,
    BankPayment,
    MobileMoneyPayment,
    PayoutRequest,
    cryptoPaymentMethod,
    PayoutResponse,
    Quote,
    LRProviderEvents,
    HoneyCoinWebhook,
    MudaWebhook
} from '../interfaces/webhook.interfaces';

// Type aliases for LR provider event data
type cryptoEvent = Partial<CryptoWebhookData> & { amount: string; asset_code: string; from_address: string; to_address: string; contract_address?: string; fee?: string };
type fiatEvent = { amount: string; currency: string;[key: string]: any };
import { WebhookFactory } from '../helpers/webhookFactory';
import { WebhookSender } from '../helpers/webhookSender';
import { ProviderService, RateRequest, RateResponse } from '../interfaces/rate.interfaces';
import { ChainInfo, PaymentInformation } from "../interfaces/quidax.interfaces";
import { HoneyCoinPaymentInformation } from "../interfaces/honeycoin.interfaces";
import { SaveQuoteRequest } from "../interfaces/quote.interfaces";


dotenv.config();
const mailer = new EmailSender()
const cngn = new CNGNUtility()
const mudaProvider = new MudaProvider(process.env.MUTA_API_KEY || "");

export interface PaymentMethod {
    payment_method_id: string;
    company_id: string;
    kotani_customer_key?: string | null;
    type: 'bank' | 'mobile_money';
    currency: string | null;
    phone_number: string | null;
    country_code: string | null;
    network: string | null;
    account_name: string;
    bank_name: string | null;
    bank_code: string | null;
    account_number: string;
    bank_address: string | null;
    bank_phone_number: string;
    bank_country: string | null;
    sort_code: string | null;
    swift_code?: string | null;
}

type QuoteResponse = {
    quote_id: string;
    status: string;
    receive_amount: number;
    send_asset: string;
    receive_currency: string;
    send_amount: string;
    ex_rate: number;
    fee: number;
    payment_method_id: string;
    expires_at: string;

    chainInfo?: {
        pay_in_address: string;
        pay_in_memo: string;
    };
}

class Accounts extends Model {

    private honeyCoin: HoneyCoin;

    // Provider support configuration - defines which assets each provider accepts
    private readonly PROVIDER_ASSET_SUPPORT: { [key: number]: string[] } = {
        2: ["USDT@BASE", "USDC@BASE"], // Kotani
        4: ["USDT@BSC", "USDC@BASE", "USDT@BASE", "USDC@BSC"], // Quidax
        6: ["USDC@BASE", "USDC@BSC", "USDT@BASE", "USDT@BSC"], // HoneyCoin
        1: ["USDT@BSC", "USDC@BASE", "USDT@BASE", "USDC@BSC"], // MUDA
        10: ["USDT@BASE", "USDC@BASE"] // Trading Service (provider 10)
    };

    // Chain name mapping for Rhino API compatibility
    private readonly RHINO_CHAIN_MAPPING: { [key: string]: string } = {
        'BSC': 'BINANCE',
        'BASE': 'BASE',
    };

    /**
     * Converts internal chain names to Rhino-compatible chain names
     * @param chain - Internal chain name (e.g., 'BSC', 'BASE', 'POLYGON')
     * @returns Rhino-compatible chain name (e.g., 'BINANCE', 'BASE', 'MATIC_POS')
     */
    private convertToRhinoChainName(chain: string): string {
        const upperChain = chain.toUpperCase();
        const rhinoChain = this.RHINO_CHAIN_MAPPING[upperChain];
        
        if (!rhinoChain) {
            console.warn(`⚠️ No Rhino chain mapping found for: ${chain}, using original value`);
            return upperChain;
        }
        
        console.log(`🔄 Chain mapping: ${chain} -> ${rhinoChain}`);
        return rhinoChain;
    }

    constructor() {
        super();
        this.honeyCoin = new HoneyCoin();
    }
    static async LogWebhook(data: any, eventType: string, provider: string) {
        const logData = {
            event_type: eventType,
            provider: provider,
            data: JSON.stringify(data)
        }
        await new Accounts().insertData("lg_received_webhooks", logData);
        return true;
    }



    async changePassword(data: any) {
        const { oldPassword, staff_id, newPassword } = data;

        // Validate new password strength using our secure policy
        const passwordValidation = this.validatePasswordPolicy(newPassword);
        if (!passwordValidation.isValid) {
            return this.makeResponse(400, "Password does not meet security requirements", {
                errors: passwordValidation.errors,
                strength: passwordValidation.strength,
                score: passwordValidation.score
            });
        }

        const hashedOldPassword = await this.hashPassword(oldPassword);
        const hashedNewPassword = await this.hashPassword(newPassword);

        const existingUser = await this.getLoggedInUser(staff_id, hashedOldPassword);
        if (existingUser.length == 0) {
            return this.makeResponse(401, "Auth error");
        }
        const email = existingUser[0].email
        const user_id = existingUser[0].user_id
        const first_name = existingUser[0].first_name
        const updates: any = {
            password: hashedNewPassword
        };
        await this.updateData('users', `user_id = '${user_id}'`, updates);
        this.sendEmail("CHANGE_PASSWORD", email, first_name);
        return this.makeResponse(200, "success");
    }

    async resetPasswordRequest(data: any) {
        try {
            const { email } = data;

            const existingUser = await this.getUserByEmail(email);
            if (existingUser.length == 0) {
                return this.makeResponse(404, "email not found");
            }


            const user_id = existingUser[0].company_id
            const first_name = existingUser[0].name
            const otp = await this.getOTP(email, user_id.toString());
            this.sendEmail("RESET_PASSWORD_REQUEST", email, first_name, otp);
            return this.makeResponse(200, "success");
        } catch (err) {
            console.log(err)
            return this.makeResponse(203, "Error processing request");

        }
    }

    //webhook from moralis
    async cryptoWebhook(data: any) {
        try {
            console.log("moralisData", data);

            const webhookPayload = await WebhookFactory.createCryptoReceived(data, 'moralis');
            const result = await this.processReceivedWebhook(webhookPayload);
            return this.makeResponse(200, "Webhook processed successfully", result);
        } catch (error) {
            console.error("Error in cryptoWebhook:", error);
            return this.makeResponse(203, "Error processing webhook");
        }
    }




    async resetPassword(data: any) {
        try {
            const { otp, email, newPassword } = data;

            // Validate new password strength using our secure policy
            const passwordValidation = this.validatePasswordPolicy(newPassword);
            if (!passwordValidation.isValid) {
                return this.makeResponse(400, "Password does not meet security requirements", {
                    errors: passwordValidation.errors,
                    strength: passwordValidation.strength,
                    score: passwordValidation.score
                });
            }

            const hashedNewPassword = await this.hashPassword(newPassword);

            const otpRs = await this.selectDataQuerySafe("user_otp", { email, otp });
            if (otpRs.length == 0) {
                return this.makeResponse(203, "OTP not found");
            }
            const user_id = otpRs[0]['user_id']

            const existingUser = await this.getUserByEmail(email);
            if (existingUser.length == 0) {
                return this.makeResponse(404, "User Id not found" + user_id);
            }
            const first_name = existingUser[0].name
            const updates: any = {
                password: hashedNewPassword
            };
            await this.updateDataSafe('company_accounts', { company_id: user_id }, updates);
            this.sendEmail("RESET_PASSWORD_COMPLETE", email, first_name);
            return this.makeResponse(200, "success");
        } catch (err) {
            console.log(err)
            return this.makeResponse(203, "Error processing request");

        }
    }




    async verifyCode(data: any) {
        const { code } = data;

        // Input validation
        if (!code || typeof code !== 'string') {
            return this.makeResponse(400, "Invalid OTP code");
        }

        const otpRs = await this.selectDataQuerySafe("user_otp", { otp: code });
        if (otpRs.length == 0) {
            return this.makeResponse(203, "code not found");
        }
        const user_id = otpRs[0].user_id
        const updates: any = {
            email_verified: 'yes'
        };

        // Use safe parameterized update
        await this.callQuerySafe(`UPDATE company_accounts SET email_verified = ? WHERE company_id = ?`, ['yes', user_id]);
        return this.makeResponse(200, "User email verified");

    }



    /**
     * Hash password using bcrypt (secure)
     * @param password - Plain text password
     * @returns Promise<string> - Hashed password
     */
    private async hashPassword(password: string): Promise<string> {
        return await this.generatePassword(password);
    }

    /**
     * Verify password against hash
     * @param password - Plain text password
     * @param hash - Hashed password
     * @returns Promise<boolean> - True if password matches
     */
    private async verifyPasswordHash(password: string, hash: string): Promise<boolean> {
        return await this.verifyPassword(password, hash);
    }

    async login(data: any) {
        const { email, password } = data;

        try {
            // First try to find user by email
            const users = await this.selectDataQuerySafe("company_accounts", { email });
            const user = users.length > 0 ? users[0] : null;

            if (!user) {
                return this.makeResponse(203, "User not found");
            }

            // Check if password is stored as bcrypt hash or old SHA256 encryption
            let isPasswordValid = false;

            // First try bcrypt verification
            try {
                isPasswordValid = await this.verifyPassword(password, user.password);
            } catch (error) {
                // If bcrypt fails, try old SHA256 decryption
                try {
                    const hash = crypto.createHash('sha256');
                    const oldHash = hash.update(password).digest('hex');
                    isPasswordValid = (oldHash === user.password);
                } catch (decryptError) {
                    console.error("Error verifying password:", decryptError);
                    isPasswordValid = false;
                }
            }

            if (!isPasswordValid) {
                return this.makeResponse(203, "Invalid credentials");
            }

            // Remove the password property from the user object
            if (user.hasOwnProperty('password')) {
                delete user.password;
            }
            let user_id = user.company_id

            console.log("user_id", user_id)
            const jwts: any = process.env.JWT_SECRET
            const token = jwt.sign({ user_id, company_id: user_id }, jwts, {
                expiresIn: 86400 // 24 hours
            });

            // Remove password hash from response - SECURITY FIX
            const { password: removedPassword, ...safeUser } = user;
            const response = { ...safeUser, jwt: token };
            return this.makeResponse(200, "Login successful", response);
        } catch (error) {
            console.error("Error in login:", error);
            return this.makeResponse(203, "Error logging in");
        }
    }


    // Create a new company account
    async addCompany(data: any) {
        try {
            // Destructure the fields from the payload
            const {
                first_name,
                last_name,
                business_name,
                phone_number,
                email,
                password,      // If you truly need a password
                passport,      // If you want a separate passport field
                account_type,
                country,
                payin_assets,
                transfer_types
            } = data;

            // Combine first_name + last_name
            const full_name = `${first_name} ${last_name}`.trim();

            console.log("DATA", data);

            // Validate required fields (adjust as needed)
            if (!full_name || !email || !password || !account_type) {
                return this.makeResponse(203, "Missing required fields");
            }

            // Check if email already exists
            const companies: any = await this.selectDataQuerySafe(
                "company_accounts",
                { email }
            );
            if (companies.length > 0) {
                return this.makeResponse(203, "Email already exists");
            }

            // Validate password strength using our secure policy
            const passwordValidation = this.validatePasswordPolicy(password);
            if (!passwordValidation.isValid) {
                return this.makeResponse(400, "Password does not meet security requirements", {
                    errors: passwordValidation.errors,
                    strength: passwordValidation.strength,
                    score: passwordValidation.score
                });
            }

            // Hash the password before saving using bcrypt
            const hashPassword = await this.hashPassword(password);

            const cId = this.generateRandom4DigitNumber()
            // Build the new company object for the 'company_accounts' table
            let newCompany: any = {
                company_id: cId,
                name: full_name,
                business_name,
                phone: phone_number,
                email,
                password: hashPassword,
                user_type: account_type.toLowerCase(),  // standardize
                country
            };



            // Insert the new record in 'company_accounts'
            const insertedCompany = await this.insertData("company_accounts", newCompany);
            console.log(`INSETING`, insertedCompany)

            // If the account is a provider, handle additional data
            if (account_type.toLowerCase() === "provider") {
                // 1) Insert into 'providers' table
                const providerRecord = {
                    name: business_name,
                    approval_status: "inactive",
                    country
                };
                // e.g., insertedProvider might contain the new provider's ID
                const insertedProvider = await this.insertData("providers", providerRecord);


                if (insertedProvider && insertedProvider.insertId && payin_assets?.length > 0) {
                    for (const asset of payin_assets) {
                        await this.insertData("providers_assets", {
                            provider_id: insertedProvider.insertId,
                            asset_type: "payin", // or use a dedicated column name if needed
                            asset: asset,   // rename columns to match your schema
                        });
                    }
                }


            }


            const otp = await this.getOTP(email, cId.toString());
            this.sendEmail("ACCOUNT_CREATION", email, first_name, otp);
            // this.sendEmail("ACCOUNT_CREATION", email, first_name);

            return this.makeResponse(200, "Company added successfully", insertedCompany);
        } catch (error) {
            console.error("Error in addCompany:", error);
            return this.makeResponse(203, "Error adding company");
        }
    }



    // Fetch all companies
    async getCompanies() {
        try {
            const companies: any = await this.selectDataQuerySafe('company_accounts', {});
            if (companies.length > 0) {
                return this.makeResponse(200, "Companies fetched successfully", companies);
            } else {
                return this.makeResponse(404, "No companies found");
            }
        } catch (error) {
            console.error("Error in getCompanies:", error);
            return this.makeResponse(203, "Error fetching companies");
        }
    }

    // Create a new service for a company
    async addService(data: any) {
        try {
            console.log(data)
            const { service_name, provider_name, country, chain, company_id, currency } = data;
            const newService = { service_name, provider_name, country, chain, company_id, currency };
            const insertedService = await this.insertData('services', newService);
            return this.makeResponse(200, "Service added successfully", insertedService);
        } catch (error) {
            console.error("Error in addService:", error);
            return this.makeResponse(203, "Error adding service");
        }
    }

    // Fetch services for a specific company
    async getServices(company_id: number) {
        try {
            const services: any = await this.selectDataQuerySafe("services", { company_id });
            if (services.length > 0) {
                return this.makeResponse(200, "Services fetched successfully", services);
            } else {
                return this.makeResponse(404, "Services not found for the given company");
            }
        } catch (error) {
            console.error("Error in getServices:", error);
            return this.makeResponse(203, "Error fetching services");
        }
    }


    async getAssets(query: string) {
        try {
            let services: any = [];
            if (query) {
                services = await this.callQuerySafe("SELECT * FROM accepted_assets WHERE type = ?", [query]);
            } else {
                services = await this.callRawQuery("SELECT * FROM accepted_assets");
            }
            if (services.length > 0) {
                return this.makeResponse(200, "assets fetched successfully", services);
            } else {
                return this.makeResponse(404, "assets not found for the given company");
            }
        } catch (error) {
            console.error("Error in assets:", error);
            return this.makeResponse(203, "Error fetching assets");
        }
    }



    // add a service
    async AcceptService(data: any) {
        try {
            const { company_id, service_id, max_amount } = data;
            const newAsset = { provider_id: company_id, service_id, max_amount, min_amount: 0, };
            const exists: any = await this.callQuerySafe(
                "SELECT * FROM service_providers WHERE provider_id = ? AND service_id = ?",
                [company_id, service_id]
            )
            if (exists.length > 0) {
                return this.makeResponse(200, "Address aready exists");
            }

            const insertedAsset = await this.insertData('service_providers', newAsset);
            return this.makeResponse(200, "Accepted asset added successfully", insertedAsset);
        } catch (error) {
            console.error("Error in addAcceptedAsset:", error);
            return this.makeResponse(203, "Error adding accepted asset");
        }
    }


    async getProviderInfo(company_id: any) {
        try {
            const exists: any = await this.callQuerySafe(
                "SELECT * FROM providers WHERE provider_id = ?",
                [company_id]
            )
            return this.makeResponse(200, "Updated successfully", exists);
        } catch (error) {
            console.error("Error in addAcceptedAsset:", error);
            return this.makeResponse(203, "Error adding accepted asset");
        }
    }
    async updatedRatesUrl(data: any) {
        try {
            const { company_id, rates_endpoint } = data;
            const newAsset = { rates_endpoint };

            const rate: any = await this.getConversionRate(1, "USDT", "USD", company_id)
            const postRate: any = parseFloat(rate).toFixed(2)
            if (postRate == 0) {
                return this.makeResponse(203, "Invalid resonse from the rates endpoint, please read the response format required");
            }
            const updatedService = await this.updateData('providers', `provider_id = '${company_id}'`, newAsset);
            return this.makeResponse(200, "Updated successfully");
        } catch (error) {
            console.error("Error in addAcceptedAsset:", error);
            return this.makeResponse(203, "Error adding accepted asset");
        }
    }

    async DisableService(data: any) {
        try {
            const { company_id, service_id } = data;
            const newAsset = { status: 'inactive' };
            const exists: any = await this.callQuerySafe(
                "SELECT * FROM service_providers WHERE provider_id = ? AND service_id = ? AND status = 'active'",
                [company_id, service_id]
            )
            if (exists.length == 0) {
                return this.makeResponse(200, "service not activated");
            }
            const id = exists[0].id

            const updatedService = await this.updateData('service_providers', `id = '${id}'`, newAsset);
            return this.makeResponse(200, "Updated successfully");
        } catch (error) {
            console.error("Error in addAcceptedAsset:", error);
            return this.makeResponse(203, "Error adding accepted asset");
        }
    }




    // Add an accepted asset for a service
    async addAcceptedAsset(data: any) {
        try {

            const { asset, company_id, address, memo, chain } = data;
            const newAsset = { provider_id: company_id, chain, asset, memo, address };
            const exists: any = await this.callQuerySafe(
                "SELECT * FROM provider_addresses WHERE provider_id = ? AND asset = ? AND status = 'active'",
                [company_id, asset]
            )
            if (exists.length > 0) {
                return this.makeResponse(200, "Address aready exists");
            }

            const insertedAsset = await this.insertData('provider_addresses', newAsset);
            return this.makeResponse(200, "Accepted asset added successfully", insertedAsset);
        } catch (error) {
            console.error("Error in addAcceptedAsset:", error);
            return this.makeResponse(203, "Error adding accepted asset");
        }
    }


    async getProviderAddresses(company_id: any) {
        const exists: any = await this.callQuerySafe(
            "SELECT * FROM provider_addresses WHERE provider_id = ? AND status = 'active'",
            [company_id]
        )
        return this.makeResponse(200, "failed", exists);
    }




    // Fetch all accepted assets for a service
    async getAcceptedAssets(service_id: number) {
        try {
            const assets: any = await this.selectDataQuerySafe("accepted_assets", { service_id });
            if (assets.length > 0) {
                return this.makeResponse(200, "Accepted assets fetched successfully", assets);
            } else {
                return this.makeResponse(404, "No accepted assets found for the given service");
            }
        } catch (error) {
            console.error("Error in getAcceptedAssets:", error);
            return this.makeResponse(203, "Error fetching accepted assets");
        }
    }

    // Update a service's details
    async updateService(service_id: number, newData: any) {
        try {
            const updatedService = await this.updateData('services', `service_id = '${service_id}'`, newData);
            return this.makeResponse(200, "Service updated successfully", updatedService);
        } catch (error) {
            console.error("Error in updateService:", error);
            return this.makeResponse(203, "Error updating");
        }
    }

    async save_log(quote_id: string, hash: string, asset_code: string, asset_amount: number, currency: string, send_amount: string, account_number: string, asset_issuer: string, data: any) {
        try {
            const sql = {
                hash: hash,
                quote_id,
                received_asset: asset_code,
                amount: asset_amount,
                account_number,
                send_amount: send_amount,
                send_currency: currency,
                contract_address: asset_issuer,
                req_body: data
            };
            console.log(`insertData`)

            const result = await this.insertData("pay_log", sql);
            console.log(`Insert Result: ${result}`); // Debugging line
            return true;
        } catch (error) {
            console.log(`Error in save_log: ${error}`);
            return false;
        }
    }

    async update_log(hash: string, data: any, status: "", ext_trans_id: string = '') {
        try {
            const sql = {
                resp_body: data,
                ext_trans_id,
                status
            };

            const condition = `hash='${hash}'`; // Add quotes around the hash value

            const result = await this.updateData("pay_log", condition, sql);
            console.log(`Update Result: ${result}`); // Debugging line
            return true;
        } catch (error) {
            console.log(`Error in update_log: ${error}`);
            return false;
        }
    }


    async callback(data: any) {
        try {
            console.log(`callBack`, data)
            const { transId, hash, status, event_type } = data;
            const allowedStatus = ["SUCCESSFUL", "INITIATED", "PENDING", "ONHOLD"]
            if (!allowedStatus.includes(status)) {
                return this.makeResponse(203, "Status not allowed");
            }

            //query the hash to validate that it was paid depending on the chain
            const transInfo: any = await this.selectDataQuerySafe("quotes", { transId })
            if (transInfo.length == 0) {
                return this.makeResponse(404, "transaction not found");
            }
            const singleTransaction = transInfo[0]
            const tStatus = singleTransaction.status
            const pay_in_status = singleTransaction.pay_in_status

            if (tStatus == "SUCCESSFUL" || tStatus == "EXPIRED" || tStatus == "FAILED") {
                return this.makeResponse(203, "transaction status is " + tStatus);
            }


            if (event_type === 'CHAIN_RECEIVED') {
                if (pay_in_status != "PENDING") {
                    return this.makeResponse(203, "transaction pay in status is " + pay_in_status);
                }
                await this.updateData('quotes', `transId = '${transId}'`, { "pay_in_status": status, "hash": hash });
            } else if (event_type === 'PAYOUT') {
                await this.updateData('quotes', `transId = '${transId}'`, { status });
            }

            return this.makeResponse(200, "Status updated");
        } catch (error) {
            console.error("Error in updateTransaction:", error);
            return this.makeResponse(500, "Error updating transaction");
        }
    }

    async confirmPayment(data: any) {
        try {
            console.log(`confirmPayment`, data)
            const { hash, trans_id } = data;

            //query the hash to validate that it was paid depending on the chain
            const transInfo: any = await this.selectDataQuerySafe("quotes", { transId: trans_id })
            if (transInfo.length == 0) {
                return this.makeResponse(404, "transaction not found");
            }

            const singleTransaction = transInfo[0]
            const tStatus = singleTransaction.status
            const pay_in_status = singleTransaction.pay_in_status
            const savedHash = singleTransaction.hash
            const transId = singleTransaction.transId
            if (tStatus == "SUCCESSFUL" || tStatus == "EXPIRED" || tStatus == "FAILED") {
                return this.makeResponse(203, "transaction status is " + tStatus);
            }



            // Verify the blockchain transaction
            const blockchainVerifier = new BlockchainVerifier();
            const chain = singleTransaction.chain || "XLM"; // Default to XLM if not specified



            //  if (savedHash != hash) {
            const verificationResult = await blockchainVerifier.verifyTransaction(hash, chain, singleTransaction.send_amount);

            if (!verificationResult.isValid) {
                return this.makeResponse(203, "Blockchain verification failed");
            }

            // Verify the transaction amount matches
            if (verificationResult.amount && parseFloat(verificationResult.amount) < parseFloat(singleTransaction.send_amount)) {
                return this.makeResponse(203, "Transaction amount is less than expected");
            }

            // Verify the recipient address matches
            if (verificationResult.to && verificationResult.to.toLowerCase() !== singleTransaction.receiver_address.toLowerCase()) {
                return this.makeResponse(203, "Transaction recipient address doesn't match");
            }

            if (pay_in_status != "SUCCESSFUL") {
                await this.updateData('quotes', `transId = '${transId}'`, { pay_in_status: 'SUCCESSFUL' });
            }
            //  }


            const payoutData = {
                transId: trans_id,
                hash,
                chainInfo: {
                    hash,
                    asset_amount: singleTransaction.send_amount,
                    asset_code: singleTransaction.send_asset,
                    to_address: singleTransaction.receiver_address,
                    from_address: singleTransaction.sending_address,
                    chain: chain,
                    contract_address: singleTransaction.provider_address || ""
                },
                accountInfo: {
                    account_name: singleTransaction.bank_name || "",
                    account_number: singleTransaction.account_number
                },
                payOutCurrency: singleTransaction.receive_currency,
                payoutAmount: singleTransaction.receive_amount
            };

            const response = await this.requestPayout(payoutData);
            console.log(`requestPayout`, response);
            return response;

        } catch (error) {
            console.error("Error in confirmPayment:", error);
            return this.makeResponse(500, "Error updating transaction");
        }
    }

    async requestPayout(data: any) {
        console.log(`requestPayout`, data)
        // Extract values from the nested objects in the payload
        const hash = data.chainInfo.hash;
        const transId = data.transId; // Original transaction id (if needed)
        const asset_amount = data.chainInfo.asset_amount;
        const asset_code = data.chainInfo.asset_code;
        const to_address = data.chainInfo.to_address;
        const from_address = data.chainInfo.from_address;
        const accountInfo: any = data.accountInfo;
        const acc_name = accountInfo.account_name;
        const account_number = accountInfo.account_number;
        const chain = data.chainInfo.chain;
        const contract_address = data.chainInfo.contract_address;
        const currency = data.payOutCurrency;  // Use payOutCurrency for the payout currency
        let payout_amount = data.payoutAmount.toString(); // Ensure it's a string


        try {

            const transInfo: any = await this.selectDataQuerySafe("quotes", { transId })
            if (transInfo.length == 0) {
                return this.makeResponse(404, "transaction not found");
            }
            const singleTransaction = transInfo[0]
            const tStatus = singleTransaction.status
            const pay_in_status = singleTransaction.pay_in_status
            payout_amount = singleTransaction.receive_amount

            if (pay_in_status != "SUCCESSFUL") {
                return this.makeResponse(205, "Transaction not received");
            }


            const rsp: any = await this.selectDataQuerySafe("pay_log", { quote_id: transId });
            if (rsp.length > 0) {
                return this.makeResponse(203, "Transaction already processed");
            }

            // Save a log with the received payload details
            await this.save_log(
                transId,
                hash,
                asset_code,
                asset_amount,
                currency,
                payout_amount,
                account_number,
                contract_address,
                JSON.stringify(data)
            );

            console.log("InfoPlate", data);

            // (Optional) You may include additional chain-specific logic here.
            // For example, if (chain.toLowerCase() == "bsc") { ... }

            // Retrieve additional transaction info from previous logs if needed.
            const txInfo = rsp[0];

            await this.updateData('pay_log', `quote_id = '${transId}'`, { status: 'INITIATED' });
            let response: any = null;
            if (currency === "UGX") {
                response = await MudaPayment.makePayout(transId, payout_amount, account_number);
                console.log('UGX Transaction:', response);

            } else if (currency == "GHS") {
                response = await Ogateway.sendToMobile(transId, payout_amount, "JAMES OKELLO", account_number)
                console.log('GHS Transaction:', response);
            } else {
                console.log('UN SUPPORYTED:', response);
                return this.makeResponse(201, "not suported");
            }

            console.log("LIQU_RAIL", response.data);

            // Check if the API response indicates success
            if (response.status == 200 || response.status == 202) {
                await this.updateData('pay_log', `quote_id = '${transId}'`, { status: 'SUCCESSFUL' });
                await this.update_log(hash, JSON.stringify(response.data), transId, "SUCCESSFUL");
            } else {
                await this.updateData('pay_log', `quote_id = '${transId}'`, { status: 'FAILED' });
                await this.update_log(hash, JSON.stringify(response.data), transId, "FAILED");
                console.error("Failed to send liquidity:", response.data);
            }


            return this.makeResponse(200, "Service updated successfully");
        } catch (error) {
            await this.update_log(hash, error, "", "FAILED");
            console.error("Error in updateTransaction:", error);
            return this.makeResponse(203, "Error updating service");
        }
    }


    async getPendingQuotes(userId: any) {
        return await this.callQuerySafe("SELECT * FROM quotes WHERE company_id = ? AND status = 'PENDING'", [userId])
    }
    async getPendingQuotesFilter(data: any) {
        const { company_id, send_asset, send_amount, receive_currency, chain, service_id } = data
        return await this.callQuerySafe(
            "SELECT * FROM quotes WHERE company_id = ? AND send_asset = ? AND send_amount = ? AND receive_currency = ? AND chain = ? AND service_id = ? AND status = 'PENDING'",
            [company_id, send_asset, send_amount, receive_currency, chain, service_id]
        )
    }

    async cancelQuote(id: string, userId: any) {
        const addExists: any = await this.callQuerySafe("SELECT * FROM quotes WHERE transId = ? AND status = 'PENDING'", [id])

        if (addExists.length == 0) {
            return this.makeResponse(203, "PENDING quote not found");
        }
        const updates = {
            status: 'CANCELLED',
            narration: 'CANCELLED'
        };
        await this.updateData('quotes', `transId = '${id}'`, updates);

        // Send webhook for cancelled quote
        await WebhookSender.send(id);

        return this.makeResponse(200, "Quote cancelled");
    }
    async verifyAccount(data: any) {

        const resp = {
            accountName: "",
            bank_code: "2345",
            isvalid: true
        }
        return this.makeResponse(200, "succcess", resp);

    }



    async getQuote(data: any) {
        console.log(`getQuote`, data)
        let { receive_currency, status, send_amount, receiver_address, sending_address, send_asset } = data;
        receiver_address = receiver_address.toLowerCase();
        sending_address = sending_address.toLowerCase();

        // Construct SQL query with lowercased addresses
        const query = `
            SELECT * FROM quotes 
            WHERE send_asset = '${send_asset}' 
            AND receive_currency = '${receive_currency}' 
            AND LOWER(receiver_address) = '${receiver_address}' 
            AND LOWER(sending_address) = '${sending_address}'
            AND status='${status}'
            AND send_amount='${send_amount}'
        `;
        const chainInfo: any = await this.callRawQuery(query);
        if (chainInfo.length > 0) {
            return this.makeResponse(200, "success", chainInfo[0]);
        } else {
            return this.makeResponse(404, "not found");

        }
    }



    async confirmBookedRate(data: any) {
        try {
            const {
                company_id,
                service_id,
                reference_id,
                payment_method_id,
                sending_address,
                source,
                quote_id
            } = data


            const quoteLogResults: any = await this.callQuerySafe(
                "SELECT * FROM quote_log WHERE quoteId = ? LIMIT 1",
                [quote_id]
            );
            if (quoteLogResults.length === 0) {
                return this.makeResponse(404, 'Booked quote not found');
            }

            const quote = quoteLogResults[0];
            const currentTime = new Date();
            const expiresAtDate = new Date(quote.expiresAt);

            if (currentTime > expiresAtDate) {
                return this.makeResponse(404, 'Quote has expired');
            }

            const postData = {
                provider_id: quote.providerId,
                book_quote_id: quote_id,
                reference_id,
                chain: quote.chain,
                providerQuoteId: quote.providerQuoteId,
                asset_code: quote.asset_code,
                receive_currency: quote.currency,
                send_amount: parseFloat(quote.cryptoAmount),
                ex_rate: parseFloat(quote.quotedPrice),
                sending_address: sending_address,
                company_id: company_id,
                service_id: quote.service_id,
                transaction_type: quote.transactionType,
                payment_method_id: payment_method_id,
                source: "exchange"
            };

            return await this.saveQuote(postData, quote);

        } catch (err) {
            console.error("Error in confirmBookedRate:", err);
            return this.makeResponse(500, "Internal server error while confirming booked rate");
        }
    }




    async saveQuote(data: SaveQuoteRequest, bookedQuote: any = null) {
        try {
            const {
                provider_service_id,
                source,
                reference_id,
                sending_address,
                company_id,
                send_amount,
                receive_currency,
                payment_method_id,
                book_quote_id,
                asset_code,
                service_id: dataServiceId,
                provider_id: dataProviderId,
                transaction_type = "off_ramp",
                chain
            } = data;
            console.log(`saveQuote`, data)

            // Handle provider 10 (Trading Service) - save as pending rail order

            let transId = this.getRandomString();
            let hasBookedQuote = false;

            if (book_quote_id && book_quote_id !== "" && book_quote_id !== "undefined") {
                transId = book_quote_id;
                hasBookedQuote = true;
            }

            let service_id = dataServiceId || "";
            let provider_id = dataProviderId || "";
            let transaction_type_final = transaction_type;

            const supportedTransactionTypes = ["off_ramp", "on_ramp", "bridge", "swap"];
            if (!supportedTransactionTypes.includes(transaction_type_final)) {
                return this.makeResponse(203, "Not supported transaction type");
            }

            if (transaction_type_final === "on_ramp") {
                console.log("Processing on-ramp request");
            }

            // Handle Rhino bridge/swap operations
            if (transaction_type_final === "bridge" || transaction_type_final === "swap") {
                console.log(`Processing ${transaction_type_final} request`);
                return await this.handleRhinoBridgeSwap(data, transaction_type_final, transId);
            }

            if (data.provider_service_id) {
                const service: any = await this.callQuerySafe(
                    "SELECT * FROM service_providers WHERE provider_service_id = ?",
                    [data.provider_service_id]
                );
                if (service.length === 0) {
                    return this.makeResponse(203, "Service not found");
                }
                service_id = service[0].service_id;
                provider_id = service[0].provider_id;
            }


            if (data.reference_id && data.reference_id !== "") {
                const transIdReferenceId = data.reference_id;
                const quoteExists: any = await this.callQuerySafe("SELECT * FROM quotes WHERE  client_reference_id = ?", [transIdReferenceId]);
                if (quoteExists.length > 0) {
                    return this.makeResponse(203, "reference already used");
                }
            }

            // Check if quote exists
            const quoteExists: any = await this.callQuerySafe("SELECT * FROM quotes WHERE transId = ?", [transId]);
            if (quoteExists.length > 0) {
                return this.makeResponse(203, "Quote already exists");
            }

            let asset_code_final = asset_code || "";
            let supportedChain = chain || await this.getchain(asset_code_final);

            // For on-ramp transactions, use the send_asset from request body (fiat currency)
            // For off-ramp transactions, use the asset_code lookup
            let send_asset: any = data.send_asset || "";
            if (transaction_type_final === "on_ramp") {
                send_asset = data.send_asset || ""; // Use fiat currency from request (e.g., "NGN")
            } else {
                send_asset = await this.getAssetCode(asset_code_final); // Use crypto asset lookup
            }

            if (!send_asset) {
                return this.makeResponse(203, "Asset code not supported");
            }

            let evmAddress = "";
            let privateKey = "";

            const quoteQuery = {
                company_id,
                send_asset,
                send_amount,
                receive_currency,
                chain: supportedChain,
                service_id
            };

            // const pendingQuotes: any = await this.getPendingQuotesFilter(quoteQuery);
            // if (pendingQuotes.length > 3 && source === "wallet") {
            //     return this.makeResponse(405, "You can't create a new quote when you have pending quotes, please cancel the existing ones first", pendingQuotes);
            // }

            const chainInfo: any = await this.callQuerySafe(
                "SELECT * FROM accepted_assets WHERE asset_code = ? AND chain = ?",
                [asset_code_final, supportedChain]
            );
            if (chainInfo.length === 0) {
                return this.makeResponse(203, "Coin not supported");
            }

            const addExists: any = await this.callQuerySafe(
                "SELECT * FROM addresses WHERE address = ? AND chain = ?",
                [sending_address, supportedChain]
            );

            if (addExists.length === 0 && source === "wallet") {
                return this.makeResponse(203, "Address not saved, please save this sending first");
            }

            const getprovider: any = await this.getprovider(provider_id, service_id);
            if (getprovider.length === 0) {
                return this.makeResponse(203, "Provider and service id not match");
            }

            console.log(`getprovider details - saveQuote`, getprovider);
            const max_amount = getprovider[0].max_amount;
            const min_amount = getprovider[0].min_amount;
            const service_code = getprovider[0].service_code || getprovider[0].service_id;
            const provider_name = getprovider[0].provider_name;
            const final_provider_id = getprovider[0].provider_id;

            if (send_amount < min_amount) {
                return this.makeResponse(203, `Min quote amount is ${min_amount}`);
            }
            if (send_amount > max_amount) {
                return this.makeResponse(203, `Maximum quote amount is ${max_amount}`);
            }

            const paymentMethod = await this.getPaymentMethodById(payment_method_id);
            if (paymentMethod.length === 0) {
                return this.makeResponse(203, "Payout method not found");
            }

            // Validate refund address for HoneyCoin (provider 6) KES off-ramp
            if (final_provider_id == "6" && transaction_type_final === "off_ramp" && receive_currency === "KES") {
                if (!data.refund_address || data.refund_address.trim() === "") {
                    return this.makeResponse(400, "Refund address is required for HoneyCoin KES off-ramp. Please provide a valid refund_address on the same blockchain.");
                }
                console.log(`✅ Refund address provided for HoneyCoin off-ramp: ${data.refund_address}`);
            }

            // Validate sending_address for Kotani (provider 2) off-ramp
            if (transaction_type_final === "off_ramp" && (receive_currency === "ZAR" || final_provider_id == "2" || service_id === "1002")) {
                if (!sending_address || sending_address.trim() === "") {
                    return this.makeResponse(400, "Sending address is required for (provider 2) off-ramp. Please provide a valid sending_address.");
                }
                console.log(`✅ Sending address provided for Kotani off-ramp: ${sending_address}`);
            }

            console.log("paymentMethod  details", paymentMethod[0]);
            const { type, phone_number, country_code, network, account_name, bank_code, account_number: pm_account_number } = paymentMethod[0]
            let account_number = (type === "bank") ? pm_account_number : phone_number;

            const { blockchainFee, mudaFee, totalFees, payableAmount } = await this.calculateMudaFee(supportedChain, send_amount);
            console.log(`blockchainFees::1`, blockchainFee, mudaFee, totalFees, payableAmount);

            let receive_amount = 0;
            let fee = 0;
            let ex_rate = 0;
            let rate: any = null;
            if (hasBookedQuote) {
                receive_amount = transaction_type_final === "on_ramp" ? bookedQuote.toAmount : bookedQuote.fiatAmount;
                fee = bookedQuote.fee;
                ex_rate = bookedQuote.quotedPrice;

            } else {
                const rateReq: RateRequest = {
                    asset_code: asset_code_final,
                    currency: transaction_type_final === "on_ramp" ? send_asset : receive_currency,
                    amount: payableAmount,
                    provider_id: final_provider_id,
                    service_code: service_code,
                    transaction_type: transaction_type_final
                };

                console.log(`blockchainFees::2`, rateReq);
                rate = await this.getRate(rateReq, transId);
                console.log(`blockchainFees::3`, rate);
                if (!rate?.data?.fiatAmount) {
                    return this.makeResponse(203, "Rate not found");
                }
                receive_amount = transaction_type_final === "on_ramp" ? rate.data.toAmount : rate.data.fiatAmount;
                fee = rate.data.fee;
                ex_rate = rate.data.quotedPrice;
            }

            // For on-ramp: receive_amount should be crypto amount (USDT)
            // For off-ramp: receive_amount should be fiat amount


            // Validate amounts are within database limits before proceeding
            const maxDecimalValue = 999999.99; // Adjust based on your database DECIMAL precision
            const parsedReceiveAmount = parseFloat(String(receive_amount || 0));
            const parsedSendAmount = parseFloat(String(send_amount || 0));
            const parsedFee = parseFloat(String(fee || 0));
            const parsedExRate = parseFloat(String(ex_rate || 0));
            const parsedPayableAmount = parseFloat(String(payableAmount || 0));
            const parsedTotalFees = parseFloat(String(totalFees || 0));

            if (parsedReceiveAmount > maxDecimalValue) {
                return this.makeResponse(400, `Receive amount (${parsedReceiveAmount.toFixed(2)}) exceeds maximum allowed value. Please reduce the transaction amount.`);
            }
            if (parsedSendAmount > maxDecimalValue) {
                return this.makeResponse(400, `Send amount (${parsedSendAmount.toFixed(2)}) exceeds maximum allowed value.`);
            }
            if (parsedPayableAmount > maxDecimalValue) {
                return this.makeResponse(400, `Payable amount (${parsedPayableAmount.toFixed(2)}) exceeds maximum allowed value.`);
            }

            let externalRefId = "";
            let provider_memo = "";
            let mudaAddress: any = "";
            let expiresAt: any = "";
            let memo: any = "";
            let new_send_amount = send_amount.toString();


            data.chain = supportedChain;
            data.send_asset = send_asset;


            // Variables for on-ramp processing
            let confirmResponse: any = null;
            let paymentInfo: any = null;
            let cryptoInfo: any = null;
            let providerResponse: any = null;


            if (transaction_type_final === "on_ramp") {


                const result: any = await this.initiateOnRampTransaction(transId, final_provider_id, data, paymentMethod[0]);
                if (result && typeof result === 'object' && 'mudaAddress' in result) {
                    mudaAddress = result.mudaAddress;
                    expiresAt = result.expiresAt;
                    externalRefId = result.externalRefId;
                    paymentInfo = result.paymentInfo;
                    providerResponse = result?.responseData;
                } else {

                    return result; // Return error response
                }

            } else if (transaction_type_final === "off_ramp") {

                // Check if this is Quidax off-ramp (service_id 1005)
                if (String(final_provider_id) === "4" && String(dataServiceId) === "1005") {
                    console.log("Processing Quidax off-ramp (service_id 1005) in saveQuote");
                    const result = await this.createOfframpRequest(transId, receive_currency, final_provider_id, data, paymentMethod[0], data.refund_address);
                    if (result && typeof result === 'object' && 'mudaAddress' in result) {
                        mudaAddress = result.mudaAddress;
                        expiresAt = result.expiresAt;
                        externalRefId = result.externalRefId;
                        evmAddress = result.evmAddress;
                        memo = result.memo;
                        // For Quidax off-ramp, paymentInfo will be set separately
                        paymentInfo = null;
                        providerResponse = null;
                    } else {
                        return result; // Return error response
                    }
                } else {
                    // Check if bridge is required by looking up the quote_log
                    const quoteLogResults: any = await this.callQuerySafe(
                        "SELECT * FROM quote_log WHERE quoteId = ? LIMIT 1",
                        [transId]
                    );

                    const bridgeRequired = quoteLogResults.length > 0 && quoteLogResults[0].bridge_required === 1;

                    if (bridgeRequired) {
                        console.log('🌉 Bridge required for this transaction, executing bridge first');
                        const quoteLog = quoteLogResults[0];

                    // Step 1: Generate LR wallet address (this will be the final destination)
                    const MudaPayment = require('../helpers/MudaPayment').default;
                    const lrWalletResult = await MudaPayment.generateLRWallerAddress(
                        quoteLog.bridge_to_chain,  // chain - provider's supported chain
                        transId,                   // referenceId
                        `LR_RAIL`, // clientId
                        quoteLog.asset_code.split('_')[0], // asset
                        parseFloat(data.send_amount.toString()) // amount
                    );

                    console.log('🌉 LR wallet generation result:', lrWalletResult);

                    if (!lrWalletResult || lrWalletResult.status !== 200 || !lrWalletResult.data || !lrWalletResult.data.deposit_address) {
                        console.error('🌉 Failed to generate LR wallet address:', lrWalletResult);
                        return this.makeResponse(203, `Failed to generate LR wallet address: ${lrWalletResult?.message || 'Unknown error'}`);
                    }

                    const lrWalletAddress = lrWalletResult.data.deposit_address;
                    console.log('🌉 LR wallet address generated:', lrWalletAddress);

                    // Step 2: Generate smart deposit address with LR wallet as destination
                    const Rhino = require('../helpers/Rhino').default;
                    const rhinoQuoteId = quoteLog.rhino_quote_id;

                    if (!rhinoQuoteId) {
                        console.error('🌉 No Rhino quote ID found in quote_log');
                        return this.makeResponse(203, "Bridge quote ID not found. Please request a new quote.");
                    }

                    // Convert chain names to Rhino-compatible format
                    const rhinoFromChain = this.convertToRhinoChainName(quoteLog.bridge_from_chain);
                    const rhinoToChain = this.convertToRhinoChainName(quoteLog.bridge_to_chain);

                    const smartDepositResult = await Rhino.generateSmartDeposit(
                        [rhinoFromChain],              // depositChains - user's original chain (Rhino format)
                        rhinoToChain,                  // destinationChain - provider's chain (Rhino format)
                        lrWalletAddress,               // destinationAddress - LR wallet address
                        quoteLog.asset_code.split('_')[0],
                        'create-new'                // reusePolicy
                    );

                    if (!smartDepositResult.success || !smartDepositResult.data || smartDepositResult.data.length === 0) {
                        console.error('🌉 Failed to generate smart deposit:', smartDepositResult);
                        return this.makeResponse(203, `Failed to generate smart deposit address: ${smartDepositResult.message || 'Unknown error'}`);
                    }

                    const smartDepositAddress = smartDepositResult.data[0].depositAddress;
                    console.log('🌉 Smart deposit address generated:', smartDepositAddress);

                    // Step 3: Create off-ramp request with smart deposit address as sending address
                    // Update data with smart deposit address
                    const updatedData = {
                        ...data,
                        sending_address: smartDepositAddress
                    };

                    const result = await this.createOfframpRequest(transId, receive_currency, final_provider_id, updatedData, paymentMethod[0], data.refund_address);
                    if (!result || !('mudaAddress' in result)) {
                        return result; // Return error response
                    }

                    mudaAddress = smartDepositAddress; // User sends to smart deposit address
                    expiresAt = result.expiresAt;
                    externalRefId = result.externalRefId;
                    evmAddress = result.evmAddress || result.mudaAddress;
                    memo = result.memo || '';
                    supportedChain = quoteLog.bridge_from_chain; // User sends on their original chain

                    // Store bridge details
                    await this.insertData('bridge_swap_quote_details', {
                        quote_id: transId,
                        from_chain: quoteLog.bridge_from_chain,
                        to_chain: quoteLog.bridge_to_chain,
                        from_token: quoteLog.asset_code.split('_')[0],
                        to_token: quoteLog.asset_code.split('_')[0],
                        depositor_address: lrWalletAddress, // Use smart deposit if user address not provided
                        destination_address: lrWalletAddress,
                        smart_deposit_address: smartDepositAddress,
                        rhino_quote_id: rhinoQuoteId,
                        operation_type: 'bridge',
                        provider_address: result.mudaAddress // The actual provider off-ramp address
                    });

                    // Add to bridge queue for monitoring
                    console.log('🔄 Adding bridge quote to queue for monitoring');
                    const addedToQueue = await BridgeQueueService.addToQueue(
                        transId,
                        smartDepositAddress,
                        quoteLog.bridge_from_chain
                    );
                    
                    if (!addedToQueue) {
                        console.warn('⚠️ Failed to add bridge quote to queue - bridge monitoring may not work');
                    }

                    // Return cryptoInfo without bridge details (internal only)
                    cryptoInfo = {
                        chain: supportedChain,
                        send_asset: send_asset,
                        send_amount: new_send_amount.toString(),
                        pay_in_address: mudaAddress,
                        pay_in_memo: memo
                    };

                } else {
                    // No bridge required, proceed with normal off-ramp flow
                    const result = await this.createOfframpRequest(transId, receive_currency, final_provider_id, data, paymentMethod[0], data.refund_address);
                    if (result && typeof result === 'object' && 'mudaAddress' in result) {

                        mudaAddress = result.mudaAddress;
                        expiresAt = result.expiresAt;
                        externalRefId = result.externalRefId;
                        evmAddress = result.evmAddress;
                        memo = result.memo;
                        cryptoInfo = {
                            chain: supportedChain,
                            send_asset: send_asset,
                            send_amount: new_send_amount.toString(),
                            pay_in_address: mudaAddress,
                            pay_in_memo: memo
                        };

                    } else {
                        return result; // Return error response
                    }
                    }
                }
            } else {

                return this.makeResponse(203, "Provider not supported");
            }

            // Skip address mapping insertion for on-ramp transactions as it's already handled in initiateOnRampTransaction
            if (transaction_type_final !== "on_ramp") {
                // Check if mapping already exists
                const existingMapping: any = await this.callQuerySafe(
                    'SELECT id FROM quote_address_mapping WHERE quote_id = ? LIMIT 1',
                    [transId]
                );

                if (!existingMapping || existingMapping.length === 0) {
                    await this.saveAddressMapping(transId, mudaAddress, evmAddress, externalRefId);
                }
            }

            if (!expiresAt) {
                expiresAt = new Date(Date.now() + 1000 * 60 * 120)
                    .toISOString()
                    .slice(0, 19)
                    .replace('T', ' ');
            }

            const newTransaction = {
                company_id,
                provider_id: final_provider_id,
                transId,
                send_asset: send_asset,
                client_reference_id: reference_id,
                send_amount: new_send_amount,
                receive_currency,
                receive_amount: receive_amount,
                ex_rate: parsedExRate.toFixed(2),
                account_number,
                transaction_type: transaction_type_final,
                service_id,
                payable_amount: parsedPayableAmount.toFixed(2),
                sending_address: sending_address || '',
                fee: parsedFee.toFixed(2),  // Use parsedFee which includes all fees from getRate (provider + muda + bridge + rhino transfer)
                fee_currency: send_asset,
                receiver_address: mudaAddress,
                status: 'PENDING',
                provider_ref_id: externalRefId,
                provider_address: evmAddress,
                provider_memo,
                expires_at: expiresAt,
                payment_method_id,
                chain: supportedChain,
                response_body: JSON.stringify(providerResponse),
                message: `${transaction_type_final.toUpperCase()} INITIATED`
            };

            console.log(`Transaction created successfully for company ${company_id}`);
            console.log('Transaction data to insert:', JSON.stringify(newTransaction, null, 2));

            try {
                const insertedTransaction = await this.insertData('quotes', newTransaction);
                if (insertedTransaction === false) {
                    return this.makeResponse(300, "Transaction not saved");
                }
            } catch (insertError: any) {
                console.error('Error inserting transaction:', insertError);
                if (insertError.message && insertError.message.includes('Out of range')) {
                    return this.makeResponse(400, `Database error: Value exceeds column limit. receive_amount=${newTransaction.receive_amount}. Please check your database schema or reduce transaction amount.`, {
                        error: insertError.message,
                        transaction_data: {
                            send_amount: newTransaction.send_amount,
                            receive_amount: newTransaction.receive_amount,
                            payable_amount: newTransaction.payable_amount,
                            fee: newTransaction.fee
                        }
                    });
                }
                throw insertError;
            }

            console.log('Transaction saved successfully');
            try {
                await this.logMudaFee({
                    quote_id: transId,
                    provider_service_id: provider_service_id || "",
                    muda_fee: mudaFee,
                    thirdparty_fee: totalFees,
                    thirdparty_quote: rate.data?.providerQuoteId ?? '',
                    thirdparty_rate: rate.data?.quotedPrice ?? '',
                    blockchain_fee: blockchainFee,
                    blockchain_fee_asset: send_asset,
                    rate: rate.data?.quotedPrice ?? ''
                });
            } catch (feeErr) {
                console.error('Error computing/logging MUDA fee:', feeErr);
            }

            const quoteResponse: QuoteResponse = {
                quote_id: transId,
                status: 'PENDING',
                send_asset: send_asset,
                send_amount: new_send_amount.toString(),
                receive_currency: receive_currency,
                ex_rate: ex_rate,
                fee: totalFees,
                receive_amount: receive_amount,
                payment_method_id: payment_method_id,
                expires_at: expiresAt
            };

            let responseObj = null
            // Handle different responses for on-ramp vs off-ramp
            if (transaction_type_final === "on_ramp") {

                const fiatInfo: PaymentInformation = paymentInfo;
                responseObj = {
                    ...quoteResponse,
                    fiatInfo: fiatInfo
                };
            } else {
                const payInfo: ChainInfo = cryptoInfo;
                responseObj = {
                    ...quoteResponse,
                    chainInfo: payInfo
                };
            }
            this.LogOperation(transId, 'QUOTE_RESPONSE', 'QUOTE_CREATED_RESPONSE', responseObj);
            return this.makeResponse(200, "Quote created successfully", responseObj);
        } catch (error: any) {
            console.error('Error inserting transaction:', error);
            this.LogOperation("ERROR_LOG", 'QUOTE_RESPONSE', 'QUOTE_CREATED_ERROR', error);
            return this.makeResponse(500, "Error generating a quote");
        }
    }

    /**
     * Handle Rhino bridge/swap operations
     * @param data SaveQuoteRequest with bridge/swap specific fields
     * @param operation_type 'bridge' or 'swap'
     * @param transId Generated transaction ID
     * @returns Promise with quote response
     */
    async handleRhinoBridgeSwap(data: any, operation_type: string, transId: string) {
        try {
            const {
                company_id,
                provider_id,
                service_id,
                send_amount,
                from_chain,
                to_chain,
                from_token,
                to_token,
                destination_address,
                depositor_address,
                receiver_address,
                reference_id
            } = data;

            // Validate required fields
            if (!provider_id) {
                return this.makeResponse(400, "Missing required field: provider_id");
            }
            if (!service_id) {
                return this.makeResponse(400, "Missing required field: service_id");
            }
            if (!reference_id) {
                return this.makeResponse(400, "Missing required field: reference_id (format: REF9076505380)");
            }

            // Validate reference_id format (REF + 10 digits)
            const referenceIdPattern = /^REF\d{10}$/;
            if (!referenceIdPattern.test(reference_id)) {
                return this.makeResponse(400, "Invalid reference_id format. Expected format: REF9076505380 (REF followed by 10 digits)");
            }

            // Validate provider exists
            const providerExists: any = await this.callQuerySafe(
                "SELECT provider_id FROM providers WHERE provider_id = ? LIMIT 1",
                [provider_id]
            );
            if (providerExists.length === 0) {
                return this.makeResponse(400, `Invalid provider_id: ${provider_id}. Provider not found.`);
            }

            // Validate service exists and belongs to the provider
            const serviceExists: any = await this.callQuerySafe(
                "SELECT s.service_id FROM services s JOIN service_providers sp ON s.service_id = sp.service_id WHERE s.service_id = ? AND sp.provider_id = ? LIMIT 1",
                [service_id, provider_id]
            );
            if (serviceExists.length === 0) {
                return this.makeResponse(400, `Invalid service_id: ${service_id}. Service not found or does not belong to provider ${provider_id}.`);
            }

            // Check for duplicate reference_id
            const existingQuote: any = await this.callQuerySafe(
                "SELECT transId FROM quotes WHERE client_reference_id = ? AND company_id = ?",
                [reference_id, company_id]
            );
            if (existingQuote.length > 0) {
                return this.makeResponse(409, `Duplicate request: A quote with reference_id '${reference_id}' already exists`, {
                    existing_transaction_id: existingQuote[0].transId,
                    reference_id: reference_id
                });
            }

            // Use receiver_address as fallback for destination_address
            const finalDestinationAddress = destination_address || receiver_address;

            // Validate required fields for bridge/swap
            if (!from_chain || !to_chain || !from_token || !to_token || !finalDestinationAddress || !depositor_address) {
                return this.makeResponse(400, "Missing required fields for bridge/swap: from_chain, to_chain, from_token, to_token, destination_address, depositor_address");
            }

            console.log(`🔹 Processing ${operation_type} request:`, {
                from_chain,
                to_chain,
                from_token,
                to_token,
                send_amount,
                destination_address: finalDestinationAddress
            });

            // Import Rhino integration
            const rhinoClient = require('../helpers/Rhino').default;

            // Prepare Rhino quote request
            const rhinoQuoteRequest = {
                fromChain: from_chain,
                toChain: to_chain,
                fromToken: from_token,
                toToken: to_token,
                amount: send_amount.toString(),
                depositor: depositor_address,
                recipient: finalDestinationAddress
            };

            console.log('🔹 Generating Rhino quote:', rhinoQuoteRequest);

            // Get quote from Rhino
            const rhinoQuoteResponse = await rhinoClient.getQuote(rhinoQuoteRequest);

            if (!rhinoQuoteResponse.success) {
                console.log('❌ Rhino quote generation failed:', rhinoQuoteResponse);
                return this.makeResponse(400, rhinoQuoteResponse.message || "Failed to generate Rhino quote");
            }

            const rhinoQuote = rhinoQuoteResponse.data;
            const totalFees = rhinoQuote.fees?.reduce((sum: number, fee: any) => sum + parseFloat(fee.amount || '0'), 0) || 0;


            const outputAmount = rhinoQuote.outputAmount;
            const inputAmount = rhinoQuote.inputAmount;

            // For token-to-token transactions, use rate of 1
            const exchange_rate = 1;

            const payableAmount = parseFloat(send_amount.toString()) + totalFees;

            const finalTransactionType = operation_type;
            const finalReceiveAmount = parseFloat(outputAmount);
            const finalExpiresAt = rhinoQuote.expiresAt ? new Date(rhinoQuote.expiresAt).toISOString().slice(0, 19).replace('T', ' ') : null;
            const quoteData = {
                transId: transId,
                provider_id: provider_id,
                company_id: company_id,
                send_asset: from_token,
                send_amount: send_amount.toString(),
                receive_currency: to_token,
                chain: null,
                transaction_type: finalTransactionType,
                receive_amount: finalReceiveAmount,
                payable_amount: payableAmount,
                ex_rate: exchange_rate.toString(),
                account_number: "",
                service_id: service_id,
                receiver_address: finalDestinationAddress,
                pay_in_status: 'PENDING',
                status: 'QUOTE_GENERATED',
                sending_address: '',
                response_body: JSON.stringify(rhinoQuote),
                reason: null,
                bank_name: null,
                bank_code: null,
                provider_ref_id: rhinoQuote.id,
                provider_address: null,
                provider_memo: null,
                fee: totalFees,
                fee_currency: null,
                payment_method_id: "",
                narration: '',
                hash: '',
                client_reference_id: reference_id,
                expires_at: finalExpiresAt
            };


            await this.insertData('quotes', quoteData);

            // Save detailed bridge/swap information to the dedicated table
            const bridgeSwapDetails = {
                quote_id: transId,
                from_chain: from_chain,
                to_chain: to_chain,
                from_token: from_token,
                to_token: to_token,
                depositor_address: depositor_address,
                destination_address: finalDestinationAddress,
                smart_deposit_address: null, // Will be updated when quote is confirmed
                estimated_duration: rhinoQuote.timeEstimate || null,
                rhino_quote_id: rhinoQuote.id,
                operation_type: finalTransactionType
            };

            await this.insertData('bridge_swap_quote_details', bridgeSwapDetails);
            console.log('✅ Bridge/Swap quote details saved for quote:', transId);

            const quoteResponse = {
                quote_id: transId,
                reference_id: reference_id,
                status: 'QUOTE_GENERATED',
                operation_type: finalTransactionType,
                from_chain: from_chain,
                to_chain: to_chain,
                from_token: from_token,
                to_token: to_token,
                send_amount: send_amount,
                receive_amount: finalReceiveAmount,
                payable_amount: payableAmount,
                exchange_rate: exchange_rate,
                fee: totalFees,
                destination_address: finalDestinationAddress,
                expires_at: rhinoQuote.expiresAt ? new Date(rhinoQuote.expiresAt).toISOString() : null,
                rhino_quote_id: rhinoQuote.id
            };

            console.log('✅ Bridge/Swap quote saved with ID:', transId);
            return this.makeResponse(200, "Quote generated successfully", quoteResponse);

        } catch (error) {
            console.error('❌ Error generating Rhino bridge/swap quote:', error);
            return this.makeResponse(500, "Internal server error while generating quote");
        }
    }

    /**
     * Confirm Rhino bridge/swap quote and create smart deposit address
     * @param quote_id The quote ID to confirm
     * @returns Promise with confirmation response including smart deposit address
     */
    async confirmRhinoBridgeSwapQuote(quote_id: string) {
        try {
            console.log(`🔹 Confirming Rhino bridge/swap quote: ${quote_id}`);

            // Get Rhino provider ID
            const rhinoProvider: any = await this.callQuerySafe(
                "SELECT provider_id FROM providers WHERE name = 'Rhino.fi' LIMIT 1"
            );

            if (rhinoProvider.length === 0) {
                return this.makeResponse(400, 'Rhino provider not found in database');
            }

            const provider_id = rhinoProvider[0].provider_id;

            // Get the quote from database with service information
            const quoteResults: any = await this.callQuerySafe(
                `SELECT q.*, s.service_code
                 FROM quotes q
                 LEFT JOIN services s ON q.service_id = s.service_id
                 WHERE q.transId = ? AND q.provider_id = ? LIMIT 1`,
                [quote_id, provider_id]
            );

            if (quoteResults.length === 0) {
                return this.makeResponse(404, 'Rhino bridge/swap quote not found');
            }

            const quote = quoteResults[0];

            // Check if quote is still valid
            if (quote.status !== 'QUOTE_GENERATED') {
                return this.makeResponse(400, `Quote cannot be confirmed. Current status: ${quote.status}`);
            }

            // Parse the Rhino quote data from response_body
            const rhinoQuoteData = JSON.parse(quote.response_body);
            const rhinoQuoteId = quote.provider_ref_id;

            if (!rhinoQuoteId) {
                return this.makeResponse(400, 'Rhino quote ID not found');
            }

            // Import Rhino integration and chain mapping
            const rhinoClient = require('../helpers/Rhino').default;
            const { mapToRhinoChain } = require('../helpers/rhinoChainConfigs');

            console.log(`🔹 Creating Smart Deposit address for quote: ${rhinoQuoteId}`);

            // Determine operation type from service code
            const operationType = quote.service_code === 'RHINO_BRIDGE' ? 'bridge' : 'swap';

            // Get chains from the stored Rhino quote data
            const fromChain = rhinoQuoteData.fromChain; // Already in Rhino format (e.g., 'ETHEREUM')
            const toChain = rhinoQuoteData.toChain; // Already in Rhino format (e.g., 'MATIC_POS')
            const destinationAddress = quote.receiver_address;

            console.log(`🔹 Operation: ${operationType}, From: ${fromChain}, To: ${toChain}`);

            // Generate Smart Deposit address using Rhino Smart Deposits flow
            const smartDepositResponse = await rhinoClient.generateSmartDeposit(
                [fromChain], // depositChains array
                toChain, // destinationChain
                destinationAddress // destinationAddress
            );

            if (!smartDepositResponse.success) {
                console.log('❌ Rhino Smart Deposit generation failed:', smartDepositResponse);

                // Update quote status to failed
                await this.updateData('quotes', `transId = '${quote_id}'`, {
                    status: 'FAILED',
                    pay_in_status: 'FAILED',
                    reason: smartDepositResponse.message || 'Failed to generate smart deposit address'
                });

                return this.makeResponse(400, smartDepositResponse.message || "Failed to generate smart deposit address");
            }

            const smartDepositData = smartDepositResponse.data;
            console.log('✅ Rhino Smart Deposit address generated:', smartDepositData);

            // Extract the smart deposit address from the response
            const smartDepositAddress = smartDepositData && smartDepositData.length > 0
                ? smartDepositData[0].depositAddress
                : null;

            if (!smartDepositAddress) {
                console.log('❌ No smart deposit address returned from Rhino');

                await this.updateData('quotes', `transId = '${quote_id}'`, {
                    status: 'FAILED',
                    pay_in_status: 'FAILED',
                    reason: 'No smart deposit address generated'
                });

                return this.makeResponse(400, "No smart deposit address generated");
            }

            // Calculate expires_at from Rhino response or use default
            const expiresAt = smartDepositData[0]?.expiresAt

            // Update quote with smart deposit address and confirmed status
            const updateData = {
                status: 'QUOTE_CONFIRMED',
                pay_in_status: 'PENDING_DEPOSIT',
                sending_address: smartDepositAddress,
                provider_address: smartDepositAddress,
                provider_memo: smartDepositData[0]?.memo || '',
                expires_at: expiresAt,
                response_body: JSON.stringify({
                    ...rhinoQuoteData,
                    smartDepositResponse: smartDepositData
                })
            };

            await this.updateData('quotes', `transId = '${quote_id}'`, updateData);

            // Update the bridge_swap_quote_details table with smart deposit address
            await this.updateData('bridge_swap_quote_details', `quote_id = '${quote_id}'`, {
                smart_deposit_address: smartDepositAddress
            });
            console.log('✅ Updated bridge_swap_quote_details with smart deposit address:', smartDepositAddress);

            // Prepare response
            const confirmationResponse = {
                quote_id: quote_id,
                status: 'QUOTE_CONFIRMED',
                operation_type: operationType, // 'bridge' or 'swap' - auto-detected from service_code
                from_chain: fromChain, // Rhino chain format (e.g., 'ETHEREUM')
                to_chain: toChain, // Rhino chain format (e.g., 'MATIC_POS')
                from_token: quote.send_asset,
                to_token: quote.receive_currency,
                send_amount: parseFloat(quote.send_amount),
                receive_amount: quote.receive_amount,
                smart_deposit_address: smartDepositAddress,
                deposit_memo: smartDepositData[0]?.memo || '',
                destination_address: quote.receiver_address,
                rhino_quote_id: rhinoQuoteId,
                expires_at: expiresAt
            };

            console.log(`✅ ${operationType} quote confirmed with smart deposit address:`, confirmationResponse.smart_deposit_address);
            return this.makeResponse(200, "Quote confirmed successfully", confirmationResponse);

        } catch (error) {
            console.error('❌ Error confirming Rhino bridge/swap quote:', error);
            return this.makeResponse(500, "Internal server error while confirming quote");
        }
    }

    /**
     * Update quote deposit transaction hash
     * Used by public endpoint to allow users to submit their deposit transaction hash
     */
    async updateQuoteDepositTxHash(quote_id: string, depositTxHash: string) {
        try {
            console.log(`🔹 Updating depositTxHash for quote ${quote_id}: ${depositTxHash}`);

            // First check if the quote exists and is in QUOTE_CONFIRMED status
            const quote: any = await this.callQuerySafe(
                "SELECT q.transId, q.status, bsd.quote_id FROM quotes q INNER JOIN bridge_swap_quote_details bsd ON q.transId = bsd.quote_id WHERE q.transId = ?",
                [quote_id]
            );

            if (quote.length === 0) {
                return this.makeResponse(404, "Quote not found");
            }

            const quoteData = quote[0];
            if (quoteData.status !== 'QUOTE_CONFIRMED') {
                return this.makeResponse(400, `Quote must be in QUOTE_CONFIRMED status. Current status: ${quoteData.status}`);
            }

            // Check if depositTxHash already exists
            const existingHash: any = await this.callQuerySafe(
                "SELECT depositTxHash FROM bridge_swap_quote_details WHERE quote_id = ?",
                [quote_id]
            );

            if (existingHash.length > 0 && existingHash[0].depositTxHash) {
                return this.makeResponse(400, "Deposit transaction hash already exists for this quote");
            }

            // Update the depositTxHash in bridge_swap_quote_details table
            await this.updateData('bridge_swap_quote_details', `quote_id = '${quote_id}'`, {
                depositTxHash: depositTxHash
            });

            console.log(`✅ Successfully updated depositTxHash for quote ${quote_id}`);

            return this.makeResponse(200, "Deposit transaction hash updated successfully", {
                quote_id: quote_id,
                depositTxHash: depositTxHash
            });

        } catch (error: any) {
            console.error(`❌ Error updating depositTxHash for quote ${quote_id}:`, error);
            return this.makeResponse(500, "Error updating deposit transaction hash", error.message);
        }
    }

    async saveAddressMapping(transId: string, internalAddress: string, provider_id: string, customerAddress: string) {

        try {
            return await this.insertData('quote_address_mapping', {
                quote_id: transId,
                muda_address: internalAddress,
                provider_id,
                provider_address: customerAddress,
                memo: transId
            });
        } catch (error: any) {
            console.error(`❌ Error saving address mapping for quote ${transId}:`, error);
            return this.makeResponse(500, "Error saving address mapping");
        }
    }

    async updatePayinTransaction(transId: string, status: string, message: string, data: any, finalStatus: string = "INITIATED") {
        await this.updateData("quotes", `transId = '${transId}'`, {
            provider_ref_id: data?.quidax_reference || transId,
            pay_in_status: status,
            response_body: JSON.stringify({
                message,
                data
            })
        });
    }

    async createOfframpRequest(transId: string, receive_currency: string, provider_id: string, data: SaveQuoteRequest, paymentMethod: any, refundAddress?: string) {
        let mudaAddress = "";
        let expiresAt = "";
        let externalRefId = "";
        let evmAddress = "";
        let memo = "";
        let supportedChain = "";
        let account_number = (paymentMethod.type === "bank") ? paymentMethod.account_number : paymentMethod.phone_number;
        const account_name = paymentMethod.account_name;

        console.log(`createOfframpRequest`, transId, receive_currency, provider_id, data, refundAddress)
        const send_asset = await this.getAssetCode(data.asset_code || "")

        if (provider_id == "2") {
            supportedChain = "BASE";
            
            // Get Kotani's provider address (escrow address)
            const providerAddress = await this.createKotaniOffRamp(
                data.payment_method_id,
                receive_currency,
                data.chain || "",
                send_asset,
                data.send_amount.toString(),
                transId,
                data.sending_address || ""  // Use the sending address from request
            );
            
            if (!providerAddress) {
                return this.makeResponse(203, "Provider failed to create off ramp");
            }
            
            mudaAddress = providerAddress; // Use provider's address directly
            evmAddress = providerAddress;
            
            console.log('✅ Kotani off-ramp created:', {
                kotaniAddress: mudaAddress,
                chain: supportedChain
            });
        } else if (provider_id == "6" && receive_currency == "KES") {


            const amountToPayout = parseFloat(data.send_amount.toString());
            const payCountry = "KE";
            const receiveEmail = "<EMAIL>";
            const payDestination = (paymentMethod.type === "bank") ? "bank" : "mobile";
            if (payDestination === "mobile") {

                const validatePhoneNumber: any = await this.honeyCoin.validatePayoutPhoneNumber(receive_currency, payCountry, account_number);
                if (!validatePhoneNumber?.status) {
                    return this.makeResponse(500, validatePhoneNumber.message, {
                        error: validatePhoneNumber.data || ""
                    });
                }
            }

            const validateChains: any = await this.honeyCoin.getAcceptedOnRampChains((data?.chain || "").toUpperCase());
            if (!validateChains) {
                return this.makeResponse(500, `Network ${data.chain} not supported`, {
                    error: `Network ${data.chain} not supported`
                });
            }


            this.LogOperation(transId, 'HONEY_COIN', 'HONEY_COIN_OFFRAMP', {
                amountToPayout,
                send_asset: send_asset,
                receive_currency,
                chain: data.chain,
                receiveEmail,
                payCountry,
                payDestination,
                account_number: account_number,
                account_name: account_name
            });

            const offRamp: any = await this.honeyCoin.offRamp(
                amountToPayout,
                send_asset,
                receive_currency,
                data.chain || "",
                receiveEmail,
                payCountry,
                "",
                transId,
                payCountry,
                payDestination,
                account_number,
                account_name,
                {},
                refundAddress
            );

            this.LogOperation(transId, 'HONEY_COIN', 'HONEY_COIN_OFFRAMP_RESPONSE', offRamp);

            if (!offRamp.success) {
                return this.makeResponse(203, offRamp.data.message || offRamp?.message);
            }
            
            const providerAddress = offRamp.data.address;
            expiresAt = await this.changeTimeStampToDate(offRamp.data.expiresAt);
            externalRefId = offRamp.data.transactionId;
            supportedChain = (data.chain || "").toUpperCase();
            
            mudaAddress = providerAddress; // Use provider's address directly
            evmAddress = providerAddress;
            
            console.log('✅ HoneyCoin off-ramp created:', {
                honeyCoinAddress: mudaAddress,
                chain: supportedChain
            });

        } else if (provider_id == "4") {
            // Quidax off-ramp (service_id 1005)
            console.log('Processing Quidax off-ramp in createOfframpRequest');

            // Get client information
            const client: any = await this.callQuerySafe(
                "SELECT * FROM company_accounts WHERE company_id = ? LIMIT 1",
                [data.company_id]
            );

            if (!client || client.length === 0) {
                return this.makeResponse(203, "Client not found");
            }

            const clientData = client[0];

            // Prepare customer data for Quidax using payment method account_name
            const customerFirstName = paymentMethod.account_name ? paymentMethod.account_name.split(' ')[0] : "Customer";
            const customerLastName = paymentMethod.account_name && paymentMethod.account_name.split(' ').length > 1 ? paymentMethod.account_name.split(' ').slice(1).join(' ') : "User";

            // Log customer details being sent to Quidax
            console.log('Quidax Off-ramp Customer Details:', {
                email: clientData.email,
                first_name: customerFirstName,
                last_name: customerLastName,
                company_id: data.company_id
            });

            // Determine network from asset code
            const network = this.getQuidaxNetworkMapping(data.asset_code || "");
            const baseCurrency = this.extractBaseCurrency(data.asset_code || "");

            console.log(`Quidax off-ramp transaction details: ${data.asset_code} -> base: ${baseCurrency}, network: ${network}`);

            // Step 1: Initiate Quidax off-ramp transaction
            const initiateResponse = await Quidax.initiateOffRampTransaction({
                merchant_reference: transId,
                from_currency: baseCurrency, // Use base currency without chain suffix
                to_currency: (data.receive_currency || "").toLowerCase(),
                from_amount: data.send_amount.toString(),
                network: network,
                customer: {
                    email: clientData.email,
                    first_name: customerFirstName,
                    last_name: customerLastName
                }
            });

            this.LogOperation(transId, 'quidax', 'OFF_RAMP_INITIATE_REQUEST', initiateResponse);

            if (!initiateResponse?.status || initiateResponse?.data?.status !== 'ok') {
                this.LogOperation(transId, 'quidax', 'OFF_RAMP_INITIATE_ERROR', initiateResponse);
                return this.makeResponse(500, "Failed to initiate off-ramp with Quidax", {
                    error: initiateResponse?.data?.message || initiateResponse?.message || "Unknown error"
                });
            }

            const quidaxData = initiateResponse.data.data;

            // Step 2: Add bank account to the transaction
            const bankResponse = await Quidax.addBankAccountToOffRamp(
                transId,
                paymentMethod.bank_code,
                account_number
            );

            this.LogOperation(transId, 'quidax', 'OFF_RAMP_ADD_BANK_REQUEST', bankResponse);

            if (!bankResponse?.status || bankResponse?.data?.status !== 'ok') {
                this.LogOperation(transId, 'quidax', 'OFF_RAMP_ADD_BANK_ERROR', bankResponse);
                return this.makeResponse(500, "Failed to add bank account to off-ramp", {
                    error: bankResponse?.data?.message || bankResponse?.message || "Unknown error"
                });
            }

            // Step 3: Confirm the transaction to get deposit address
            const confirmResponse = await Quidax.confirmOffRampTransaction(transId);
            this.LogOperation(transId, 'quidax', 'OFF_RAMP_CONFIRM_REQUEST', confirmResponse);

            if (!confirmResponse?.status || confirmResponse?.data?.status !== 'ok') {
                this.LogOperation(transId, 'quidax', 'OFF_RAMP_CONFIRM_ERROR', confirmResponse);
                return this.makeResponse(500, "Failed to confirm off-ramp transaction", {
                    error: confirmResponse?.data?.message || confirmResponse?.message || "Unknown error"
                });
            }

            const depositData = confirmResponse.data.data;
            const providerAddress = depositData.address; // Quidax's provider address
            externalRefId = quidaxData.public_id;

            // Set expiration (30 minutes from now as per Quidax documentation)
            expiresAt = new Date(Date.now() + 30 * 60 * 1000)
                .toISOString()
                .slice(0, 19)
                .replace('T', ' ');
            
            // Determine chain from network
            supportedChain = network.toUpperCase();
            
            mudaAddress = providerAddress; // Use provider's address directly
            evmAddress = providerAddress;
            
            console.log('✅ Quidax off-ramp created:', {
                quidaxAddress: mudaAddress,
                chain: supportedChain
            });

            this.LogOperation(transId, 'quidax', 'OFF_RAMP_QUOTE_CREATED', {
                send_asset: data.asset_code,
                receive_currency: data.receive_currency,
                send_amount: data.send_amount,
                receive_amount: quidaxData.to_amount,
                provider_address: providerAddress,
                transaction_type: data.transaction_type,
                quidax_reference: quidaxData.public_id
            });

        } else if (provider_id == "10" || provider_id == "1") {
            // Provider 10 (Trading Service) - create pending rail order with deposit address
            console.log('🚂 Provider 10 - creating pending rail order');

            const paymentMethod = await this.getPaymentMethodById(data.payment_method_id || '');
            const mobileNumber = paymentMethod.length > 0 ?
                (paymentMethod[0].type === 'mobile_money' ? paymentMethod[0].phone_number : paymentMethod[0].account_number)
                : '';


            const postData: CreatePaymentIntentData = {
                reference_id: transId,
                amount: data.send_amount.toString(),
                from_currency: send_asset || "",
                to_currency: data.receive_currency || "",
                chain: data.chain || "",
                payment_method: paymentMethod[0] as MobileMoneyPayment | BankPayment | cryptoPaymentMethod,
                transaction_type: data.transaction_type as 'on_ramp' | 'off_ramp',
            }
            this.LogOperation(transId, 'muda', 'CREATE_PAYMENT_INTENT_REQUEST', postData);
            const response = await mudaProvider.createPaymentIntent(provider_id, postData);
            this.LogOperation(transId, 'muda', 'CREATE_PAYMENT_INTENT_RESPONSE', response);
            if (response?.status === 200) {
                mudaAddress = response.data.address;
                expiresAt = response.data.expires_at;
                externalRefId = response.data.memo;
            } else {
                return this.makeResponse(500, response.message, {
                    error: response.data || ""
                });
            }

        }

        return { mudaAddress, expiresAt, externalRefId, evmAddress, memo };
    }

    async initiateOnRampTransaction(transId: string, provider_id: string, data: SaveQuoteRequest, paymentMethod: any) {

        console.log("Processing Quidax on-ramp in saveQuote");
        try {

            let account_number = (paymentMethod.type === "bank") ? paymentMethod.account_number : paymentMethod.phone_number;
            const customerAddress = data.receiver_address || data.sending_address;
            const internalAddress = customerAddress;
            const account_name = paymentMethod.account_name;
            if (!internalAddress) {
                return this.makeResponse(203, `Internal address not configured for ${data.receive_currency} on ${data.network}`);
            }

            const mudaAddress = internalAddress; // Use internal address for Quidax

            // Store address mapping for later delivery to customer
            const saveAddressMapping = await this.saveAddressMapping(transId, internalAddress, provider_id, customerAddress);
            if (saveAddressMapping === false) {
                return this.makeResponse(203, "Address mapping not saved");
            }

            // Get client details for Quidax
            const client = await this.selectDataQuerySafe("company_accounts", { company_id: data.company_id });
            if (client.length === 0) {
                return this.makeResponse(404, `Client not found`);
            }

            const expiresAt = new Date(Date.now() + 1000 * 60 * 30) // 30 minutes fallback
                .toISOString()
                .slice(0, 19)
                .replace('T', ' ');

            const clientData = client[0];
            if (String(provider_id) === "4") {

                // Prepare customer data for Quidax using payment method account_name
                const customerFirstName = paymentMethod.account_name ? paymentMethod.account_name.split(' ')[0] : "Customer";
                const customerLastName = paymentMethod.account_name && paymentMethod.account_name.split(' ').length > 1 ? paymentMethod.account_name.split(' ').slice(1).join(' ') : "User";

                // Log customer details being sent to Quidax
                console.log('Quidax Customer Details:', {
                    email: clientData.email,
                    first_name: customerFirstName,
                    last_name: customerLastName,
                    company_id: data.company_id
                });

                // Initiate Quidax transaction immediately
                const baseCurrency = data.receive_currency;
                const network = data.network;

                console.log(`Quidax transaction details: ${data.receive_currency} -> base: ${baseCurrency}, network: ${network}`);

                const initiateResponse = await Quidax.initiateOnRampTransaction({
                    merchant_reference: transId,
                    from_currency: (data.send_asset || "").toLowerCase(),
                    to_currency: baseCurrency, // Use base currency without chain suffix
                    from_amount: data.send_amount.toString(), // Keep as string
                    customer: {
                        email: clientData.email,
                        first_name: customerFirstName,
                        last_name: customerLastName
                    },
                    wallet_address: {
                        address: internalAddress, // Use internal address instead of customer address
                        network: network!!
                    }
                });

                this.LogOperation(transId, 'quidax', 'ON_RAMP_INITIATE_REQUEST', initiateResponse);

                if (!initiateResponse?.status || initiateResponse?.data?.status !== 'ok') {
                    this.LogOperation(transId, 'quidax', 'ON_RAMP_INITIATE_ERROR', initiateResponse);
                    return this.makeResponse(500, "Failed to initiate on-ramp with Quidax", {
                        error: initiateResponse?.data?.message || "Unknown error"
                    });
                }

                const quidaxData = initiateResponse.data.data;

                // Confirm the transaction to get bank details
                const confirmResponse = await Quidax.confirmOnRampTransaction(transId);
                this.LogOperation(transId, 'quidax', 'ON_RAMP_CONFIRM_REQUEST', confirmResponse);

                const externalRefId = quidaxData.public_id;



                this.LogOperation(transId, 'quidax', 'ON_RAMP_QUOTE_CREATED', {
                    send_asset: data.send_asset,
                    receive_currency: data.receive_currency,
                    send_amount: data.send_amount,
                    receive_amount: data.send_amount,
                    destination_address: mudaAddress,
                    transaction_type: data.transaction_type,
                    quidax_reference: quidaxData.public_id
                });

                const bankDetails = confirmResponse?.data?.data;
                const paymentInfo: PaymentInformation = bankDetails;
                return { mudaAddress, expiresAt, externalRefId, paymentInfo };


            } else if (String(provider_id) === "6") {

                // Prepare customer data for Quidax using payment method account_name
                const customerFirstName = paymentMethod.account_name ? paymentMethod.account_name.split(' ')[0] : "Muda";
                const customerLastName = paymentMethod.account_name && paymentMethod.account_name.split(' ').length > 1 ? paymentMethod.account_name.split(' ').slice(1).join(' ') : "User";

                // Log customer details being sent to Quidax
                console.log('Honeycoin Customer Details:', {
                    email: clientData.email,
                    first_name: customerFirstName,
                    last_name: customerLastName,
                    company_id: data.company_id
                });

                // Initiate Quidax transaction immediately
                const baseCurrency = data.receive_currency;
                const networkDetails: any = await this.getAssetChain(data?.asset_code || "");
                if (!networkDetails) {
                    return this.makeResponse(500, `Network not supported`, {
                        error: `Network not supported`
                    });
                }
                const network: any = networkDetails;

                const momoOperatorId = paymentMethod.type === "bank" ? "" : "mpesa";
                // remove the + if paymentMethod.type == mobile_money 
                if (paymentMethod.type === "mobile_money") {

                    const assetToSend = (data.send_asset || "").toUpperCase()
                    const validatePhoneNumber: any = await this.honeyCoin.validatePayinPhoneNumber(assetToSend, "KE", account_number);
                    if (!validatePhoneNumber?.status) {
                        return this.makeResponse(500, validatePhoneNumber.message, {
                            error: validatePhoneNumber.data || ""

                        });
                    }
                    account_number = account_number.replace("+", "");
                }

                const validateChains: any = await this.honeyCoin.getAcceptedOnRampChains((network).toUpperCase());
                if (!validateChains) {
                    return this.makeResponse(500, ` ${network.toUpperCase()} network not supported`, {

                        error: `Network ${network} not supported`
                    });
                }


                const validateAssets: any = await this.honeyCoin.getAcceptedOnRampAssets(data.receive_currency.toUpperCase());
                if (!validateAssets) {
                    return this.makeResponse(500, `Receiver  asset ${data.receive_currency} not supported`, {
                        error: `Asset ${data.send_asset} not supported`
                    });
                }

                console.log(`Honeycoin transaction details: ${data.receive_currency} -> base: ${baseCurrency}, network: ${network}`);
                const initiateResponse = await this.honeyCoin.onRamp(data.send_amount,
                    (data.send_asset || "").toLowerCase(),
                    baseCurrency,
                    network!!,
                    clientData.email,
                    momoOperatorId,
                    "MoMo",
                    transId,
                    paymentMethod.type,
                    account_number,
                    `${customerFirstName} ${customerLastName}`,
                    internalAddress,
                    {});

                console.log(`honey coin initiateResponse`, initiateResponse);
                this.LogOperation(transId, 'HoneyCoin', 'ON_RAMP_INITIATE_REQUEST', initiateResponse);
                if (!initiateResponse?.success) {
                    this.LogOperation(transId, 'HoneyCoin', 'ON_RAMP_INITIATE_ERROR', initiateResponse);
                    return this.makeResponse(500, `Failed ${paymentMethod.type} on-ramp. ${initiateResponse?.message}`, {
                        error: initiateResponse?.message || "Unknown error"
                    });
                }

                const honeyCoinData = initiateResponse.data;
                // Confirm the transaction to get bank details
                const confirmResponse: any = await this.honeyCoin.getTransaction(transId);
                console.log(`honey coin confirmResponse`, confirmResponse);
                this.LogOperation(transId, 'HoneyCoin', 'ON_RAMP_CONFIRM_REQUEST', confirmResponse);
                const externalRefId = honeyCoinData.transactionId;
                const providerResponseData = {
                    send_asset: confirmResponse?.data?.senderCurrency || data.send_asset,
                    receive_currency: confirmResponse?.data?.receiverCurrency || data.receive_currency,
                    send_amount: confirmResponse?.data?.senderAmount,
                    receive_amount: confirmResponse?.data?.receiverAmount,
                    destination_address: mudaAddress,
                    transaction_type: data.transaction_type,
                    provider_reference: honeyCoinData.transactionId,
                    payment_info: confirmResponse?.data?.virtualAccount || {}
                }


                this.LogOperation(transId, 'honeyCoin', 'ON_RAMP_QUOTE_CREATED', providerResponseData);
                let bankPaymentDetails: HoneyCoinPaymentInformation = {};
                if (confirmResponse?.data?.virtualAccount) {
                    bankPaymentDetails = {
                        public_id: confirmResponse?.data?.transactionId,
                        account_name: confirmResponse?.data?.virtualAccount?.accountName || "Honeypenny Kenya Limited",
                        account_number: confirmResponse?.data?.virtualAccount?.accountNumber,
                        bank_name: confirmResponse?.data?.virtualAccount?.bankName,
                        bank_code: confirmResponse?.data?.virtualAccount?.bankCode || "",
                        reference: confirmResponse?.data?.transactionId,
                        amount: confirmResponse?.data?.virtualAccount?.amount,
                        amount_expected: confirmResponse?.data?.virtualAccount?.amount,
                        created_at: confirmResponse?.data?.fullTimestamp,
                        updated_at: confirmResponse?.data?.virtualAccount?.createdAt,
                        expires_at: confirmResponse?.data?.virtualAccount?.expiresAt,
                        processor_fee: "",
                        vat: "",
                        note: confirmResponse?.data?.virtualAccount?.note,
                        bank_logo: confirmResponse?.data?.virtualAccount?.bankLogo
                    }
                }

                const paymentDetails = bankPaymentDetails;
                const paymentInfo: HoneyCoinPaymentInformation = paymentDetails;
                const responseData = providerResponseData
                return { mudaAddress, expiresAt, externalRefId, paymentInfo, responseData };

            } else {

                const postData: CreatePaymentIntentData = {
                    reference_id: transId,
                    amount: data.send_amount.toString(),
                    from_currency: data.send_asset || "",
                    to_currency: data.receive_currency || "",
                    payment_method: paymentMethod[0] as MobileMoneyPayment | BankPayment,
                    transaction_type: data.transaction_type as 'on_ramp' | 'off_ramp',
                }
                this.LogOperation(transId, 'muda', 'CREATE_PAYMENT_INTENT_REQUEST', postData);
                const response = await mudaProvider.createPaymentIntent(provider_id, postData);
                this.LogOperation(transId, 'muda', 'CREATE_PAYMENT_INTENT_RESPONSE', response);
                let externalRefId = "";
                let paymentInfo = null;
                if (response.status == 200) {
                    externalRefId = response.data.transaction_id;
                    paymentInfo = response.data;
                }
                return { mudaAddress, expiresAt, externalRefId, paymentInfo };
            }
        } catch (error: any) {
            console.log("ErrorLog", error)
            this.LogOperation(transId, 'ONRAMP', 'CREATE_PAYMENT_INTENT_ERROR', error);
            return this.makeResponse(500, "Error generating the onramp request");
        }

    }


    // Unified webhook processor
    async processReceivedWebhook(webhookPayload: any): Promise<any> {
        try {
            console.log(`Processing webhook from ${webhookPayload.provider}:`, webhookPayload);

            const transactionInfo = await this.findTransactionByWebhook(webhookPayload);
            if (!transactionInfo) {
                return this.makeResponse(404, "Transaction not found");
            }

            const normalizedStatus = webhookPayload.status;
            const eventType = webhookPayload.eventType;

            console.log(`normalizedStatus`, normalizedStatus)

            //  const normalizedStatus = this.normalizeWebhookStatus(webhookPayload.status, webhookPayload.provider);

            // Update transaction status
            await this.updateWebhookTransactionStatus(transactionInfo, normalizedStatus, webhookPayload);

            // Send outgoing webhook based on current status
            //   await WebhookSender.send(transactionInfo.transId);

            // If payment is successful, process payout
            const transaction_type = transactionInfo.transaction_type;
            if (normalizedStatus === 'SUCCESSFUL' && transaction_type == 'off_ramp') {

                if (eventType === 'fiat_sent') {
                    this.updateFinalTransaction(webhookPayload.transactionId, 'SUCCESSFUL', "SUCCESSFUL", "");
                    await WebhookSender.send(transactionInfo.transId);
                    return this.makeResponse(200, "Webhook processed and payout initiated");
                } else if (eventType === 'crypto_received') {
                    const payoutRequest: PayoutRequest = {
                        transId: transactionInfo.transId,
                        chain: transactionInfo.chain,
                        providerId: transactionInfo.provider_id,
                        serviceId: transactionInfo.service_id,
                        currency: transactionInfo.receive_currency,
                        asset_code: transactionInfo.send_asset,
                        amount: transactionInfo.payable_amount,
                        fiatAmount: transactionInfo.receive_amount,
                        accountNumber: transactionInfo.account_number,
                        paymentMethodId: transactionInfo.payment_method_id
                    };

                    const payoutResult = await this.processSwapRequest(payoutRequest);

                    // Send webhook after payout completion (status will be updated in processPayout)
                } else {
                    console.log("Webhook processed and payout not initiated");
                }

                return this.makeResponse(200, "Webhook processed and payout initiated");
            } else if (transaction_type == 'onramp') {

                if (eventType === 'fiat_received') {
                    this.updatePayinTransaction(webhookPayload.transactionId, normalizedStatus, normalizedStatus, "", "PENDING");
                    await WebhookSender.send(transactionInfo.transId);
                    return this.makeResponse(200, "Webhook processed and payout initiated");
                } else if (eventType === 'crypto_sent') {
                    this.updateFinalTransaction(webhookPayload.transactionId, normalizedStatus, normalizedStatus, "");
                    await WebhookSender.send(transactionInfo.transId);
                    return this.makeResponse(200, "Webhook processed and payout initiated");
                } else {
                    console.log("Webhook processed and payout not initiated");
                }
            }

            return this.makeResponse(200, "Webhook processed successfully");
        } catch (error) {
            console.error("Error processing webhook:", error);
            return this.makeResponse(500, "Error processing webhook");
        }
    }

    // Find transaction based on webhook data
    private async findTransactionByWebhook(webhook: any): Promise<any> {
        let query = '';
        const eventType = webhook.eventType;
        let receivedData: any = null;
        if (eventType === 'crypto_received') {
            receivedData = webhook as CryptoReceivedWebhook;
        } else {
            receivedData = webhook
        }
        this.saveApiLog(receivedData, 'findTransactionByWebhook');
        console.log(`receivedData`, receivedData)
        switch (receivedData.provider) {
            case 'muda':
                if (eventType == 'crypto_received') {
                    query = `send_amount = ${receivedData.amount} AND  LOWER(receiver_address) = LOWER('${receivedData.to_address}')  AND send_asset = '${receivedData.asset_code}' AND status='PENDING'`;
                } else {
                    query = `transId = '${receivedData.reference_id || receivedData.transactionId}' AND status='PENDING'`;
                }
                break;
            case 'fireblocks':
            case 'quidax':
                query = `send_amount = ${receivedData.amount} AND LOWER(receiver_address) = LOWER('${receivedData.to_address}')  AND send_asset = '${receivedData.asset_code}' AND status='PENDING'`;
                break;
            case 'kotani':
            case 'general':
            case 'moralis':
                query = `send_amount = '${webhook.amount}' AND status='PENDING'`;
                break;
            case 'stellar':
                query = `send_amount = ${receivedData.amount} AND id = '${receivedData.memo}' AND LOWER(receiver_address) = LOWER('${receivedData.to_address}')  AND send_asset = '${receivedData.asset_code}' AND status='PENDING'`;
                break;
            default:
                query = `send_amount = ${receivedData.amount} AND LOWER(sending_address) = LOWER('${receivedData.from_address}') AND LOWER(receiver_address) = LOWER('${receivedData.to_address}')  AND send_asset = '${receivedData.asset_code}' AND status='PENDING'`;
        }
        this.saveApiLog(query, 'findTransactionByWebhook');

        const result: any = await this.callRawQuery(`SELECT * FROM quotes WHERE ${query}`);
        return result.length > 0 ? result[0] : null;
    }



    // Update transaction status with webhook data
    private async updateWebhookTransactionStatus(transaction: any, status: string, webhook: WebhookPayload): Promise<void> {
        const updates: any = {
            pay_in_status: status
        };

        // Only update hash for crypto webhooks that have a hash property
        if ('hash' in webhook && webhook.hash) {
            updates.hash = webhook.hash;
        }

        updates.narration = `Provider: ${webhook.provider}, Status: ${webhook.status}, Type: ${webhook.eventType}`;

        await this.updateData('quotes', `transId = '${transaction.transId}'`, updates);
    }





    async processSwapRequest(payoutRequest: PayoutRequest) {
        try {
            console.log(`Processing swap and withdraw to the provider:`, payoutRequest);

            let swapResponse: any = null;

            const stableAssets = ['USDT', 'USDC']
            const chain = payoutRequest.chain?.toLocaleLowerCase() || ''
            const sentAsset = payoutRequest.asset_code.toLocaleLowerCase()
            let path = [sentAsset, 'usdt']
            const getlogmapping: any = await this.selectDataQuerySafe("quote_address_mapping", { quote_id: payoutRequest.transId })
            const mudaAddress = getlogmapping[0].muda_address
            const providerAddress = getlogmapping[0].provider_address

            // Detect if the providerAddress is a Tron or BSC address
            // Tron addresses typically start with 'T' and are 34 chars, BSC are 42 chars and start with '0x'
            let addressType = '';
            if (typeof providerAddress === 'string') {
                if (providerAddress.startsWith('T') && providerAddress.length === 34) {
                    addressType = 'tron';
                } else if (providerAddress.startsWith('0x') && providerAddress.length === 42) {
                    addressType = 'bsc';
                } else {
                    addressType = 'unknown';
                }
            }
            const providerId = payoutRequest.providerId
            console.log(`Detected provider address type:`, addressType);
            const memo = getlogmapping[0].memo
            console.log(`payoutRequest`, payoutRequest)

            let settlementAddress = providerAddress
            if (providerId == '2') {
                const response = await this.createKotaniOffRamp(payoutRequest.paymentMethodId || '', payoutRequest.currency, chain, payoutRequest.asset_code, payoutRequest.amount, payoutRequest.transId, settlementAddress)
                if (response != null) {
                    settlementAddress = response
                }
            }
            const swappableChains = ['tron', 'bsc']
            const nowSwap = ['stellar']
            // const isSwappable = swappableChains.includes(chain)
            const isNowSwap = nowSwap.includes(chain)

            const swapCurrency = ['ugx', 'ghs', 'kes', 'ngn']
            const nowSwapCurrency = ['ugx']

            let isSwappable = false;
            if (isNowSwap) {
                isSwappable = true;
            }
            const currency = payoutRequest.currency.toLowerCase()
            const isSwapCurrency = swapCurrency.includes(currency)
            if (isSwapCurrency) {
                isSwappable = true;
            } else {
                isSwappable = false;
            }

            console.log(`isSwappable`, isSwappable)
            if (sentAsset.toLowerCase() == 'cngn' || providerId == '4') {
                if (sentAsset == 'cngn') {
                    const swap1 = await this.swapQuidax(sentAsset, 'ngn', Number(payoutRequest.amount), this.getRandomString())
                    this.LogOperation(payoutRequest.transId, 'quidax', 'SWAP1', swap1)

                    if (currency.toLowerCase() != 'ngn') {
                        const swap2 = await this.swapQuidax('ngn', 'usdt', Number(payoutRequest.amount), this.getRandomString())
                        this.LogOperation(payoutRequest.transId, 'quidax', 'SWAP2', swap2)
                    }
                }

                if (payoutRequest.currency.toLowerCase() == 'ngn') {
                    const swap1 = await this.swapQuidax(sentAsset, 'ngn', Number(payoutRequest.amount), this.getRandomString())
                    this.LogOperation(payoutRequest.transId, 'quidax', 'SWAP1', swap1)

                    return await this.processPayout(payoutRequest);

                } else {
                    this.LogOperation(payoutRequest.transId, 'quidax', 'WITHDRAW', payoutRequest.amount)
                    const response = await this.QuidaxWithdraw(settlementAddress, 'usdt', Number(payoutRequest.amount), "qw_" + payoutRequest.transId, "");
                    this.LogOperation(payoutRequest.transId, 'quidax', 'WITHDRAW_RESPONSE', response)

                    if (response.status === 'success') {
                        return await this.processPayout(payoutRequest);
                    }
                }
            } else {
                return this.processPayout(payoutRequest);
            }

        } catch (error) {
            console.error("Error processing payout:", error);
            return {
                success: false,
                status: 'FAILED',
                message: 'Payout processing error'
            };
        }




    }

    async swapQuidax(from: string, to: string, amount: number, transId: string) {
        const quoteResponse: any = await this.getQuidaxQuote(from, to, amount);
        this.LogOperation(transId, 'quidax', 'QUOTATION_REQUEST', { from, to, amount })
        const provider_quote_id = quoteResponse?.providerQuoteId || "";
        const swapResponse = await Quidax.confirmCryptoFiatSwap('me', provider_quote_id);
        this.LogOperation(transId, 'quidax', 'QUOTATION_RESPONSE', swapResponse)
    }



    async   processQuidaxOnRampWebhook(webhookData: any) {
        try {
            console.log("Processing Quidax on-ramp webhook:", webhookData);
            this.LogOperation(webhookData.transId, 'quidax', 'ON_RAMP_WEBHOOK_RECEIVED', webhookData);

            const processedWebhook = Quidax.handleOnRampWebhooks(webhookData);
            const transId = processedWebhook.merchant_reference;

            if (!transId) {
                console.error("No merchant reference found in webhook");
                return this.makeResponse(400, "Invalid webhook data");
            }

            this.LogOperation(transId, 'quidax', 'ON_RAMP_WEBHOOK_RECEIVED', processedWebhook);

            // Get transaction details
            const transaction = await this.selectDataQuerySafe("quotes", { transId });
            if (transaction.length === 0) {
                console.error(`Transaction not found: ${transId}`);
                return this.makeResponse(404, "Transaction not found");
            }

            const txData = transaction[0];

            if (processedWebhook.status === 'completed') {
                this.updateFinalTransaction(transId, "SUCCESSFUL", "On-ramp completed successfully", transaction);
                const webhookPayload = await WebhookFactory.createFiatReceived(webhookData.data, 'quidax');
                this.LogOperation(transId, 'quidax', 'QUIDAX_ONRAMP_WEBHOOK_SUCCESS', webhookPayload);

                return this.makeResponse(200, "On-ramp completed successfully");

            } else if (processedWebhook.status === 'processing') {
                this.updatePayinTransaction(transId, "SUCCESSFUL", "On-ramp processing", processedWebhook, "PENDING");
                return this.makeResponse(202, "On-ramp processing status updated", {
                    transaction_id: transId,
                    status: 'processing'
                });

            } else if (processedWebhook.status === 'failed') {
                // Update transaction as failed
                this.updateFinalTransaction(transId, "FAILED", "On-ramp failed", transaction);
                this.updatePayinTransaction(transId, "FAILED", "On-ramp failed", processedWebhook, "FAILED");

                this.LogOperation(transId, 'quidax', 'ON_RAMP_WEBHOOK_FAILED', processedWebhook);

                return this.makeResponse(200, "On-ramp failure processed", {
                    transaction_id: transId,
                    status: 'failed',
                    reason: processedWebhook.reason
                });
            }

            return this.makeResponse(200, "Webhook received but no action taken");

        } catch (error: any) {
            console.error("Error processing Quidax on-ramp webhook:", error);
            return this.makeResponse(500, "Internal server error");
        }
    }

    async processQuidaxOffRampWebhook(webhookData: any) {
        try {
            console.log("Processing Quidax off-ramp webhook:", webhookData);
            this.LogOperation(webhookData.transId, 'quidax', 'OFF_RAMP_WEBHOOK_RECEIVED', webhookData);

            const processedWebhook = Quidax.handleOffRampWebhooks(webhookData);
            const transId = processedWebhook.merchant_reference;

            if (!transId) {
                console.error("No merchant reference found in off-ramp webhook");
                return this.makeResponse(400, "Invalid webhook data");
            }

            this.LogOperation(transId, 'quidax', 'OFF_RAMP_WEBHOOK_PROCESSED', processedWebhook);

            // Get transaction details
            const transaction = await this.selectDataQuerySafe("quotes", { transId });
            if (transaction.length === 0) {
                console.error(`Off-ramp transaction not found: ${transId}`);
                return this.makeResponse(404, "Transaction not found");
            }

            const txData = transaction[0];

            if (processedWebhook.status === 'completed') {
                // Update transaction as successful
                this.updateFinalTransaction(transId, "SUCCESSFUL", "Off-ramp completed successfully", transaction);

                // Create webhook payload for successful off-ramp
                const webhookPayload = await WebhookFactory.createCryptoReceived(processedWebhook, 'quidax');
                this.LogOperation(transId, 'quidax', 'QUIDAX_OFFRAMP_WEBHOOK_SUCCESS', webhookPayload);

                // Send webhook notification
                await this.processReceivedWebhook(webhookPayload);

                return this.makeResponse(200, "Off-ramp completed successfully", {
                    transaction_id: transId,
                    status: 'completed',
                    crypto_amount: processedWebhook.crypto_amount,
                    fiat_amount: processedWebhook.fiat_amount,
                    transaction_hash: processedWebhook.transaction_hash
                });

            } else if (processedWebhook.status === 'processing') {
                // Update transaction as processing
                this.updatePayinTransaction(transId, "PROCESSING", "Off-ramp processing", processedWebhook, "PENDING");

                return this.makeResponse(202, "Off-ramp processing status updated", {
                    transaction_id: transId,
                    status: 'processing',
                    crypto_amount: processedWebhook.crypto_amount,
                    fiat_amount: processedWebhook.fiat_amount
                });

            } else if (processedWebhook.status === 'failed') {
                // Update transaction as failed
                this.updateFinalTransaction(transId, "FAILED", "Off-ramp failed", transaction);
                this.updatePayinTransaction(transId, "FAILED", "Off-ramp failed", processedWebhook, "FAILED");

                this.LogOperation(transId, 'quidax', 'OFF_RAMP_WEBHOOK_FAILED', processedWebhook);

                return this.makeResponse(200, "Off-ramp failure processed", {
                    transaction_id: transId,
                    status: 'failed',
                    reason: processedWebhook.reason || "Transaction failed"
                });
            }

            return this.makeResponse(200, "Off-ramp webhook received but no action taken");

        } catch (error: any) {
            console.error("Error processing Quidax off-ramp webhook:", error);
            return this.makeResponse(500, "Internal server error");
        }
    }


    async QuidaxWithdraw(escrowAddress: string, currency: string, amount: number, transId: string, bankCode: string) {
        const withdrawData: QuidaxWithdrawData = {
            user: 'me',
            currency: currency,
            amount: amount.toString(),
            transaction_note: transId,
            narration: 'MUDA PAY',
            fund_uid: escrowAddress,
            fund_uid2: bankCode,
            reference: transId,
            network: 'bep20'
        }
        this.LogOperation(transId, 'quidax', 'QUIDAX_WITHDRAW_REQUEST', withdrawData)
        const response = await Quidax.createWithdraw(withdrawData);
        this.LogOperation(transId, 'quidax', 'QUIDAX_WITHDRAW_RESPONSE', response)

        let resp: any = null;
        if (response.status === 'success') {
            resp = {
                status: 200,
                message: response.message,
                transactionId: response.data.id
            }
        } else {
            resp = {
                status: 203,
                message: response.message
            }
        }
        return resp
    }

    async processPayout(payoutRequest: PayoutRequest): Promise<PayoutResponse> {
        try {

            console.log(`Waiting for 30 seconds:`, payoutRequest.transId);
            await new Promise(resolve => setTimeout(resolve, 30000));
            console.log(`Processing payout:`, payoutRequest.transId);
            this.LogOperation(payoutRequest.transId, 'processPayou', 'PROCESS_PAYOUT_REQUEST', payoutRequest)

            // Get payment method details if needed
            let paymentMethod = null;
            if (payoutRequest.paymentMethodId) {
                const paymentMethods = await this.getPaymentMethodById(payoutRequest.paymentMethodId);
                paymentMethod = paymentMethods.length > 0 ? paymentMethods[0] : null;
            }

            const quoteResponse: any = await this.selectDataQuerySafe("quote_log", { quoteId: payoutRequest.transId });
            const quotation_id = quoteResponse[0].providerQuoteId;

            let response: any = null;
            const { currency, transId, accountNumber, fiatAmount } = payoutRequest;


            // Route to appropriate payment processor based on currency
            switch (currency) {
                case 'UGX':
                    const rspInfo = await MudaPayment.makePayout(transId, parseFloat(fiatAmount), accountNumber);
                    response = {
                        status: rspInfo.statusCode,
                        message: rspInfo.message,
                        data: rspInfo.data
                    }
                    break;

                case 'GHS':
                    const accountName = paymentMethod?.account_name || "MUDA PAY USER";
                    response = await Ogateway.sendToMobile(transId, parseFloat(fiatAmount), accountName, accountNumber);
                    break;

                case 'NGN':
                    const bankCode = paymentMethod.bank_code;
                    response = await this.QuidaxWithdraw(accountNumber, 'ngn', Number(fiatAmount), transId, bankCode);
                    this.LogOperation(transId, 'quidax', 'QUIDAX_CONVERT_RESPONSE', response)
                    break;
                case 'ZAR':
                    await this.updateData('quotes', `transId = '${transId}'`, { status: 'PENDING_PROVIDER' });
                    break;
                case 'KES':
                    await this.updateData('quotes', `transId = '${transId}'`, { status: 'PENDING_PROVIDER' });
                    break;

                default:
                    return {
                        success: false,
                        status: 'FAILED',
                        message: `Currency ${currency} not supported`
                    };
            }

            // Process response and update transaction
            if (response && (response.status === 200 || response.status === 202)) {
                await this.updateData('quotes', `transId = '${transId}'`, { status: 'SUCCESSFUL' });
                this.LogOperation(transId, 'processPayout', 'PROCESS_PAYOUT_SUCCESS', response)
                // Send webhook for successful payout
                await WebhookSender.send(transId);

                return {
                    success: true,
                    status: 'SUCCESSFUL',
                    message: 'Payout processed successfully',
                    transactionId: transId,
                    providerResponse: response.data
                };
            } else {
                await this.updateData('quotes', `transId = '${transId}'`, { status: 'ONHOLD' });
                this.LogOperation(transId, 'processPayout', 'PROCESS_PAYOUT_ONHOLD', response)
                // Send webhook for failed payout
                await WebhookSender.send(transId);

                return {
                    success: false,
                    status: 'ONHOLD',
                    message: 'Payout failed',
                    transactionId: transId,
                    providerResponse: response?.data
                };
            }

        } catch (error) {
            console.error("Error processing payout:", error);

            await this.updateData('quotes', `transId = '${payoutRequest.transId}'`, { status: 'FAILED' });

            // Send webhook for payout error
            await WebhookSender.send(payoutRequest.transId);

            return {
                success: false,
                status: 'FAILED',
                message: 'Payout processing error'
            };
        }
    }

    // Webhook methods - now using WebhookFactory
    async webhooksMuda(data: MudaWebhook) {
        try {
            let event = data.event
            const getRandomString = data.transaction_id
            const status = data.statusCode
            const direction = data.trans_type
            this.LogOperation(getRandomString, 'muda', 'MUDA_WEBHOOK', data)

            const allowedEvents = ['coin.deposit', 'coin.transfer', 'coin.withdraw', 'fiat.deposit', 'fiat.transfer']
            if (!allowedEvents.includes(event)) {
                return this.makeResponse(200, "Event not processed");
            }
            if (event === 'coin.deposit') {
                const webhookPayload = WebhookFactory.createCryptoReceived(data, 'muda');
                return await this.processReceivedWebhook(webhookPayload);
            }

            if (event === 'fiat.transfer') {
                const webhookPayload = WebhookFactory.createFiatSent(data, 'muda');
                return await this.processReceivedWebhook(webhookPayload);
            }
            this.LogOperation(getRandomString, 'muda', 'MUDA_WEBHOOK_NOT_PROCESSED', data)
            return this.makeResponse(203, "Event not processed");
        } catch (error) {
            return this.makeResponse(203, "Event not processed");
        }

    }

    async webhooksFireblocks(pdata: any) {
        if (pdata.event === 'TRANSACTION_STATUS_UPDATED') {
            const webhookPayload = await WebhookFactory.createCryptoReceived(pdata.data, 'fireblocks');
            return await this.processReceivedWebhook(webhookPayload);
        }
        return this.makeResponse(200, "Event not processed");
    }

    async webhook_stellar(pdata: any) {
        const getRandomString = this.getRandomString();
        this.LogOperation(getRandomString, 'stellar', 'STELLAR_WEBHOOK', pdata)
        const webhookPayload = await WebhookFactory.createCryptoReceived(pdata.data, 'stellar');
        this.LogOperation(getRandomString, 'stellar', 'STELLAR_WEBHOOK_DEPOSIT', webhookPayload)
        return await this.processReceivedWebhook(webhookPayload);
    }

    async railEvents(pdata: any) {
        const clientId = pdata.clientId
        const getRandomString = this.getRandomString();
        const allowedEvents = ['fiat_sent', 'fiat_received', 'crypto_received', 'crypto_sent']
        if (!allowedEvents.includes(pdata.eventType)) {
            return this.makeResponse(400, "Event type not allowed");
        }

        this.LogOperation(getRandomString, 'LR_PROVIDER_EVENT', 'LR_PROVIDER_EVENT', pdata)
        const webhookPayload: LRProviderEvents = WebhookFactory.createLRProviderEvents(pdata);
        const referenceId = webhookPayload.reference_id
        const status = webhookPayload.status
        const eventType = webhookPayload.eventType
        const data = webhookPayload.data

        const quote: any = await this.selectDataQuerySafe("quotes", { transId: referenceId });
        if (quote.length == 0) {
            return this.makeResponse(404, "Quote not found");
        }
        this.LogOperation(referenceId, 'LR_PROVIDER_TRANSACTION', 'LR_PROVIDER_TRANSACTION', pdata)

        const quoteData = quote[0]
        const quoteId = quoteData.transId
        const providerId = quoteData.provider_id
        const companyId = quoteData.company_id
        const serviceId = quoteData.service_id
        const amount = quoteData.amount
        const currency = quoteData.currency
        const transaction_type = quoteData.transaction_type

        if (transaction_type === 'on_ramp') {
            return this.makeResponse(200, "OK");
        } else if (transaction_type === 'off_ramp') {
            if (eventType == "crypto_received") {
                const cryptoData = data as cryptoEvent;
                const amount = cryptoData.amount;
                const asset_code = cryptoData.asset_code;
                const from_address = cryptoData.from_address;
                const to_address = cryptoData.to_address;
                const contract_address = cryptoData.contract_address;
                const fee = cryptoData.fee;
                const message = `Crypto received: ${amount} ${asset_code} from ${from_address} to ${to_address}`;

                if (status == "SUCCESSFUL") {
                    await this.updatePayinTransaction(referenceId, "SUCCESSFUL", message, "");
                } else if (status == "FAILED") {
                    await this.updatePayinTransaction(referenceId, "FAILED", message, "");
                    await this.updateFinalTransaction(referenceId, "FAILED", message, "");
                } else if (status == "CANCELLED") {
                    await this.updatePayinTransaction(referenceId, "CANCELLED", message, "");
                    await this.updateFinalTransaction(referenceId, "CANCELLED", message, "");
                } else if (status == "EXPIRED") {
                    await this.updatePayinTransaction(referenceId, "EXPIRED", message, "");
                    await this.updateFinalTransaction(referenceId, "EXPIRED", message, "");

                } else {
                    await this.updatePayinTransaction(referenceId, status, message, "");
                }
            } else if (eventType == "fiat_sent") {
                const fiatData = data as unknown as fiatEvent;
                const amount = fiatData.amount;
                const currency = fiatData.currency;
                const message = `Fiat sent: ${amount} ${currency}`;
                if (status == "SUCCESSFUL") {
                    await this.updateFinalTransaction(referenceId, "SUCCESSFUL", message, "");
                } else if (status == "FAILED") {
                    await this.updateFinalTransaction(referenceId, "FAILED", message, "");
                } else if (status == "CANCELLED") {
                    await this.updateFinalTransaction(referenceId, "CANCELLED", message, "");
                } else if (status == "EXPIRED") {
                    await this.updateFinalTransaction(referenceId, "EXPIRED", message, "");
                } else {
                    await this.updateFinalTransaction(referenceId, "ONHOLD", message, "");
                }
            }
        }

        return this.makeResponse(200, "OK");
    }

    async webhooksQuidax(pdata: any) {
        try {
            const { event } = pdata;
            const type = pdata.data?.type;
            const getRandomString = this.getRandomString();
            this.LogOperation(getRandomString, 'quidax', 'QUIDAX_WEBHOOK', pdata)

            if (event === 'deposit.successful') {
                if (type === 'coin_address') {
                    const webhookPayload = await WebhookFactory.createCryptoReceived(pdata.data, 'quidax');
                    this.LogOperation(getRandomString, 'quidax', 'QUIDAX_WEBHOOK_DEPOSIT', webhookPayload)

                    return await this.processReceivedWebhook(webhookPayload);
                } else {
                    const webhookPayload = WebhookFactory.createFiatReceived(pdata.data, 'quidax');
                    return await this.processReceivedWebhook(webhookPayload);
                }
            } else if (event === 'buy_transaction.successful' || event === 'buy_transaction.processing' || event === 'buy_transaction.failed') {
                // Handle on-ramp events - WebhookFactory will be called inside processQuidaxOnRampWebhook only if successful

                // add there an interface implementaion for ProcessedQuidaxWebhook
                //    const webhookPayload = WebhookFactory.createFiatReceived(pdata.data, 'quidax');

                return await this.processQuidaxOnRampWebhook(pdata);
            } else if (event === 'sell_transaction.successful' || event === 'sell_transaction.processing' || event === 'sell_transaction.failed') {
                // Handle off-ramp events using the new off-ramp webhook processor
                this.LogOperation(getRandomString, 'quidax', 'QUIDAX_OFFRAMP_WEBHOOK_RECEIVED', pdata);
                return await this.processQuidaxOffRampWebhook(pdata);
            } else if (event === 'withdraw.successful') {
                // Handle legacy withdraw events - for now just log
                this.LogOperation(getRandomString, 'quidax', 'QUIDAX_WEBHOOK_WITHDRAW', pdata);
                return this.makeResponse(200, "Withdraw event logged");
            }

            return this.makeResponse(200, "Event not processed");
        } catch (error) {
            console.log("Quidax webhook error:", error);
            return this.makeResponse(200, "Error processed");
        }
    }

    async webhookStella(pdata: any) {
        const { event } = pdata;
        const type = pdata.type;
        if (event === 'token_payment') {
            const webhookPayload = await WebhookFactory.createCryptoReceived(pdata.data, 'stellar');
            return await this.processReceivedWebhook(webhookPayload);
        }
        return this.makeResponse(200, "Event not processed");
    }

    // General webhook for external systems
    async webhooks(data: any) {
        const webhookPayload = WebhookFactory.createGeneralStatus(data);
        return await this.processReceivedWebhook(webhookPayload);
    }
    async banks(currency: any) {
        if (currency == '') {
            return await this.callRawQuery("SELECT * FROM banks")
        }
        // fetch KES banks
        if (currency == "KES") {
            const banksList: any = await this.honeyCoin.getBanks("KE", currency);
            return banksList;
        }

        return await this.callQuerySafe("SELECT * FROM banks WHERE currency = ? AND status = 'ACTIVE'", [currency])
    }
    async getTransactions(id: string) {
        const resp: any = await this.callQuerySafe(
            "SELECT * FROM quotes s INNER JOIN providers p ON s.provider_id = p.provider_id INNER JOIN services r ON r.service_id = s.service_id WHERE s.company_id = ? ORDER BY s.id DESC",
            [id]
        )
        console.log(`resp====>`, resp)
        return this.makeResponse(200, "success", resp)
    }

    // return a branch per banj selected 
    async getBranchPerBank(bank_code: string) {
        try {
            const banksBranchList = await this.honeyCoin.getBankBranch(bank_code);
            return this.makeResponse(200, "success", banksBranchList)
        } catch (error: any) {
            console.error("Error in getBranchPerBank:", []);
            console.error("Error gettting branches", []);
        }
    }



    async getSingleTransaction(transId: any) {
        try {
            const rsp: any = await this.callQuerySafe(
                "SELECT * FROM quotes s INNER JOIN providers p ON s.provider_id = p.provider_id INNER JOIN services r ON r.service_id = s.service_id WHERE transId = ?",
                [transId]
            )
            if (rsp.length > 0) {
                const resp_format = {
                    thirdparty_trans_id: rsp[0].id,
                    amount_to_pay: rsp[0].receive_amount,
                    amount_paid: rsp[0].receive_amount,
                    currency: rsp[0].currency,
                    fee: rsp[0].fee,
                    ext_trans_id: rsp[0].transId,
                    account_number: rsp[0].account_number,
                    status: rsp[0].account_number,
                    quote_id: transId,
                    date: rsp[0].created_on,
                    channel: "MTN"

                };
                return this.makeResponse(200, "success", resp_format);
            } else {
                return this.makeResponse(404, "not found");


            }
        } catch (error) {
            console.error("Error in updateService:", error);
            return this.makeResponse(203, "Error");
        }
    }

    async getTransactionByRefTransId(transId: any) {
        const rsp: any = await this.callQuerySafe("SELECT * FROM quotes WHERE client_reference_id = ?", [transId])
        if (rsp.length > 0) {
            return this.makeResponse(200, "success", rsp[0]);
        } else {
            return this.makeResponse(203, "Error getting transaction");


        }
    }

    async getTransaction(transId: any) {
        try {
            const rsp: any = await this.callQuerySafe(
                "SELECT * FROM quotes s INNER JOIN providers p ON s.provider_id = p.provider_id INNER JOIN services r ON r.service_id = s.service_id WHERE transId = ?",
                [transId]
            )

            if (rsp.length > 0) {
                return this.makeResponse(200, "success", rsp[0]);
            } else {
                return this.makeResponse(203, "Error getting transaction");


            }
        } catch (error) {
            console.error("Error in updateService:", error);
            return this.makeResponse(203, "Error");
        }
    }


    async bookRate(data: RateRequest) {
        const qtId = "md" + this.getRandomString();
        const rate = await this.getRate(data, qtId);
        return rate
    }

    async createKotaniOffRamp(payment_method_id: string, receive_currency: string, chains: string, send_asset: string, send_amount: string, quoteId: string, sending_address: string) {

        let chain = "BASE"
        const paymentMethod = await this.getPaymentMethodById(payment_method_id || "");
        if (paymentMethod.length == 0) {
            return false;
        }
        const paymentMethodData: PaymentMethod = paymentMethod[0];

        const { phone_number, network, bank_code, bank_address, account_number, type, account_name, bank_country } = paymentMethodData;
        const bankCode = bank_code;

        const MMObject: any = {
            phoneNumber: account_number,
            networkProvider: network || "",
            currency: receive_currency.toLowerCase(),
            chain: chain,
            accountName: account_name,
            token: send_asset,
            cryptoAmount: Number(send_amount),
            referenceId: quoteId,
            senderAddress: sending_address
        }

        const BankObject: any = {
            accountName: account_name,
            accountNumber: account_number,
            address: bank_address,
            phoneNumber: phone_number,
            bankCode: Number(bankCode),
            chain: chain,
            token: send_asset,
            currency: receive_currency,
            cryptoAmount: Number(send_amount),
            referenceId: quoteId,
            country: bank_country,
            senderAddress: sending_address
        }
        let kotaniOffRamp: any = null;
        if (type == "mobile_money") {
            this.LogOperation(quoteId, 'kotani', 'KOTANI_OFFRAMP_REQUEST_MM', MMObject)
            kotaniOffRamp = await kotaniPay.createMobileMoneyOfframp(MMObject);
            this.LogOperation(quoteId, 'kotani', 'KOTANI_OFFRAMP_RESPONSE_MM', kotaniOffRamp)
        } else {
            this.LogOperation(quoteId, 'kotani', 'KOTANI_OFFRAMP_REQUEST_BANK', BankObject)
            kotaniOffRamp = await kotaniPay.createBankOfframp(BankObject);
            this.LogOperation(quoteId, 'kotani', 'KOTANI_OFFRAMP_RESPONSE_BANK', kotaniOffRamp)
        }
        this.LogOperation(quoteId, 'kotani', 'KOTANI_OFFRAMP_RESPONSE', kotaniOffRamp)
        if (kotaniOffRamp.success) {
            const escrowAddress = kotaniOffRamp.data.escrowAddress;
            return escrowAddress;
        }
        return null;
    }




    async getRate(data: RateRequest, quoteId: string = "") {
        try {

            console.log(`getRate`, data)
            const { amount, currency } = data
            let provider_id = data.provider_id || null
            let service_code = data.service_code || ""
            let service_id = data.service_code || ""
            let transaction_type = data.transaction_type || "off_ramp"
            let chain = data.chain || "bsc"

            // Handle provider 10 (Trading Service) - get deposit address


            // restrict on ramp support to off_ramp and on_ramp              
            const supportedCurrencyRamps = ["off_ramp", "on_ramp"]
            if (!supportedCurrencyRamps.includes(transaction_type)) {
                return this.makeResponse(203, "Not supported transaction type");
            }

            if (transaction_type === "on_ramp") {
                // Enable on-ramp processing
                console.log("Processing on-ramp request");
            }
            let supportsMudaProviderFlow = 0

            if (provider_id == null) {
                console.log(`step12`, service_code, currency)
                const provider: any = await this.searchprovider(service_code, currency)
                if (provider.length == 0) {
                    return this.makeResponse(203, "No provider found");
                }
                provider_id = provider[0].provider_id
                supportsMudaProviderFlow = provider[0].supports_muda_pg

            }

            if (service_id != "") {
                const service = await this.getService(service_id)
                console.log(`servicesInformato`, service)
                if (service.status == 200) {
                    const serviceData = service.data
                    console.log(`serviceData`, serviceData)
                    service_id = serviceData.service_id
                } else {
                    return this.makeResponse(203, "Service not found");
                }
            }


            // Skip provider validation for provider 10 (Trading Service)
            if (provider_id !== 10) {
                const getprovider: any = await this.validateProviderCurrency(String(provider_id), String(currency));
                if (getprovider.length == 0) {
                    return this.makeResponse(203, "Provider does not support this currency");
                }
            }




            let symbol = data.asset_code || ""
            console.log(`step11`, symbol)

            // Parse the original asset code to get asset and chain
            const parsed = this.parseAssetCode(symbol);
            const originalAsset = parsed.asset;
            const originalChain = parsed.chain;

            let chain_ = originalChain || await this.getchain(symbol)

            let assetCode;
            if (transaction_type === "on_ramp") {

                assetCode = symbol; // Keep original asset code like "USDC_BASE"
                console.log(`On-ramp: preserving original asset code: ${assetCode}`);
            } else {
                assetCode = await this.getAssetCode(symbol); // Convert to asset name like "USDC"
                console.log(`Off-ramp: converted asset code: ${symbol} -> ${assetCode}`);
            }

            if (!assetCode) {
                return this.makeResponse(203, "Asset code not supported");
            }
            const supportedCurrency = await this.getAssetCode(currency)
            if (!supportedCurrency) {
                return this.makeResponse(203, "Currency not supported");
            }


            const { mudaFee, payableAmount } = await this.calculateMudaFee(chain, amount)

            // For off-ramp transactions, calculate additional Rhino transfer costs
            let rhinoTransferFee = 0;
            const numericAmount = Number(amount); // Convert to number for calculations
            let finalProviderAmount = numericAmount; // Start with full amount (no muda fee deduction)

            // Check if we need to bridge for off-ramp transactions
            let needsBridge = false;
            let bridgeCost = 0;
            let bridgeOutputAmount = numericAmount; // Use full amount for bridge calculation
            let providerAssetCode = assetCode;
            let providerChain = chain;
            let rhinoQuoteId: string | null = null; // Store Rhino quote ID if bridge is needed

            if (transaction_type === "off_ramp") {
                // Skip Rhino transfer cost calculation for CNGN and CUSD (not supported by Rhino)
                if (assetCode === "CNGN" || assetCode === "CUSD") {
                    console.log(`🦏 Skipping Rhino bridge/transfer cost calculation for ${assetCode} (not supported)`);
                    // Use full amount directly - no muda fee, bridge or transfer costs for CNGN/CUSD
                    finalProviderAmount = numericAmount;
                } else {
                    // Check if we need to bridge to provider's supported chain
                    if (provider_id && originalChain) {
                        const providerSupportedAsset = this.findSupportedAssetForProvider(Number(provider_id), assetCode, originalChain);
                        const requestedAssetChain = this.formatAssetChain(assetCode, originalChain);

                        console.log(`🔍 Checking asset support: provider ${provider_id}, requested: ${requestedAssetChain}`);

                        if (providerSupportedAsset && providerSupportedAsset !== requestedAssetChain) {
                            // Need to bridge from requested chain to provider's supported chain
                            const supportedParsed = this.parseAssetCode(providerSupportedAsset.replace('@', '_'));
                            needsBridge = true;
                            providerChain = supportedParsed.chain;

                            console.log(`🌉 Bridge needed: ${requestedAssetChain} -> ${providerSupportedAsset}`);

                            // Calculate bridge cost (start with full amount, no muda fee deduction)
                            const bridgeResult = await this.calculateRhinoBridgeCost(
                                assetCode,
                                originalChain,
                                assetCode,
                                providerChain,
                                numericAmount // Use full amount instead of payableAmount
                            );

                            if (bridgeResult.success) {
                                bridgeCost = bridgeResult.totalFee;
                                bridgeOutputAmount = bridgeResult.outputAmount;
                                // Store Rhino quote ID for later use in saveQuote
                                rhinoQuoteId = bridgeResult.quote?.id || bridgeResult.quote?.quoteId || null;
                                console.log('🌉 Bridge cost calculated:', {
                                    fromChain: originalChain,
                                    toChain: providerChain,
                                    bridgeCost,
                                    bridgeOutputAmount,
                                    rhinoQuoteId
                                });

                                // Calculate Rhino transfer cost ONLY when bridge is required
                                // (Cost to move funds from smart deposit to provider address after bridging)
                                console.log('🦏 Calculating Rhino transfer costs for bridged off-ramp transaction');
                                const transferCost = await this.calculateRhinoTransferCost(assetCode, providerChain, bridgeOutputAmount);
                                if (transferCost.success) {
                                    rhinoTransferFee = transferCost.totalFee;
                                    finalProviderAmount = bridgeOutputAmount - rhinoTransferFee;
                                    console.log('🦏 Rhino transfer cost calculated:', {
                                        inputAmount: bridgeOutputAmount,
                                        rhinoTransferFee,
                                        finalProviderAmount
                                    });
                                } else {
                                    console.error('🦏 Failed to calculate Rhino transfer cost:', transferCost.error);
                                    // Continue without transfer cost for now, but log the error
                                }
                            } else {
                                console.error('🌉 Failed to calculate bridge cost:', bridgeResult.error);
                                return this.makeResponse(203, `Cannot bridge ${requestedAssetChain} to ${providerSupportedAsset}: ${bridgeResult.error}`);
                            }
                        } else {
                            // No bridge needed - direct off-ramp
                            // Use full amount directly (no muda fee deduction for direct off-ramps)
                            finalProviderAmount = numericAmount;
                            console.log('✅ Direct off-ramp (no bridge) - using full amount:', finalProviderAmount);
                        }
                    } else {
                        // No provider_id or originalChain - use full amount directly
                        finalProviderAmount = numericAmount;
                        console.log('✅ Direct off-ramp (no provider check) - using full amount:', finalProviderAmount);
                    }
                }
            }

            let healthResponse: RateResponse | any;
            let provider_quote_id: string = "";

            const supportedOnRampCurrencies = ["KES", "NGN"];
            if (transaction_type === "on_ramp" && !supportedOnRampCurrencies.includes(currency)) {
                return this.makeResponse(203, "No provider found for this currency on ramp");
            }

            // Use finalProviderAmount for off-ramp, original amount for on-ramp

            let providerQuoteAmount = transaction_type === "off_ramp" ? finalProviderAmount : numericAmount;
            providerQuoteAmount = Number(providerQuoteAmount.toFixed(2));

            console.log("supportsMudaProviderFlow", supportsMudaProviderFlow, provider_id)
            if (supportsMudaProviderFlow || provider_id == 10 || provider_id == 1) {
                const quoteRequest = {
                    amount: providerQuoteAmount,
                    currency,
                    chain: chain,
                    asset_code: assetCode,
                    provider_id: Number(provider_id),
                    transaction_type: transaction_type
                }
                healthResponse = await mudaProvider.getQuote(provider_id, quoteRequest)
                console.log(`healthResponseMuda`, healthResponse)
                provider_quote_id = healthResponse?.providerQuoteId || healthResponse?.id || "";

            } else if (provider_id == 2) {

                healthResponse = await kotaniPay.getOffRampRate(assetCode, currency, providerQuoteAmount);
                console.log(`healthResponseKotani`, healthResponse)
                provider_quote_id = healthResponse?.id || "";

            } else if (provider_id == 6) {

                console.log(`step1`, assetCode, currency, amount, transaction_type)
                const assetCode_: any = await this.getAssetCode(data.asset_code || "")
                if (!assetCode_) {
                    return this.makeResponse(203, "Unknown selected receive asset");
                }



                const assetChain_: any = await this.getAssetChain(data.asset_code || "")
                const ValidateAcceptedAssetChain_: any = await this.honeyCoin.getAcceptedOnRampChains(assetChain_)
                if (!ValidateAcceptedAssetChain_) {
                    return this.makeResponse(203, "Asset chain not supported");
                }

                const ValidateAcceptedAssets_: any = await this.honeyCoin.getAcceptedOnRampAssets(assetCode_)
                if (!ValidateAcceptedAssets_) {
                    return this.makeResponse(203, "Asset not supported");
                }


                if (transaction_type === "on_ramp") {
                    console.log(`on ramp rate healthResponseHoneyCoin`, currency, assetCode_, providerQuoteAmount)
                    healthResponse = await this.honeyCoin.getOffRampRate(currency, assetCode_, providerQuoteAmount);
                    console.log(`on ramp rate healthResponseQuidaxOnRamp`, healthResponse);
                    if (healthResponse?.expiresAt === "") {
                        healthResponse.expiresAt = new Date(Date.now() + 1000 * 60 * 30) // 10 minutes from now
                            .toISOString()
                            .slice(0, 19)
                            .replace('T', ' ');
                    }
                    healthResponse = healthResponse?.data ?? healthResponse
                    provider_quote_id = healthResponse?.id || "";

                } else {

                    healthResponse = await this.honeyCoin.getOffRampRate(assetCode_, currency, providerQuoteAmount);
                    console.log(`off ramp rate healthResponseQuidax`, healthResponse);
                }


                healthResponse = healthResponse?.data ?? healthResponse
                provider_quote_id = healthResponse?.id || "";

            } else if (provider_id == 4) {
                if (transaction_type === "on_ramp") {
                    // Use Quidax on-ramp quote
                    healthResponse = await this.getQuidaxOnRampQuote(currency, assetCode, providerQuoteAmount);
                    console.log(`healthResponseQuidaxOnRamp`, healthResponse);
                } else if (service_id == "1005") {
                    // Use new Quidax off-ramp quote (RAMP API) for service_id 1005
                    healthResponse = await this.getQuidaxOffRampQuote(assetCode, currency, providerQuoteAmount);
                    console.log(`healthResponseQuidaxOffRamp`, healthResponse);
                } else {
                    // Fallback for other Quidax off-ramp services (legacy quote API)
                    healthResponse = await this.getQuidaxOffRampQuote(assetCode, currency, providerQuoteAmount);
                    console.log(`healthResponseQuidaxLegacy`, healthResponse);
                }
                provider_quote_id = healthResponse?.providerQuoteId || healthResponse?.id || "";
            } else if (provider_id == 3) {
                healthResponse = await Ogateway.getRates({
                    amount: providerQuoteAmount,
                    symbol: "USD",
                    currency,
                });

            } else {
                return this.makeResponse(203, "No provider found for this currency");
            }
            // Check if healthResponse is an error object
            if (healthResponse?.error) {
                return this.makeResponse(203, healthResponse.message, healthResponse.data);
            }

            if (healthResponse.providerQuoteId && healthResponse.providerQuoteId != "") {
                healthResponse = healthResponse
            } else {
                return this.makeResponse(203, healthResponse.message || "Rate not found", healthResponse);
            }

            // Attach quoteId to response object
            // Calculate total fees: provider fee + Muda fee + bridge cost + Rhino transfer fee
            let totalComputedFee = (Number(healthResponse.fee) || 0) + (Number(mudaFee) || 0) + (Number(bridgeCost) || 0) + (Number(rhinoTransferFee) || 0);
            if (!isFinite(totalComputedFee)) {
                totalComputedFee = 0;
            }
            healthResponse.fee = totalComputedFee;

            // Add bridge information if needed
            if (needsBridge) {
                healthResponse.bridgeRequired = true;
                healthResponse.bridgeFromChain = originalChain;
                healthResponse.bridgeToChain = providerChain;
                healthResponse.bridgeFee = bridgeCost;
                healthResponse.rhinoQuoteId = rhinoQuoteId; // Add Rhino quote ID
            }



            const expiresAt_ = new Date(Date.now() + 1000 * 60 * 30)
                .toISOString()
                .slice(0, 19)
                .replace('T', ' ');


            //  if (quoteId !== "") {
            const saveToDb = healthResponse;
            saveToDb.quoteId = quoteId;
            saveToDb.asset_code = symbol;
            saveToDb.currency = currency;
            saveToDb.service_id = service_id;
            saveToDb.chain = chain;
            delete saveToDb.from;
            delete saveToDb.to;

            // Format numeric fields to proper decimal values
            if (saveToDb.fiatAmount) saveToDb.fiatAmount = parseFloat(saveToDb.fiatAmount).toFixed(2);
            if (saveToDb.toAmount) saveToDb.toAmount = parseFloat(saveToDb.toAmount).toFixed(2);
            if (saveToDb.cryptoAmount) saveToDb.cryptoAmount = parseFloat(saveToDb.cryptoAmount).toFixed(8);
            if (saveToDb.fee) saveToDb.fee = parseFloat(saveToDb.fee).toFixed(2);
            if (saveToDb.quotedPrice) saveToDb.quotedPrice = parseFloat(saveToDb.quotedPrice).toFixed(2);

            // Format expiresAt to MySQL datetime format (YYYY-MM-DD HH:MM:SS)
            const expiresAtValue = (saveToDb?.expiresAt !== undefined && saveToDb?.expiresAt !== "") ? saveToDb?.expiresAt : expiresAt_;
            saveToDb.expiresAt = expiresAt_


            // Map bridge fields to database column names
            if (saveToDb.bridgeRequired) {
                saveToDb.bridge_required = saveToDb.bridgeRequired ? 1 : 0;
                saveToDb.bridge_from_chain = saveToDb.bridgeFromChain;
                saveToDb.bridge_to_chain = saveToDb.bridgeToChain;
                saveToDb.bridge_fee = saveToDb.bridgeFee;
                // Store the Rhino quote ID if available (will be populated later in saveQuote)
                saveToDb.rhino_quote_id = saveToDb.rhinoQuoteId || null;
            }

            // Remove the camelCase versions after mapping
            delete saveToDb.bridgeRequired;
            delete saveToDb.bridgeFromChain;
            delete saveToDb.bridgeToChain;
            delete saveToDb.bridgeFee;
            delete saveToDb.rhinoQuoteId;

            saveToDb.expiresAt = (saveToDb?.expiresAt !== undefined && saveToDb?.expiresAt !== "") ? saveToDb?.expiresAt : expiresAt_;
            saveToDb.transactionType = transaction_type;
            await this.insertData("quote_log", saveToDb);
            // }
            delete healthResponse.providerQuoteId;
            delete healthResponse.symbol;
            healthResponse.asset_code = symbol;
            healthResponse.asset_network = chain;
            console.log(`healthResponseFinal`, healthResponse);
            return this.makeResponse(200, "success", healthResponse);

        } catch (error) {

            console.error("Error in updateService:", error);
            return this.makeResponse(203, "error");
        }
    }

    async getService(service_id: string) {
        const resp: any = await this.callQuerySafe(
            "SELECT * FROM services WHERE service_id = ? OR service_code = ?",
            [service_id, service_id]
        )
        if (resp.length > 0) {
            return this.makeResponse(200, "success", resp[0]);
        } else {
            return this.makeResponse(404, "Service not found");
        }
    }

    async services() {
        const response: any = await this.callRawQuery("SELECT * FROM services")
        return this.makeResponse(200, "success", response);
    }


    async getAssetCode(code: string) {
        const asset: any = await this.callQuerySafe("SELECT asset_name FROM accepted_assets WHERE asset_code = ?", [code])
        if (asset.length > 0) {
            return asset[0].asset_name
        }
        return false;
    }

    async getAssetChain(code: string) {
        const asset: any = await this.callQuerySafe("SELECT chain FROM accepted_assets WHERE asset_code = ?", [code])
        if (asset.length > 0) {
            return asset[0].chain
        }
        return false;
    }

    async getProviders(data: RateRequest, filter = "all") {

        console.log(`provider==>1`, data)
        const service = data.service_code || ""
        const currency = data.currency;
        // Default amount based on currency minimum requirements
        let amount = data.amount;
        if (!amount) {
            amount = currency === 'ZAR' ? 30 : 1;
        }
        const transaction_type = data.transaction_type || "off_ramp"
        // const asset = await this.getAssetCode(data.asset_code || "")
        const asset = data.asset_code || ""
        const chain = await this.getchain(asset)
        console.log(`chain==>`, chain)

        try {


            let provider: any = await this.callRawQuery(
                `SELECT * FROM service_providers s 
             INNER  JOIN providers p ON s.provider_id = p.provider_id 
             INNER  JOIN services r ON r.service_id = s.service_id 
             WHERE  p.approval_status = 'active' and r.currency='${currency}'  ${service ? `and (r.service_code='${service}' OR s.service_id='${service}')  ` : ''}`
            );
            console.log(`PROVIDERS==>1`, provider)

            const providers: ProviderService[] = [];
            for (let i = 0; i < provider.length; i++) {
                try {
                    const provider_id = provider[i].provider_id;
                    const rateReq: RateRequest = {
                        // asset_code: asset,
                        // currency: currency,
                        asset_code: asset,
                        currency: currency,
                        amount,
                        chain,
                        provider_id: provider_id,
                        service_code: service || provider[i].service_id,
                        transaction_type: transaction_type
                    };

                    let exRate = 0;
                    let fiatAmount = 0;
                    let fee = 0;


                    const rate: any = await this.getRate(rateReq);
                    console.log(`PROVIDERS==>2`, rate)
                    if (rate?.data?.fiatAmount) {
                        fiatAmount = rate?.data?.fiatAmount || "";
                        fee = rate?.data?.fee || "";
                        exRate = rate?.data?.quotedPrice || "";
                    }

                    provider[i].fiatAmount = fiatAmount;
                    provider[i].rate = exRate
                    provider[i].fee = fee;
                    provider[i].transaction_type = transaction_type;
                    provider[i].asset_network = rate?.data?.asset_network || "";
                    if (exRate > 0) {
                        providers.push(provider[i]);
                    }
                } catch (error) {
                    console.log(`PROVIDERS==>3`, error)
                    provider[i].rate = 0;
                }
            }


            console.log(`sortedProviders before `, providers)
            // 🔥 Sort providers by rate in DESC order before returning
            let sortedProviders: any = []
            sortedProviders = providers.sort((a, b) => b.rate - a.rate);
            console.log(`sortedProviders`, sortedProviders)
            if (sortedProviders.length > 0) {

                if (filter == "all") {
                    return this.makeResponse(200, "success", sortedProviders);
                } else {
                    return this.makeResponse(200, "success", sortedProviders[0]);
                }

            } else {
                return this.makeResponse(404, "No providers found");
            }



        } catch (error) {
            console.error("Error in updateService:", error);
            return this.makeResponse(500, "Error getting providers");
        }
    }


    async assets() {
        return await this.selectDataQuerySafe("assets", {})

    }
    async currencies() {
        return await this.selectDataQuerySafe("currencies", {})
    }

    // Delete a service
    async deleteAddress(service_id: string) {
        try {
            await this.deleteData('addresses', `id = '${service_id}'`);
            return this.makeResponse(200, "Service deleted successfully");
        } catch (error) {
            console.error("Error in deleteService:", error);
            return this.makeResponse(203, "Error deleting service");
        }
    }

    // Delete a service
    async deletePhoneNumber(service_id: any, userId: string) {
        try {
            console.log(`deletePhoneNumber`, service_id, userId)
            const method: any = await this.selectDataQuerySafe("payment_methods", { payment_method_id: service_id, company_id: userId });
            if (method.length == 0) {
                return this.makeResponse(203, "Item not found");
            }
            await this.deleteData('payment_methods', `payment_method_id = '${service_id}' and company_id='${userId}'`);

            return this.makeResponse(200, "Item deleted successfully");
        } catch (error) {
            console.error("Error in deleteService:", error);
            return this.makeResponse(203, "Error deleting service");
        }
    }

    // Delete a service
    async deleteService(service_id: number) {
        try {
            await this.deleteData('services', `service_id = '${service_id}'`);
            return this.makeResponse(200, "Service deleted successfully");
        } catch (error) {
            console.error("Error in deleteService:", error);
            return this.makeResponse(203, "Error deleting service");
        }
    }

    async getPaymentMethodAccountNumber(payId: string) {
        return await this.selectDataQuerySafe("payment_methods", { account_number: payId });
    }

    async getPaymentMethodAccount(payId: string, company_id: string) {
        return await this.selectDataQuerySafe("payment_methods", { company_id, phone_number: payId });
    }

    async getPaymentMethods(company_id: string) {
        return await this.selectDataQuerySafe("payment_methods", { company_id });
    }

    async getPaymentMethodForCurrency(company_id: string, currency: string) {
        return await this.selectDataQuerySafe("payment_methods", { company_id, currency });
    }



    async getAddressesForCurrency(company_id: string, currency: string, chain: string) {
        //  const chain = await this.getchain(currency)
        return await this.selectDataQuerySafe("addresses", { company_id, chain });
    }


    async addPaymentMethod(data: any) {
        try {

            const { company_id, currency, type } = data;
            if (!company_id || !type) {
                // return this.makeResponse(400, "Company ID and payment method type are required");
            }

            if (type !== "bank" && type !== "mobile_money") {
                return this.makeResponse(400, "Invalid payment method type");
            }
            let PaymentMethodId = this.getRandomString()

            let newPaymentMethod: BankPayment | MobileMoneyPayment;
            let kotani_customer_key = ""
            const network = "MTN"


            // validate bank account number by cuureny
            const accountValidation: any = await this.accountNumberValidation(type, currency, data)
            if (!accountValidation.status) {
                return this.makeResponse(400, accountValidation.message)
            }

            // validate phone number by currency and country code
            if (type === "mobile_money") {

                PaymentMethodId = "1" + PaymentMethodId
                const country_code = "UG"

                const { phone_number, account_name } = data as MobileMoneyPayment;
                // validate We and check if phone_number abd account_name is given      
                if (!phone_number || !account_name) {
                    return this.makeResponse(400, "Phone number and account name are required for mobile money");
                }

                if (phone_number.length < 11) {
                    return this.makeResponse(400, "Phone number is not valid, must start with a country code");
                }
                const paymethod = await this.getPaymentMethodAccount(phone_number, company_id)
                if (paymethod.length > 0) {
                    return this.makeResponse(400, "Phone number exists");
                }


                const addResponse = await kotaniPay.createCustomerMobileMoney(phone_number, country_code, network, account_name);
                console.log(`addResponse`, addResponse)
                const status = addResponse?.success
                if (status) {
                    kotani_customer_key = addResponse?.data?.customer_key
                }

                if (!phone_number || !country_code) {
                    return this.makeResponse(400, "Phone number and country code are required for mobile money");
                }
                newPaymentMethod = { kotani_customer_key, currency, company_id, type, phone_number, country_code, network, account_name };
            } else {
                PaymentMethodId = "2" + PaymentMethodId
                const { bank_name, bank_code, account_number, bank_address, phone_number, bank_country, account_name, beneficiary_address = "", beneficiary_phone_number = "", sort_code = "", swift_code = "" } = data as BankPayment;
                let providerId = "";

                let providerCustomerKey = "";
                let bankBranchCode = "";
                if (currency === "KES") {
                    providerId = "6";
                    providerCustomerKey = "";
                    bankBranchCode = "";
                }

                // validate account number deplicates
                const accountNumberExists: any = await this.callQuerySafe(`select COUNT(*) as total from payment_methods where account_number=? and company_id=? and bank_code=?`, [account_number, company_id, bank_code])
                if (accountNumberExists[0].total > 0) {
                    return this.makeResponse(400, "Account number already exists");
                }

                if (!bank_name || !account_number || !bank_code) {
                    return this.makeResponse(400, "Bank name, account number, and bank code are required for bank payment method");
                }

                newPaymentMethod = { phone_number: phone_number, kotani_customer_key, currency, company_id, type, bank_name: bank_name, bank_code: bank_code, account_number: account_number, bank_address: bank_address, bank_phone_number: phone_number, bank_country: bank_country, account_name: account_name, provider_id: providerId, provider_customer_key: providerCustomerKey, bank_branch_code: bankBranchCode, sort_code: sort_code, swift_code: swift_code };
            }


            let addPaymentMethod: any = newPaymentMethod
            addPaymentMethod.payment_method_id = PaymentMethodId
            const insertedPaymentMethod = await this.insertData("payment_methods", addPaymentMethod);
            return this.makeResponse(200, "Payment method added successfully", addPaymentMethod);

        } catch (error) {
            console.error("Error in addPaymentMethod:", error);
            return this.makeResponse(500, "Error adding payment method");
        }
    }



    // edit payment method 
    async updatePaymentMethod(id: any, data: any) {
        try {

            const { company_id, currency } = data;
            let { type } = data;

            if (!company_id || !type) {
                //   return this.makeResponse(400, "Company ID and payment method type are required");
            }

            const checkPaymentMethodExists: any = await this.callQuerySafe(`select id, type from payment_methods where payment_method_id = ?`, [id])
            if (checkPaymentMethodExists.length === 0) {
                return this.makeResponse(400, "Payment method not found");
            }
            const selectePaymentMethod: any = checkPaymentMethodExists[0]
            type = selectePaymentMethod.type

            if (type !== "bank" && type !== "mobile_money") {
                return this.makeResponse(400, "Invalid payment method type");
            }

            let PaymentMethodId = this.getRandomString()
            let newPaymentMethod: BankPayment | MobileMoneyPayment;
            let kotani_customer_key = ""
            const network = "MTN"


            if (type === "mobile_money") {
                PaymentMethodId = "1" + PaymentMethodId
                const country_code = "UG"

                const { phone_number, account_name } = data as MobileMoneyPayment;

                // validate We and check if phone_number abd account_name is given      
                if (!phone_number || !account_name) {
                    return this.makeResponse(400, "Phone number and account name are required for mobile money");
                }

                if (phone_number.length < 11) {
                    return this.makeResponse(400, "Phone number is not valid, must start with a country code");
                }
                const paymethod = await this.getPaymentMethodAccount(phone_number, company_id)
                // filter paymethod if ut has id
                const filteredExistingPaymethod = paymethod.filter((item: any) => item.id !== selectePaymentMethod.id)
                if (paymethod.length > 0 && filteredExistingPaymethod.length > 0) {
                    return this.makeResponse(400, "Phone number exists");
                }

                const addResponse = await kotaniPay.createCustomerMobileMoney(phone_number, country_code, network, account_name);
                console.log(`addResponse`, addResponse)
                const status = addResponse?.success
                if (status) {
                    kotani_customer_key = addResponse?.data?.customer_key
                }

                if (!phone_number || !country_code) {
                    return this.makeResponse(400, "Phone number and country code are required for mobile money");
                }

                newPaymentMethod = { kotani_customer_key, currency, company_id, type, phone_number, country_code, network, account_name };
            } else {

                PaymentMethodId = "2" + PaymentMethodId
                const { bank_name, bank_code, account_number, bank_address, bank_phone_number, bank_country, account_name, phone_number, beneficiary_address, sort_code = "", swift_code = "" } = data as BankPayment;
                let providerId = "";

                let providerCustomerKey = "";
                let bankBranchCode = "";
                if (currency === "KES") {
                    providerId = "6";
                    providerCustomerKey = "";
                    bankBranchCode = "";
                }
                // validate account number deplicates
                const accountNumberExists: any = await this.callQuerySafe(`select COUNT(*) as total from payment_methods where account_number=? and company_id=? and bank_code=? AND id != ?`, [account_number, company_id, bank_code, selectePaymentMethod.id])
                if (accountNumberExists[0].total > 0) {
                    return this.makeResponse(400, "Account number already exists");
                }

                if (!bank_name || !account_number || !bank_code) {
                    return this.makeResponse(400, "Bank name, account number, and bank code are required for bank payment method");
                }

                //   const addResponse = await kotaniPay.createCustomerBank(data);
                newPaymentMethod = { phone_number: phone_number, kotani_customer_key, currency, company_id, type, bank_name: bank_name, bank_code: bank_code, account_number: account_number, bank_address: bank_address, bank_phone_number: phone_number, bank_country: bank_country, account_name: account_name, provider_id: providerId, provider_customer_key: providerCustomerKey, bank_branch_code: bankBranchCode, sort_code: sort_code, swift_code: swift_code };
            }


            let addPaymentMethod: any = newPaymentMethod
            await this.updateData("payment_methods", `id = '${selectePaymentMethod.id}'`, addPaymentMethod);
            return this.makeResponse(200, "Payment method updated successfully", addPaymentMethod);

        } catch (error) {
            console.error("Error in addPaymentMethod:", error);
            return this.makeResponse(500, "Error editing payment method");
        }
    }


    async getChains() {
        const data: any = await this.callRawQuery("SELECT * FROM chains")
        return this.makeResponse(203, "sucess", data);
    }

    // get payment methods by id
    async getPaymentMethodById(payment_method_id: string) {
        return await this.selectDataQuerySafe("payment_methods", { payment_method_id });
    }

    // Create a new address for a company
    async addAddress(data: any) {
        try {

            console.log(`data`, data)
            const { company_id, wallet_name, address, chain } = data;


            const addExists: any = await this.callQuerySafe(
                "SELECT * FROM addresses WHERE address = ? AND company_id = ? AND chain = ?",
                [address, company_id, chain]
            )
            if (addExists.length > 0) {
                return this.makeResponse(203, "Address exists already");

            }


            //  console.log(``)
            // add a check for a valid address
            if (chain === "BANTU" && (!address.startsWith('G') || address.length !== 56)) {
                return this.makeResponse(203, "Invalid BANTU address");
            }

            if (chain === "STELLAR" && (!address.startsWith('G') || address.length !== 56)) {
                return this.makeResponse(203, "Invalid XLM address");
            }

            if (chain === "TRON" && (!address.startsWith('T') || address.length !== 34)) {
                return this.makeResponse(203, "Invalid TRON address");
            }
            // Add explicit BASE chain validation
            if (chain === "BASE" && (!address.startsWith('0x') || address.length !== 42)) {
                return this.makeResponse(203, "Invalid BASE address");
            }
            else if (!address.startsWith('0x') || address.length !== 42) {
                return this.makeResponse(203, "Invalid address");
            }
            const newAddress = { company_id, wallet_name, address, chain, created_at: new Date() };
            const insertedAddress = await this.insertData('addresses', newAddress);
            return this.makeResponse(200, "Address added successfully", insertedAddress);
        } catch (error) {
            console.error("Error in addAddress:", error);
            return this.makeResponse(203, "Address already exists");
        }
    }



    async getQuotestatus() {
        const quote: any = await this.callRawQuery(`
            SELECT * 
            FROM quotes 
            WHERE LOWER(status) NOT IN ('successful', 'failed', 'expired', 'cancelled')
          `);
        console.log(`OBTAINED_QUOTED`, quote.length)
        for (let i = 0; i < quote.length; i++) {
            try {
                const created_on = quote[i].created_on
                const pay_in_status = quote[i].pay_in_status
                const provider_id = quote[i].provider_id
                const transId = quote[i].transId

                if (provider_id == 2) { //for kotani
                    const healthResponse = await kotaniPay.getOfframpStatus(transId);
                    console.log(`getQuotestatus`, healthResponse)
                    const status = healthResponse.success
                    if (status) {
                        const fiatStatus = healthResponse.data.status
                        const onchainStatus = healthResponse.data.onchainStatus
                        if (onchainStatus != "PENDING") {
                            const updates: any = {
                                pay_in_status: onchainStatus,
                                status: fiatStatus
                            };
                            await this.updateData('quotes', `transId = '${transId}'`, updates);
                            await WebhookSender.send(transId);
                        }
                    } else {
                        // await this.checkExpired(transId, created_on)
                    }

                } else if (provider_id == 6) { // got honey coin

                    // await this.checkExpired(transId, created_on)
                    // const transaction = await this.honeyCoin.getATransaction(transId)
                    // console.log(`transaction`, transaction)
                    // const status = transaction.success
                    // if (status) {
                    //     const fiatStatus = transaction.data.status
                    //     const onchainStatus = transaction.data.chargeStatus
                    //     const updates: any = {
                    //         pay_in_status: onchainStatus,
                    //         status: fiatStatus
                    //     };
                    //     await this.updateData('quotes', `transId = '${transId}'`, updates);
                    // } else {
                    //     // await this.checkExpired(transId, created_on)
                    // }

                } else {
                    // await this.checkExpired(transId, created_on)

                }
            } catch (error) {
                console.log(`Kotanierror`, error)

            }
        }

    }

    async expirePendingQuotes() {
        try {
            const pendingQuotes: any = await this.callRawQuery(`
                SELECT * FROM quotes 
                WHERE status = 'PENDING' 
            `);

            const currentDate = new Date();

            for (let quote of pendingQuotes) {
                const createdOnDate = new Date(quote.created_on);
                const expiresAt = new Date(quote.expires_at);
                console.log(`createdOnDate`, createdOnDate, expiresAt)

                const timeSinceCreationInMinutes = (currentDate.getTime() - createdOnDate.getTime()) / (1000 * 60);
                console.log(`timeSinceCreationInMinutes`, timeSinceCreationInMinutes)
                if (currentDate > expiresAt && timeSinceCreationInMinutes >= 120) {
                    const updates = {
                        status: 'EXPIRED',
                        narration: 'EXPIRED'
                    };
                    await this.updateData('quotes', `transId = '${quote.transId}'`, updates);
                    await WebhookSender.send(quote.transId);
                }
            }

            return this.makeResponse(200, "Pending quotes expired successfully");
        } catch (error) {
            console.error("Error in expirePendingQuotes:", error);
            return this.makeResponse(203, "Error expiring pending quotes");
        }
    }



    // Fetch addresses for a specific company
    async getAddresses(company_id: string) {
        try {
            const addresses: any = await this.selectDataQuerySafe("addresses", { company_id });
            if (addresses.length > 0) {
                return this.makeResponse(200, "Addresses fetched successfully", addresses);
            } else {
                return this.makeResponse(404, "Addresses not found for the given company");
            }
        } catch (error) {
            console.error("Error in getAddresses:", error);
            return this.makeResponse(203, "Error fetching addresses");
        }
    }

    async getAllQuotes(wallet: any, data: any): Promise<any> {
        // Remove all keys with empty values from data
        const cleanData = Object.entries(data || {}).reduce((acc, [key, value]) => {
            if (value !== null && value !== undefined && value !== '') {
                if (Array.isArray(value)) {
                    if (value.length > 0 && value.some(v => v !== null && v !== undefined && v !== '')) {
                        acc[key] = value.filter(v => v !== null && v !== undefined && v !== '');
                    }
                } else {
                    acc[key] = value;
                }
            }
            return acc;
        }, {} as any);

        const PAGE: number = Number(cleanData?.page) || 1;
        const LIMIT = cleanData?.limit || 14;
        const OFFSET = (PAGE - 1) * LIMIT;
        const SEARCH = cleanData?.search;
        const CURRENCY = cleanData?.currency;
        const PAY_IN_STATUS = cleanData?.pay_in_status ? (Array.isArray(cleanData.pay_in_status) ? cleanData.pay_in_status : [cleanData.pay_in_status]) : ['SUCCESSFUL', 'PENDING'];
        const STATUS = cleanData?.status || [];
        const PROVIDER_ID = cleanData?.provider_id || [];
        const SEND_ASSET = cleanData?.send_asset || [];
        const RECEIVE_CURRENCY = cleanData?.receive_currency || [];
        const START_DATE = cleanData?.start_date || '';
        const END_DATE = cleanData?.end_date || '';
        const SORT_KEY = cleanData?.sort_key || '';
        const SORT_ORDER = cleanData?.sort_order || 'DESC';

        let whereClause = '';
        const conditions = [];

        conditions.push(`(
            p.provider_address = '${wallet}' OR 
            p.sending_address = '${wallet}' OR
            p.receiver_address = '${wallet}'
        )`);

        if (SEARCH) {
            conditions.push(`(
            p.transId LIKE '%${SEARCH}%' OR 
            p.provider_address LIKE '%${SEARCH}%' OR
            p.account_number LIKE '%${SEARCH}%' OR
            p.hash LIKE '%${SEARCH}%' OR
            p.company_id LIKE '%${SEARCH}%' OR
            p.sending_address LIKE '%${SEARCH}%' OR 
            p.receiver_address LIKE '%${SEARCH}%'
        )`);
        }

        // Always include pay_in_status filter for SUCCESSFUL or PENDING by default
        if (Array.isArray(PAY_IN_STATUS) && PAY_IN_STATUS.length > 0) {
            const statusConditions = PAY_IN_STATUS.map(status => `p.pay_in_status = '${status}'`).join(' OR ');
            conditions.push(`(${statusConditions})`);
        } else if (PAY_IN_STATUS) {
            conditions.push(`p.pay_in_status = '${PAY_IN_STATUS}'`);
        } else {
            // Default to SUCCESSFUL and PENDING if no pay_in_status provided
            conditions.push(`(p.pay_in_status = 'SUCCESSFUL' OR p.pay_in_status = 'PENDING')`);
        }

        if (START_DATE && START_DATE !== '' && START_DATE !== null && START_DATE !== undefined) {
            conditions.push(`DATE(p.created_on) >= '${START_DATE}'`);
        }

        if (END_DATE && END_DATE !== '' && END_DATE !== null) {
            conditions.push(`DATE(p.created_on) <= '${END_DATE}'`);
        }

        if (Array.isArray(SEND_ASSET) && SEND_ASSET.length > 0) {
            const sendAssetConditions = SEND_ASSET.map(asset => `p.send_asset = '${asset}'`).join(' OR ');
            conditions.push(`(${sendAssetConditions})`);

        } else if (SEND_ASSET && SEND_ASSET !== '' && SEND_ASSET.length > 0) {
            conditions.push(`p.send_asset = '${SEND_ASSET}'`);
        }

        if (Array.isArray(RECEIVE_CURRENCY) && RECEIVE_CURRENCY.length > 0) {
            const receiveCurrencyConditions = RECEIVE_CURRENCY.map(currency => `p.receive_currency = '${currency}'`).join(' OR ');
            conditions.push(`(${receiveCurrencyConditions})`);

        } else if (RECEIVE_CURRENCY && RECEIVE_CURRENCY !== '' && RECEIVE_CURRENCY.length > 0) {
            conditions.push(`p.receive_currency = '${RECEIVE_CURRENCY}'`);
        }

        if (Array.isArray(STATUS) && STATUS.length > 0) {
            const statusConditions = STATUS.map(status => `p.status = '${status}'`).join(' OR ');
            conditions.push(`(${statusConditions})`);

        } else if (STATUS && STATUS !== '' && STATUS.length > 0) {
            conditions.push(`p.status = '${STATUS}'`);
        }

        if (Array.isArray(PROVIDER_ID) && PROVIDER_ID.length > 0) {
            const providerConditions = PROVIDER_ID.map(id => `p.provider_id = '${id}'`).join(' OR ');
            conditions.push(`(${providerConditions})`);
        } else if (PROVIDER_ID && PROVIDER_ID !== '' && PROVIDER_ID.length > 0) {

            conditions.push(`p.provider_id = '${PROVIDER_ID}'`);
        }

        if (conditions.length > 0) {
            whereClause = `WHERE ${conditions.join(' AND ')}`;
        }

        let orderClause = '';
        if (SORT_KEY) {
            const validSortKeys = ['created_at', 'transId', 'send_asset', 'receive_currency', 'pay_in_status', 'status'];
            const validSortOrders = ['ASC', 'DESC'];

            if (validSortKeys.includes(SORT_KEY)) {
                const order = validSortOrders.includes(SORT_ORDER.toUpperCase()) ? SORT_ORDER.toUpperCase() : 'DESC';
                orderClause = `ORDER BY p.${SORT_KEY} ${order}`;
            }
        } else {
            orderClause = 'ORDER BY p.created_ON DESC';
        }

        const query = `
        SELECT
            p.*,
            p.id AS quote_id,
            an.*,
            an.id AS payment_mtd_id,
            sp.*, 
            serv.*
        FROM quotes p 
        LEFT JOIN payment_methods an ON p.payment_method_id = an.payment_method_id
        LEFT JOIN service_providers sp ON p.provider_id = sp.provider_service_id
        LEFT JOIN services serv ON p.service_id = serv.service_id
        ${whereClause}
        ${orderClause}
        LIMIT ${LIMIT} OFFSET ${OFFSET}
        `;
        const countQuery = `
        SELECT COUNT(*) as total 
        FROM quotes p 
        LEFT JOIN payment_methods an ON p.payment_method_id = an.payment_method_id
        LEFT JOIN service_providers sp ON p.provider_id = sp.provider_service_id
        LEFT JOIN services serv ON p.service_id = serv.service_id
        ${whereClause}
        `;

        const [items, countResult] = await Promise.all([
            this.callRawQuery(query),
            this.callRawQuery(countQuery)
        ]);

        const total = countResult[0]?.total || 0;
        const totalPages = Math.ceil(total / LIMIT);

        const groupedItems = items.map((item: any) => {
            const {
                quote_id, payment_method_id, kotani_customer_key, type, currency, phone_number,
                country_code, network, account_name, bank_name, bank_code,
                account_number, bank_address, bank_phone_number, bank_country,
                sort_code, swift_code, created_at: payment_created_at,
                updated_at: payment_updated_at,
                provider_service_id, service_id, provider_id, min_amount, max_amount,
                service_code, service_name, country, provider_type,
                ...quoteData
            } = item;

            return {
                ...quoteData,
                id: quote_id,
                payment_method: {
                    id: payment_method_id,
                    kotani_customer_key,
                    type,
                    currency,
                    phone_number,
                    country_code,
                    network,
                    account_name,
                    bank_name,
                    bank_code,
                    account_number,
                    bank_address,
                    bank_phone_number,
                    bank_country,
                    sort_code,
                    swift_code,
                    created_at: payment_created_at,
                    updated_at: payment_updated_at
                },
                service_provider: {
                    provider_service_id,
                    service_id,
                    provider_id,
                    min_amount,
                    max_amount,
                    service: {
                        service_code,
                        service_name,
                        country,
                        provider_type
                    }
                }
            };
        });

        const reports = {
            items: groupedItems,
            pagination: {
                current_page: PAGE,
                next_page: PAGE < totalPages ? PAGE + 1 : null,
                previous_page: PAGE > 1 ? PAGE - 1 : null,
                total_pages: totalPages,
                total_items: total,
                items_per_page: LIMIT
            }
        };

        return this.makeResponse(200, "Transactions reports fetched successfully", reports);
    }


    /**
     * Parse asset code like "USDC_BASE" into {asset: "USDC", chain: "BASE"}
     */
    private parseAssetCode(assetCode: string): { asset: string; chain: string } {
        const parts = assetCode.split('_');
        if (parts.length === 2) {
            return { asset: parts[0], chain: parts[1] };
        }
        // Fallback: try to get from database
        return { asset: assetCode, chain: '' };
    }

    /**
     * Format asset and chain into "ASSET@CHAIN" format
     */
    private formatAssetChain(asset: string, chain: string): string {
        return `${asset}@${chain}`;
    }

    /**
     * Check if provider supports a specific asset-chain combination
     */
    private providerSupportsAsset(providerId: number, assetChain: string): boolean {
        const supported = this.PROVIDER_ASSET_SUPPORT[providerId] || [];
        return supported.includes(assetChain);
    }

    /**
     * Find a supported asset-chain for a provider that matches the asset
     * Now checks if the exact asset@chain combo is supported first before returning alternative chain
     */
    private findSupportedAssetForProvider(providerId: number, asset: string, preferredChain?: string): string | null {
        const supported = this.PROVIDER_ASSET_SUPPORT[providerId] || [];
        
        // If preferred chain is provided, check if exact match exists
        if (preferredChain) {
            const exactMatch = `${asset}@${preferredChain}`;
            if (supported.includes(exactMatch)) {
                console.log(`✅ Exact match found for provider ${providerId}: ${exactMatch}`);
                return exactMatch;
            }
        }
        
        // Otherwise, find first supported asset that matches the token
        const match = supported.find(sc => sc.startsWith(`${asset}@`));
        console.log(`🔍 Alternative match found for provider ${providerId}: ${match}`);
        return match || null;
    }

    /**
     * Calculate Rhino bridge quote for converting one asset-chain to another
     */
    private async calculateRhinoBridgeCost(
        fromAsset: string,
        fromChain: string,
        toAsset: string,
        toChain: string,
        amount: number
    ): Promise<any> {
        try {
            console.log('🌉 Calculating Rhino bridge cost', { fromAsset, fromChain, toAsset, toChain, amount });

            const Rhino = require('../helpers/Rhino').default;

            // Convert chain names to Rhino-compatible format
            const rhinoFromChain = this.convertToRhinoChainName(fromChain);
            const rhinoToChain = this.convertToRhinoChainName(toChain);

            const dummyAddresses: { [key: string]: string } = {
                'BASE': '******************************************',
                'BINANCE': '******************************************',
                'BSC': '******************************************',
                'BNB': '******************************************'
            };

            const depositorAddress = dummyAddresses[rhinoFromChain] || dummyAddresses[fromChain] || dummyAddresses['BASE'];
            const recipientAddress = dummyAddresses[rhinoToChain] || dummyAddresses[toChain] || dummyAddresses['BASE'];

            const quoteRequest = {
                fromChain: rhinoFromChain,  // Use Rhino-compatible chain name
                toChain: rhinoToChain,      // Use Rhino-compatible chain name
                fromToken: fromAsset,
                toToken: toAsset,
                amount: amount.toString(),
                depositor: depositorAddress,
                recipient: recipientAddress
            };

            console.log('🌉 Rhino bridge quote request:', quoteRequest);

            const response = await Rhino.getQuote(quoteRequest);

            if (!response.success) {
                console.error('🌉 Rhino bridge quote failed:', response);
                return {
                    success: false,
                    error: response.error || 'Failed to get bridge quote',
                    totalFee: 0
                };
            }

            const quote = response.data || response;
            console.log('🌉 DEBUG quote structure:', {
                hasData: !!quote,
                keys: Object.keys(quote || {}),
                fees: quote.fees,
                receiveAmount: quote.receiveAmount
            });

            // Handle both array format (formatted response) and object format (raw response)
            let totalFee = 0;
            if (Array.isArray(quote.fees)) {
                // Formatted response: fees is an array
                totalFee = quote.fees.reduce((sum: number, fee: any) => {
                    const amount = parseFloat(fee.amount || '0') || 0;
                    return sum + amount;
                }, 0);
            } else if (quote.fees && typeof quote.fees === 'object') {
                // Raw response: fees is an object with .fee property
                totalFee = parseFloat(quote.fees.fee || '0') || 0;
            }

            const outputAmount = parseFloat(quote.receiveAmount || quote.outputAmount || amount) || 0;

            console.log('🌉 Rhino bridge quote successful:', {
                totalFee,
                outputAmount,
                gasFee: Array.isArray(quote.fees) ? 'array format' : quote.fees?.gasFee,
                platformFee: Array.isArray(quote.fees) ? 'array format' : quote.fees?.platformFee
            });

            return {
                success: true,
                totalFee,
                outputAmount,
                quote
            };

        } catch (error: any) {
            console.error('🌉 Error calculating Rhino bridge cost:', error);
            return {
                success: false,
                error: error.message,
                totalFee: 0
            };
        }
    }

    /**
     * Calculate Rhino transfer cost for moving funds from smart deposit to provider address
     */
    private async calculateRhinoTransferCost(assetCode: string, chain: string, amount: number): Promise<any> {
        try {
            console.log('🦏 Calculating Rhino transfer cost', { assetCode, chain, amount });

            // Import Rhino helper
            const Rhino = require('../helpers/Rhino').default;

            // Convert chain name to Rhino-compatible format
            const rhinoChain = this.convertToRhinoChainName(chain);

            // Use dummy addresses for rate calculation (valid BASE/BSC addresses)
            const dummyAddresses: { [key: string]: string } = {
                'BASE': '******************************************',
                'BINANCE': '******************************************',
                'BSC': '******************************************',
                'BNB': '******************************************'
            };

            const depositorAddress = dummyAddresses[rhinoChain] || dummyAddresses[chain] || dummyAddresses['BASE'];
            const recipientAddress = dummyAddresses[rhinoChain] || dummyAddresses[chain] || dummyAddresses['BASE'];

            // Get quote from Rhino using public method
            const quoteRequest = {
                fromChain: rhinoChain,  // Use Rhino-compatible chain name
                toChain: rhinoChain,    // Use Rhino-compatible chain name
                token: assetCode,
                amount: amount.toString(),
                depositor: depositorAddress,
                recipient: recipientAddress
            };

            console.log('🦏 Rhino transfer quote request:', quoteRequest);

            const response = await Rhino.getBridgeQuoteForTransfer(quoteRequest);

            if (!response.success) {
                console.error('🦏 Rhino transfer quote failed:', response);
                return {
                    success: false,
                    error: response.error || 'Failed to get transfer quote',
                    totalFee: 0
                };
            }

            const quote = response.data || response;

            // Handle both array format (formatted response) and object format (raw response)
            let totalFee = 0;
            if (Array.isArray(quote.fees)) {
                // Formatted response: fees is an array
                totalFee = quote.fees.reduce((sum: number, fee: any) => {
                    const amount = parseFloat(fee.amount || '0') || 0;
                    return sum + amount;
                }, 0);
            } else if (quote.fees && typeof quote.fees === 'object') {
                // Raw response: fees is an object with .fee property
                totalFee = parseFloat(quote.fees.fee || '0') || 0;
            }

            console.log('🦏 Rhino transfer quote successful:', {
                totalFee,
                gasFee: Array.isArray(quote.fees) ? 'array format' : quote.fees?.gasFee,
                platformFee: Array.isArray(quote.fees) ? 'array format' : quote.fees?.platformFee,
                percentageFee: Array.isArray(quote.fees) ? 'array format' : quote.fees?.percentageFee
            });

            return {
                success: true,
                totalFee,
                quote
            };

        } catch (error: any) {
            console.error('🦏 Error calculating Rhino transfer cost:', error);
            return {
                success: false,
                error: error.message,
                totalFee: 0
            };
        }
    }

    /**
     * Calculate the internal MUDA fee for a quote.
     */
    private async calculateMudaFee(chain: string, amount: any): Promise<any> {
        try {
            const send_amount = parseFloat(amount)
            const fees: any = await this.callQuerySafe(
                "SELECT * FROM lr_fees INNER JOIN chains c ON lr_fees.chain_id = c.chain_id WHERE c.chain_code = ?",
                [chain]
            );

            if (fees.length == 0) {
                return { blockchainFee: 0, mudaFee: 0, totalFees: 0, payableAmount: amount }
            }
            console.log(`fees::1`, fees)
            const fee_type = fees[0].charge_type
            const block_fee_usdt = fees[0].gas_fee
            let mudaFee: any = 0
            if (fee_type == "percentage") {
                const fee_value = fees[0].muda_charge
                mudaFee = (send_amount * fee_value) / 100
            } else {
                mudaFee = fees[0].muda_charge
            }
            const miniFee = 0
            if (mudaFee < miniFee) {
                mudaFee = miniFee
            }
            const blockchainFee = parseFloat(block_fee_usdt)
            const totalFees = parseFloat(mudaFee) + blockchainFee
            const payableAmount = send_amount - totalFees
            console.log(`send_amount`, send_amount)
            console.log(`totalFees`, totalFees)
            console.log(`payableAmount`, payableAmount)
            console.log(`blockchainFees::3`, blockchainFee, mudaFee, totalFees, payableAmount)

            return { blockchainFee, mudaFee, totalFees, payableAmount }
        } catch (err) {
            console.log(`err::1`, err)
            return { blockchainFee: 0, mudaFee: 0, totalFees: 0, payableAmount: amount }
        }
    }

    /**
     * Persist a fee log for later reconciliation / analytics.
     * Columns expected (based on previous schema):
     *   quote_id | provider_service_id | muda_fee | thirdparty_fee | thirdparty_quote | thirdparty_rate |
     *   blockchain_fee | blockchain_fee_asset | rate
     */
    private async logMudaFee(params: {
        quote_id: string;
        provider_service_id: number | string;
        muda_fee: number;
        thirdparty_fee?: number;
        thirdparty_quote?: string;
        thirdparty_rate?: number | string;
        blockchain_fee?: number;
        blockchain_fee_asset?: string;
        rate?: number | string;
    }): Promise<void> {
        try {
            // Table assumed to be `muda_fee_log`. Change if your actual table differs.
            await this.insertData('muda_fee_log', params);
        } catch (err) {
            console.error('Failed to write fee log:', err);
        }
    }


    // webhook for honeycoin
    async webhookHoneyCoin(signature: string, data: any) {
        const webhookSecret = process.env.HONEYCOIN_WEBHOOK_SECRET;

        console.log(`signature`, signature)

        this.LogOperation(this.getRandomString(), 'HONEY_COIN', 'HONEY_COIN', data)
        console.log(`HONEY_COIN_REQUEST`, data)

        const webhookPayload: any = data
        const event = webhookPayload.event
        if (event !== 'transaction_updated') {
            return this.makeResponse(400, "Invalid event");
        }

        try {
            if (signature !== webhookSecret) {
                //   return this.makeResponse(400, "Invalid signature");
            }

            const thirdPartReference: any = webhookPayload.data.externalReference
            const status = webhookPayload.data.status


            const quote: any = await this.callQuerySafe(
                `SELECT * FROM quotes WHERE transId = ? AND LOWER(status) NOT IN ('successful', 'failed', 'expired', 'cancelled')`,
                [thirdPartReference]
            );

            if (quote.length == 0) {
                return this.makeResponse(400, "Transaction not found");
            }

            const hash = webhookPayload.data.txId
            if (hash && hash !== '') {
                await this.updateData('quotes', `transId = '${thirdPartReference}'`, { pay_in_status: 'SUCCESSFUL' });
            } else {
                await this.updateData('quotes', `transId = '${thirdPartReference}'`, { pay_in_status: 'FAILED' });
            }

            const transactionInfo = quote[0]
            //    const responses = ['successful', 'failed', 'pending']

            if (status.toLocaleLowerCase() === 'successful') {
                await this.updateData('quotes', `transId = '${thirdPartReference}'`, { status: 'SUCCESSFUL' });
            } else if (status.toLocaleLowerCase() === 'pending') {
                //  await this.updateData('quotes', `transId = '${thirdPartReference}'`, { pay_in_status: 'FAILED' });
            } else if (status.toLocaleLowerCase() === 'failed') {
                const narration = webhookPayload.data.note || ""

                await this.updateData('quotes', `transId = '${thirdPartReference}'`, { status: 'FAILED' });
            }
            await WebhookSender.send(transactionInfo.transId);

            console.log("Provider HoneyCoin webhook processed successfully");
            return this.makeResponse(200, "Webhook processed successfully");

        } catch (err) {

            console.log(`Failed to process provider HoneyCoin webhook`, err)
            return this.makeResponse(400, "Failed to process provider webhook");
        }
    }

}

export default Accounts;
