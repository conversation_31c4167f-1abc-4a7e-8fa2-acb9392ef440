# Simple Message Bus Utility for Liquidity Rail Admin

A simple RabbitMQ message queuing utility for the Liquidity Rail Admin service, designed to handle basic transaction events.

## Features

- **Simple Transaction Events**: Just transactionId, status, and amount
- **RabbitMQ Integration**: Basic message queuing
- **TypeScript Support**: Full type safety
- **Easy to Use**: Minimal configuration and setup

## Installation

The message bus utility requires the `amqplib` package. Add it to your project:

```bash
npm install amqplib
npm install @types/amqplib --save-dev
```

## Quick Start

### Basic Usage

```typescript
import MessageBus from '../utils/messageBus';
import { RABBITMQ_CONFIG } from '../config/messageBus.config';

// Initialize message bus
const messageBus = new MessageBus(RABBITMQ_CONFIG);

// Connect to RabbitMQ
await messageBus.connect();

// Publish a transaction event
const event = createTransactionEvent('tx123', 'pending', 100.0);
await messageBus.publishTransaction(event);

// Close connection
await messageBus.close();
```

### Environment Configuration

Set these environment variables in your `.env` file:

```env
# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest
```

## Transaction Events

### Event Structure

```typescript
interface TransactionEvent {
  transactionId: string;
  status: 'pending' | 'completed' | 'failed';
  amount: number;
  timestamp: string;
}
```

### Creating Events

```typescript
import { createTransactionEvent } from '../utils/messageBus';

// Create different types of events
const pendingEvent = createTransactionEvent('tx123', 'pending', 100.0);
const completedEvent = createTransactionEvent('tx123', 'completed', 100.0);
const failedEvent = createTransactionEvent('tx124', 'failed', 50.0);
```

## Message Consumption

### Setting Up Consumers

```typescript
// Consume messages from queue
await messageBus.consumeMessages((msg) => {
  try {
    const eventData = JSON.parse(msg.content.toString());
    console.log('Received transaction:', eventData.transactionId);
    
    // Process the event
    processTransaction(eventData);
    
    // Acknowledge the message
    messageBus.ackMessage(msg);
    
  } catch (error) {
    console.error('Error processing message:', error);
    // Reject the message and don't requeue
    messageBus.nackMessage(msg, false);
  }
});
```

### Message Acknowledgment

- **Acknowledge (ACK)**: Message processed successfully
- **Negative Acknowledge (NACK)**: Message processing failed
  - `requeue: true` - Put message back in queue for retry
  - `requeue: false` - Discard message permanently

## Event Handling

### Setting Up Event Listeners

```typescript
messageBus.on('connected', () => {
  console.log('Connected to RabbitMQ');
});

messageBus.on('disconnected', () => {
  console.log('Disconnected from RabbitMQ');
});

messageBus.on('event_published', (event: TransactionEvent) => {
  console.log('Event published:', event.transactionId);
});
```

## Testing

### Running Examples

```typescript
import { runAllExamples } from '../examples/messageBusExample';

// Run all examples
await runAllExamples();

// Or run specific examples
await basicMessageBusExample();
await publishTransactionEventsExample();
await consumeMessagesExample();
```

## API Reference

### MessageBus Class

- `connect()`: Connect to RabbitMQ
- `close()`: Close connection
- `publishTransaction(event)`: Publish transaction event
- `consumeMessages(callback)`: Start consuming messages
- `ackMessage(msg)`: Acknowledge message
- `nackMessage(msg, requeue)`: Reject message
- `isHealthy()`: Check connection health

### Event Interface

- `TransactionEvent`: Basic transaction event with transactionId, status, amount, and timestamp

### Utility Functions

- `createTransactionEvent(transactionId, status, amount)`: Create transaction event

## Best Practices

1. **Always acknowledge messages** (ACK/NACK)
2. **Handle errors gracefully**
3. **Close connections when done**
4. **Use environment variables for configuration**

## Troubleshooting

### Common Issues

1. **Connection Refused**: Check RabbitMQ service status
2. **Authentication Failed**: Verify username/password
3. **Messages Not Consumed**: Check consumer setup

## License

This utility is part of the Liquidity Rail Admin service and follows the same licensing terms.
