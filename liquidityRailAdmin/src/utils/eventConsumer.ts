/**
 * Simple LR Event Consumer
 * Consumes PAYOUT_MADE events from trading service
 */

import { LRMessageBus, TradingEvent } from './messageBus';

class LREventConsumer {
  private messageBus = new LRMessageBus();

  async connect() {
    await this.messageBus.connect();
  }

  async startConsuming(onEvent: (event: TradingEvent) => void) {
    await this.messageBus.consumeEvents(onEvent);
  }

  async close() {
    await this.messageBus.close();
  }
}

export const lrEventConsumer = new LREventConsumer();
export default lrEventConsumer;
