/**
 * Simple LR Service Message Bus
 * Handles PAYOUT_MADE events from trading service
 */

import amqp, { Channel, Connection, Message, ChannelModel } from 'amqplib';

export interface MessageBusConfig {
  host: string;
  port: number;
  username: string;
  password: string;
}

export interface TradingEvent {
  eventType: 'SWAP_COMPLETED' | 'DEPOSIT_RECEIVED' | 'PAYOUT_MADE';
  id: string;
  clientId: string;
  fromAsset: string;
  toAsset: string;
  amount: number;
  status: 'completed' | 'failed';
  stellarHash?: string;
  timestamp: string;
}

export class LRMessageBus {
  private connection: ChannelModel | null = null;
  private channel: Channel | null = null;
  private isConnected: boolean = false;

  constructor() {}

  async connect(): Promise<void> {
    const url = `amqp://${process.env.RABBITMQ_USERNAME || 'guest'}:${process.env.RABBITMQ_PASSWORD || 'guest'}@${process.env.RABBITMQ_HOST || 'localhost'}:${process.env.RABBITMQ_PORT || '5672'}`;
    
    this.connection = await amqp.connect(url);
    this.channel = await this.connection.createChannel();
    await this.channel.assertQueue('inter_service_events', { durable: true });
    
    this.isConnected = true;
    console.log('LR MessageBus connected');
  }

  async consumeEvents(callback: (event: TradingEvent) => void): Promise<void> {
    if (!this.channel || !this.isConnected) return;

    await this.channel.consume('inter_service_events', (msg: Message | null) => {
      if (msg) {
        try {
          const event = JSON.parse(msg.content.toString());
          callback(event);
          this.channel?.ack(msg);
        } catch (error) {
          console.error('Error processing event:', error);
          this.channel?.nack(msg, false, true);
        }
      }
    });
  }

  async close(): Promise<void> {
    if (this.channel) await this.channel.close();
    if (this.connection) await this.connection.close();
    this.isConnected = false;
  }
}