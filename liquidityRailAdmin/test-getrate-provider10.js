const axios = require('axios');

const LR_ADMIN_URL = 'http://localhost:8032';

async function testGetRateProvider10() {
    console.log('🧪 Testing Get Rate with Provider 10 (Trading Service)\n');
    console.log('='.repeat(60));

    try {
        // Test data for provider 10
        const testData = {
            amount: 100,
            service_code: "1000",
            currency: "UGX",
            asset_code: "USDT",
            provider_id: 10
        };

        console.log('\n📋 Test Data:');
        console.log(JSON.stringify(testData, null, 2));

        console.log('\n📡 Calling POST /accounts/getRate...');
        
        // Generate a simple JWT token for testing
        const jwt = require('jsonwebtoken');
        const token = jwt.sign(
            { user_id: 'test-user', company_id: 'test-company' },
            process.env.JWT_SECRET || 'test-secret',
            { expiresIn: '1h' }
        );
        
        const response = await axios.post(
            `${LR_ADMIN_URL}/accounts/getRate`,
            testData,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            }
        );

        console.log('\n✅ Response Status:', response.status);
        console.log('📦 Response Data:');
        console.log(JSON.stringify(response.data, null, 2));

        // Validate response structure matches other providers
        if (response.data.status === 200 && response.data.data) {
            const data = response.data.data;
            
            console.log('\n✅ TEST PASSED - Rate fetched successfully!');
            console.log('\n📊 Response Structure Validation:');
            console.log('  ✓ provider:', data.provider || 'N/A');
            console.log('  ✓ providerQuoteId:', data.id || 'N/A');
            console.log('  ✓ fiatAmount:', data.fiatAmount || 'N/A');
            console.log('  ✓ transactionAmount:', data.transactionAmount || 'N/A');
            console.log('  ✓ cryptoAmount:', data.cryptoAmount || 'N/A');
            console.log('  ✓ fee:', data.fee || 'N/A');
            console.log('  ✓ expiresAt:', data.expiresAt || 'N/A');
            console.log('  ✓ value (exchange rate):', data.value || 'N/A');
            console.log('  ✓ address (deposit):', data.address || 'N/A');
            
            // Check if response matches expected interface
            const hasRequiredFields = 
                data.hasOwnProperty('id') &&
                data.hasOwnProperty('fiatAmount') &&
                data.hasOwnProperty('fee') &&
                data.hasOwnProperty('expiresAt');
            
            if (hasRequiredFields) {
                console.log('\n✅ Response matches expected interface!');
            } else {
                console.log('\n⚠️  Warning: Some expected fields are missing');
            }
            
            return {
                success: true,
                data: data
            };
        } else {
            console.log('\n❌ TEST FAILED - Unexpected response status:', response.data.status);
            console.log('Message:', response.data.message);
            return {
                success: false,
                error: response.data.message
            };
        }

    } catch (error) {
        console.log('\n❌ TEST FAILED - Error occurred');
        
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Response:', JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
            console.log('No response received from server');
            console.log('Is the LR Admin service running on', LR_ADMIN_URL, '?');
        } else {
            console.log('Error:', error.message);
        }
        
        return {
            success: false,
            error: error.message
        };
    }
}

// Also test comparison with another provider (e.g., provider 2 - Kotani)
async function testGetRateKotani() {
    console.log('\n\n🔄 Comparing with Provider 2 (Kotani) for reference...\n');
    console.log('='.repeat(60));

    try {
        const testData = {
            amount: 100,
            currency: "UGX",
            asset_code: "USDT",
            provider_id: 2
        };

        console.log('\n📋 Test Data:');
        console.log(JSON.stringify(testData, null, 2));

        const response = await axios.post(
            `${LR_ADMIN_URL}/accounts/getRate`,
            testData,
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log('\n✅ Kotani Response Status:', response.status);
        console.log('📦 Kotani Response Data:');
        console.log(JSON.stringify(response.data, null, 2));

        return {
            success: true,
            data: response.data.data
        };

    } catch (error) {
        console.log('\n⚠️  Could not fetch Kotani rate for comparison');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Message:', error.response.data?.message);
        }
        return { success: false };
    }
}

// Run the tests
console.log('🚀 Starting LR Admin Get Rate Test');
console.log('🔗 LR Admin Service:', LR_ADMIN_URL);
console.log('='.repeat(60));

(async () => {
    const result = await testGetRateProvider10();
    
    // Optionally compare with Kotani
    // await testGetRateKotani();
    
    console.log('\n' + '='.repeat(60));
    console.log('🏁 Test Completed');
    console.log('Result:', result.success ? '✅ SUCCESS' : '❌ FAILED');
    console.log('='.repeat(60));
    process.exit(result.success ? 0 : 1);
})();

