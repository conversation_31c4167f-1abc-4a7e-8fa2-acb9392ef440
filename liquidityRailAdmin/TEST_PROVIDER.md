# MudaProvider Test Guide

Quick test for the LR Provider integration between liquidityRailAdmin and trading service.

## Prerequisites

1. **Trading service** running on `http://localhost:8039`
2. **Database** accessible for both services

## Run Test

```bash
cd aggregator/backend/liquidityRailAdmin
npx ts-node test-muda-provider.ts
```

## Expected Output

```
🧪 Testing MudaProvider Integration

1️⃣  Getting Quote...
   ✓ Quote: QUOTE_1761896051518_a3ufa17co
   ✓ Rate: 3800 | Amount: 380000 UGX

2️⃣  Creating Payment Intent...
   ✓ Reference: REF_1761896051532
   ✓ Status: 200

3️⃣  Getting Transaction...
   ✓ Status: PENDING
   ✓ Reference: REF_1761896051532

4️⃣  Refreshing Quote...
   ✓ New Rate: 3805
   ✓ Expires: 2025-10-31T08:00:00Z

5️⃣  Sending crypto_received Webhook...
   ✓ Webhook sent: OK

6️⃣  Sending fiat_sent Webhook...
   ✓ Webhook sent: OK

✅ All tests passed!

📋 Summary:
   • Quote ID: QUOTE_1761896051518_a3ufa17co
   • Reference: REF_1761896051532
   • Webhooks: 2 sent successfully
```

## What It Tests

1. **getQuote()** - Generate exchange rate quote
2. **createPaymentIntent()** - Confirm quote and create order
3. **getTransaction()** - Retrieve transaction status
4. **refreshQuote()** - Get updated rates
5. **sendWebhook()** - Send crypto_received event
6. **sendWebhook()** - Send fiat_sent event

## Troubleshooting

### "connect ECONNREFUSED"
- Trading service not running
- Check: `curl http://localhost:8039/trading/health`

### "Transaction not found"
- Database connection issue
- Check trading service DB config

### "Rate not found"
- No liquidity in orderbook
- Check USDT/UGX pair exists in exchange_rates table

